# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js
.yarn/install-state.gz

# testing
coverage
.nyc_output

# next.js
.next/
out/
build
dist

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Package manager files
pnpm-lock.yaml
package-lock.json
yarn.lock

# Cache
.cache
.eslintcache

# Generated files
CHANGELOG.md
