# Auto detect text files and perform LF normalization
* text=auto eol=lf

# These files are text and should be normalized (Convert crlf => lf)
*.js text
*.jsx text
*.ts text
*.tsx text
*.json text
*.yml text
*.yaml text
*.md text
*.mdx text
*.css text
*.html text
*.svg text

# These files are binary and should be left untouched
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.ttf binary
*.otf binary
*.eot binary
*.woff binary
*.woff2 binary
*.pdf binary
*.zip binary
*.tar binary
*.gz binary

# Documents
*.doc diff=astextplain
*.DOC diff=astextplain
*.docx diff=astextplain
*.DOCX diff=astextplain
*.pdf diff=astextplain
*.PDF diff=astextplain