# Commit Convention Guide

## Overview

We use [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages. This leads to more readable messages that are easy to follow when looking through the project history and enables automatic generation of changelogs.

## Commit Message Format

Each commit message consists of a **header**, a **body**, and a **footer**. The header has a special format that includes a **type**, a **scope**, and a **subject**:

```
<type>(<scope>): <subject>
<BLANK LINE>
<body>
<BLANK LINE>
<footer>
```

### Type

Must be one of the following:

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code (white-space, formatting, etc)
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools and libraries
- `ci`: Changes to our CI configuration files and scripts
- `build`: Changes that affect the build system or external dependencies
- `revert`: Reverts a previous commit

### Scope

The scope is optional and should be a noun describing a section of the codebase:

- `auth`
- `components`
- `api`
- `db`
- `config`
- etc.

### Subject

The subject contains a succinct description of the change:

- use the imperative, present tense: "change" not "changed" nor "changes"
- don't capitalize the first letter
- no dot (.) at the end

### Body

The body should include the motivation for the change and contrast this with previous behavior. Use the imperative, present tense.

### Footer

The footer should contain any information about **Breaking Changes** and is also the place to reference GitHub issues that this commit **Closes**.

## Examples

```
feat(auth): add email verification flow

Implement email verification process after user registration.
- Send verification email
- Add verification endpoint
- Handle email confirmation

Closes #123
```

```
fix(components): resolve button click event in Safari

Button click events were not firing consistently in Safari browsers.
Updated event listener to use proper event delegation.

Fixes #456
```

```
docs(readme): update installation instructions

- Add pnpm installation steps
- Update environment variables section
- Add troubleshooting guide
```

```
style(components): format according to new prettier config

- Adjust indentation
- Fix line endings
- Remove extra spaces
```

## Commit Tools

### Pre-commit Hook

We use Husky to enforce commit message format. The commit will fail if the message doesn't meet the conventional commit format.

### VS Code Extension

For VS Code users, we recommend installing the ["Conventional Commits" extension](https://marketplace.visualstudio.com/items?itemName=vivaxy.vscode-conventional-commits) for easier commit message formatting.

## Common Issues

### Invalid Commit Message Format

If your commit fails with a message format error, ensure:

1. The type is lowercase and matches the allowed types
2. There's a colon and space after the type/scope
3. The subject line isn't empty
4. No period at the end of the subject line

Example of fixing an invalid commit:

```bash
# Invalid ❌
git commit -m "FEAT: Add new feature."

# Valid ✅
git commit -m "feat: add new feature"
```

### Scope Issues

If you're unsure about the scope:

1. Look at previous commits for examples
2. Use a general scope that best describes the area of change
3. If in doubt, omit the scope

Example:

```bash
# With scope
git commit -m "feat(auth): add login with Google"

# Without scope
git commit -m "feat: add login with Google"
```

## Additional Resources

- [Conventional Commits](https://www.conventionalcommits.org/)
- [Angular Commit Message Guidelines](https://github.com/angular/angular/blob/master/CONTRIBUTING.md#commit)
- [Commitlint Rules](https://commitlint.js.org/#/reference-rules)
