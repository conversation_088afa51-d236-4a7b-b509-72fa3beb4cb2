# Paddle Integration for CopyElement

This implementation provides a complete Paddle payment gateway integration for your CopyElement application, handling subscriptions, one-time payments, and webhook events.

## 🚀 Quick Start

### 1. Install Dependencies

The Paddle Node.js SDK has already been installed:

```bash
pnpm add @paddle/paddle-node-sdk
```

### 2. Environment Variables

Add these to your `.env.local` file:

```bash
# Paddle Configuration
PADDLE_API_KEY=your_paddle_api_key_here
PADDLE_WEBHOOK_SECRET_KEY=your_paddle_webhook_secret_here
PADDLE_ENVIRONMENT=sandbox  # Change to 'production' for live
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

### 3. Database Setup

Update your subscription plans with Paddle price IDs:

```sql
-- Add Paddle price IDs to your subscription plans
UPDATE subscription_plans
SET metadata = jsonb_set(
  COALESCE(metadata, '{}'),
  '{paddle_price_id}',
  '"pri_your_paddle_price_id"'
)
WHERE name = 'Pro Monthly';
```

Or use the migration script:

```bash
node scripts/add-paddle-price-ids.js
```

## 🎯 Features Implemented

### ✅ Webhook Handler (`/api/webhooks/paddle`)

- **Signature validation** for security
- **Comprehensive event handling**:
  - Subscription created/updated/canceled/paused/resumed
  - Transaction completed/paid/updated/canceled
  - Customer created/updated
- **User management**:
  - Creates users if they don't exist
  - Updates pro status based on subscription state
  - Handles edge cases like missing user data

### ✅ Payment Integration

- **Updated checkout dialog** with Paddle option
- **Seamless user experience** alongside existing PayPal/PhonePe
- **Error handling** and loading states
- **Responsive design** for mobile and desktop

### ✅ Subscription Management

- **API endpoints** for subscription actions (cancel/pause/resume)
- **React components** for subscription management
- **Real-time status updates** via webhooks

### ✅ Developer Tools

- **Testing webhook script** for local development
- **Migration scripts** for database setup
- **Comprehensive documentation** and setup guides

## 📁 File Structure

```
src/
├── app/api/
│   ├── webhooks/paddle/route.ts          # Main webhook handler
│   ├── paddle/
│   │   ├── checkout/route.ts            # Checkout creation API
│   │   └── subscription/route.ts        # Subscription management API
├── lib/paddle/
│   ├── subscription-handlers.ts         # Event processing logic
│   └── paddle-client.ts                # Paddle SDK utilities
├── hooks/
│   └── use-paddle-checkout.ts          # React hook for payments
├── components/
│   ├── custom/checkout-dialog.tsx      # Updated with Paddle option
│   └── paddle/
│       ├── subscription-manager.tsx    # Plan selection component
│       └── subscription-card.tsx       # User subscription management
scripts/
├── test-paddle-webhook.js              # Webhook testing utility
└── add-paddle-price-ids.js            # Database migration script
docs/
└── PADDLE_SETUP.md                    # Detailed setup guide
```

## 🔧 Integration Steps

### 1. Paddle Dashboard Setup

1. **Create Products & Prices**:

   - Go to Paddle Dashboard > Catalog > Products
   - Create products for your subscription plans
   - Note the price IDs (e.g., `pri_01234567890abcdef`)

2. **Set Up Webhooks**:

   - Go to Developer Tools > Webhooks
   - Add webhook URL: `https://yourdomain.com/api/webhooks/paddle`
   - Select all subscription and transaction events
   - Copy the webhook secret key

3. **Get API Keys**:
   - Go to Developer Tools > Authentication
   - Create and copy your API key

### 2. Update Your Database

Run the migration script to add Paddle price IDs:

```bash
# 1. Edit scripts/add-paddle-price-ids.js with your price IDs
# 2. Run the migration
node scripts/add-paddle-price-ids.js
```

### 3. Test the Integration

1. **Local Testing**:

   ```bash
   # Test webhook locally (requires ngrok or similar)
   node scripts/test-paddle-webhook.js subscription.created
   ```

2. **Frontend Testing**:
   - Go to your pricing page
   - Select a plan with Paddle price ID configured
   - Choose "Paddle" as payment method
   - Complete the checkout flow

## 🎛️ Usage Examples

### Frontend Integration

```typescript
import { PaddleSubscriptionManager } from '@/components/paddle/subscription-manager'

function PricingPage() {
  const subscriptionPlans = [
    {
      id: 'pro-monthly',
      name: 'Pro Monthly',
      priceId: 'pri_01234567890abcdef', // Your Paddle price ID
      price: 29.99,
      currency: 'USD',
      description: 'Access to all pro features'
    }
  ]

  return (
    <PaddleSubscriptionManager
      userId={user.id}
      userEmail={user.email}
      subscriptionPlans={subscriptionPlans}
    />
  )
}
```

### User Subscription Management

```typescript
import { SubscriptionCard } from '@/components/paddle/subscription-card'

function UserProfile() {
  return (
    <SubscriptionCard
      userId={user.id}
      subscriptionId={user.subscriptionId}
    />
  )
}
```

## 🔐 Security Features

- **Webhook signature validation** using Paddle's secret key
- **User verification** for subscription operations
- **Environment-specific configurations** (sandbox/production)
- **Error handling** with proper logging
- **Database transaction safety**

## 🐛 Troubleshooting

### Common Issues

1. **Webhook not receiving events**:

   - Check firewall settings
   - Verify HTTPS configuration
   - Use ngrok for local testing

2. **Signature validation failing**:

   - Verify `PADDLE_WEBHOOK_SECRET_KEY`
   - Check webhook configuration in Paddle Dashboard

3. **User not found errors**:

   - Ensure user data is passed in Paddle custom data
   - Check user creation logic in webhook handler

4. **Price ID not found**:
   - Verify Paddle price IDs in database
   - Run migration script to update plans

### Debug Mode

Enable detailed logging:

```typescript
// In webhook handler
console.log('Webhook event:', JSON.stringify(event, null, 2))
console.log('User lookup result:', userProfile)
```

## 📈 Monitoring

Monitor these metrics:

- **Webhook delivery success rate** in Paddle Dashboard
- **User pro status updates** in your database
- **Payment completion rates**
- **Subscription lifecycle events**

## 🔄 Edge Cases Handled

- **New user registration** via webhook
- **Missing user data** graceful handling
- **Subscription state changes** (active/paused/canceled)
- **Failed payment recovery**
- **Duplicate webhook delivery** idempotency
- **Database operation failures** with retry logic

## 🚀 Production Deployment

1. Update environment variables:

   ```bash
   PADDLE_ENVIRONMENT=production
   PADDLE_API_KEY=your_production_api_key
   ```

2. Update webhook URLs to production domain

3. Test with small amounts first

4. Monitor webhook delivery and user status updates

## 📞 Support

- **Paddle Issues**: Contact Paddle support
- **Integration Issues**: Check logs and webhook delivery
- **Database Issues**: Verify Supabase permissions

---

**🎉 You're all set!** Your Paddle integration is ready to handle subscriptions, payments, and all the edge cases that come with a production payment system.
