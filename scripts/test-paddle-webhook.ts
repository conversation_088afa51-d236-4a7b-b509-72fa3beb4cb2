#!/usr/bin/env tsx

/**
 * Paddle Webhook Testing Script
 *
 * This script helps test Paddle webhook integration locally.
 * Usage: npx tsx scripts/test-paddle-webhook.ts [event-type]
 */

import crypto from 'crypto'

interface SampleEvent {
  eventType: string
  data: any
  eventId: string
  notificationId: string
  occurredAt: string
}

interface SampleEvents {
  [key: string]: SampleEvent
}

// Sample webhook events for testing
const sampleEvents: SampleEvents = {
  'subscription.created': {
    eventType: 'subscription.created',
    data: {
      id: 'sub_test_123456',
      customerId: 'ctm_test_123456',
      status: 'active',
      items: [
        {
          priceId: 'pri_test_123456',
          quantity: 1,
          price: {
            id: 'pri_test_123456',
            productId: 'pro_test_123456',
            unitPrice: {
              amount: '2999',
              currencyCode: 'USD',
            },
          },
        },
      ],
      customData: {
        userId: 'user_test_123456',
        email: '<EMAIL>',
      },
      nextBilledAt: new Date(
        Date.now() + 30 * 24 * 60 * 60 * 1000
      ).toISOString(),
    },
    eventId: 'evt_test_123456',
    notificationId: 'ntf_test_123456',
    occurredAt: new Date().toISOString(),
  },

  'subscription.canceled': {
    eventType: 'subscription.canceled',
    data: {
      id: 'sub_test_123456',
      customerId: 'ctm_test_123456',
      status: 'canceled',
      items: [
        {
          priceId: 'pri_test_123456',
          quantity: 1,
          price: {
            id: 'pri_test_123456',
            productId: 'pro_test_123456',
            unitPrice: {
              amount: '2999',
              currencyCode: 'USD',
            },
          },
        },
      ],
      customData: {
        userId: 'user_test_123456',
        email: '<EMAIL>',
      },
      canceledAt: new Date().toISOString(),
    },
    eventId: 'evt_test_789012',
    notificationId: 'ntf_test_789012',
    occurredAt: new Date().toISOString(),
  },

  'transaction.completed': {
    eventType: 'transaction.completed',
    data: {
      id: 'txn_test_123456',
      customerId: 'ctm_test_123456',
      subscriptionId: 'sub_test_123456',
      status: 'completed',
      currencyCode: 'USD',
      origin: 'subscription_recurring',
      customData: {
        userId: 'user_test_123456',
        email: '<EMAIL>',
      },
      items: [
        {
          priceId: 'pri_test_123456',
          quantity: 1,
        },
      ],
      details: {
        totals: {
          subtotal: '2999',
          discount: '0',
          tax: '0',
          total: '2999',
        },
      },
      paymentAttempt: {
        paymentMethodId: 'paymtd_test_123456',
        amount: '2999',
        status: 'captured',
        capturedAt: new Date().toISOString(),
      },
    },
    eventId: 'evt_test_345678',
    notificationId: 'ntf_test_345678',
    occurredAt: new Date().toISOString(),
  },
}

// Generate signature for testing (simplified version)
function generateSignature(body: string, secret: string): string {
  return crypto.createHmac('sha256', secret).update(body).digest('hex')
}

async function testWebhook(
  eventType: string = 'subscription.created'
): Promise<void> {
  const event = sampleEvents[eventType]

  if (!event) {
    console.error('Unknown event type. Available events:')
    console.log(Object.keys(sampleEvents))
    process.exit(1)
  }

  const webhookUrl =
    process.env.WEBHOOK_URL || 'http://localhost:3000/api/webhooks/paddle'
  const webhookSecret =
    process.env.PADDLE_WEBHOOK_SECRET_KEY || 'test_secret_key'

  const body = JSON.stringify(event)
  const signature = generateSignature(body, webhookSecret)

  console.log(`Testing webhook for event: ${eventType}`)
  console.log(`Webhook URL: ${webhookUrl}`)
  console.log(`Event data:`, JSON.stringify(event, null, 2))

  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'paddle-signature': signature,
      },
      body: body,
    })

    const responseText = await response.text()

    console.log(`Response status: ${response.status}`)
    console.log(`Response body:`, responseText)

    if (response.ok) {
      console.log('✅ Webhook test successful!')
    } else {
      console.log('❌ Webhook test failed!')
    }
  } catch (error) {
    console.error('Error testing webhook:', error)
  }
}

// Run the test
const eventType = process.argv[2] || 'subscription.created'
testWebhook(eventType)
