#!/usr/bin/env tsx

/**
 * Migration Script: Add Paddle Price IDs to Subscription Plans
 *
 * This script helps you update your subscription_plans table with Paddle price IDs.
 * Make sure to run this after setting up your products and prices in Paddle Dashboard.
 *
 * Usage: npx tsx scripts/add-paddle-price-ids.ts
 */

import { createClient } from '@supabase/supabase-js'

// Define interfaces
interface PaddlePriceMapping {
  planName: string
  paddlePriceId: string
}

interface SubscriptionPlan {
  id: string
  name: string
  metadata: Record<string, any> | null
}

// Environment variables are automatically loaded in Next.js
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY // You'll need this for admin operations
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || (!supabaseServiceKey && !supabaseAnonKey)) {
  console.error('Missing Supabase environment variables')
  console.error(
    'Required: NEXT_PUBLIC_SUPABASE_URL and either SUPABASE_SERVICE_ROLE_KEY or NEXT_PUBLIC_SUPABASE_ANON_KEY'
  )
  process.exit(1)
}

const supabase = createClient(
  supabaseUrl!,
  supabaseServiceKey || supabaseAnonKey!
)

// Define your Paddle price mappings
// Update these with your actual Paddle price IDs from your Paddle Dashboard
const paddlePriceMappings: PaddlePriceMapping[] = [
  {
    planName: 'Pro Monthly',
    paddlePriceId: 'pri_01234567890abcdef', // Replace with your actual Paddle price ID
  },
  {
    planName: 'Pro Yearly',
    paddlePriceId: 'pri_abcdef1234567890', // Replace with your actual Paddle price ID
  },
  {
    planName: 'Basic Monthly',
    paddlePriceId: 'pri_1234567890abcdef', // Replace with your actual Paddle price ID
  },
  // Add more mappings as needed
]

async function updateSubscriptionPlans(): Promise<void> {
  console.log('🚀 Starting Paddle price ID migration...')

  try {
    // First, let's see what subscription plans exist
    const { data: existingPlans, error: fetchError } = await supabase
      .from('subscription_plans')
      .select('id, name, metadata')

    if (fetchError) {
      throw fetchError
    }

    console.log('📋 Found subscription plans:')
    existingPlans?.forEach((plan: SubscriptionPlan) => {
      console.log(`  - ${plan.name} (ID: ${plan.id})`)
    })

    if (!existingPlans) {
      console.log('No subscription plans found')
      return
    }

    // Update each plan with Paddle price ID
    for (const mapping of paddlePriceMappings) {
      const plan = existingPlans.find(
        (p: SubscriptionPlan) => p.name === mapping.planName
      )

      if (!plan) {
        console.warn(
          `⚠️  Plan "${mapping.planName}" not found in database, skipping...`
        )
        continue
      }

      const updatedMetadata = {
        ...plan.metadata,
        paddle_price_id: mapping.paddlePriceId,
      }

      const { error: updateError } = await supabase
        .from('subscription_plans')
        .update({ metadata: updatedMetadata })
        .eq('id', plan.id)

      if (updateError) {
        console.error(`❌ Failed to update ${plan.name}:`, updateError)
      } else {
        console.log(
          `✅ Updated ${plan.name} with Paddle price ID: ${mapping.paddlePriceId}`
        )
      }
    }

    console.log('🎉 Migration completed!')

    // Verify the updates
    console.log('🔍 Verifying updates...')
    const { data: updatedPlans, error: verifyError } = await supabase
      .from('subscription_plans')
      .select('id, name, metadata')

    if (verifyError) {
      throw verifyError
    }

    updatedPlans?.forEach((plan: SubscriptionPlan) => {
      const paddlePriceId = plan.metadata?.paddle_price_id
      if (paddlePriceId) {
        console.log(`✅ ${plan.name}: ${paddlePriceId}`)
      } else {
        console.log(`⚠️  ${plan.name}: No Paddle price ID set`)
      }
    })
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

// Instructions for the user
function showInstructions(): void {
  console.log(`
📝 SETUP INSTRUCTIONS:

1. Update the paddlePriceMappings array above with your actual Paddle price IDs
2. Get your price IDs from Paddle Dashboard > Catalog > Prices
3. Make sure you have the correct environment variables set:
   - NEXT_PUBLIC_SUPABASE_URL
   - SUPABASE_SERVICE_ROLE_KEY (recommended) or NEXT_PUBLIC_SUPABASE_ANON_KEY

Example mapping:
{
  planName: 'Pro Monthly',
  paddlePriceId: 'pri_01hv8j2k3l4m5n6o7p8q9r0s', // Your actual Paddle price ID
}

4. Run: npx tsx scripts/add-paddle-price-ids.ts
`)
}

// Check if price mappings have been updated from defaults
const hasDefaultPriceIds = paddlePriceMappings.some((mapping) =>
  mapping.paddlePriceId.includes('01234567890abcdef')
)

if (hasDefaultPriceIds) {
  console.warn(
    '⚠️  Please update the paddlePriceMappings with your actual Paddle price IDs'
  )
  showInstructions()
  process.exit(1)
}

// Run the migration
// updateSubscriptionPlans().catch(console.error)
