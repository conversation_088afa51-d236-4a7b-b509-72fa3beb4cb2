{"name": "copyelemnt", "description": "With access to over 3500+ components you can build beautiful websites and save thousands of hours using the world&apos;s first largest Elementor component library. ", "version": "1.0.0", "private": true, "author": "<PERSON><PERSON><PERSON><PERSON> raj", "license": "MIT", "keywords": ["nextjs", "starter", "supabase", "tailwindcss", "shadcn", "typescript"], "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --pretty --noEmit", "prepare": "husky install", "test": "jest", "test:ci": "jest --ci", "analyze": "ANALYZE=true pnpm build"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@hookform/resolvers": "^3.9.0", "@next/bundle-analyzer": "^14.0.4", "@next/third-parties": "^15.1.6", "@paddle/paddle-js": "^1.4.2", "@paddle/paddle-node-sdk": "^2.8.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.39.3", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.17.15", "@tanstack/react-query-devtools": "^5.17.15", "@tanstack/react-table": "^8.19.2", "@uploadthing/react": "^7.0.2", "@vercel/analytics": "^1.1.1", "axios": "^1.6.5", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.0", "country-data-list": "^1.4.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.1.6", "framer-motion": "^11.2.14", "geist": "^1.0.0", "input-otp": "^1.2.4", "lucide-react": "^0.304.0", "mini-svg-data-uri": "^1.4.4", "motion": "^12.18.1", "next": "^14.1.0", "next-themes": "^0.2.1", "next-view-transitions": "^0.3.2", "nextjs-toploader": "^1.6.4", "posthog-js": "^1.198.0", "razorpay": "^2.9.4", "react": "18.2.0", "react-circle-flags": "^0.0.23", "react-confetti": "^6.1.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.52.1", "react-layout-masonry": "^1.2.0", "react-masonry-css": "^1.0.16", "react-plock": "^3.2.0", "react-resizable-panels": "^2.0.20", "react-slugify": "^4.0.1", "react-wrap-balancer": "^1.1.1", "react-youtube": "^10.1.0", "recharts": "^2.12.7", "resend": "^4.6.0", "schema-dts": "^1.1.5", "sonner": "^1.5.0", "tailwind-merge": "^2.2.0", "tailwind-scrollbar-hide": "^1.1.7", "undici": "^5.28.2", "uploadthing": "^7.0.2", "use-intl": "^3.25.0", "uuid": "^10.0.0", "vaul": "^0.9.1", "zod": "^3.23.8", "zod-form-data": "^2.0.2", "zustand": "^4.5.4"}, "devDependencies": {"@commitlint/cli": "^18.4.0", "@commitlint/config-conventional": "^18.4.0", "@swc/core": "^1.3.102", "@swc/jest": "^0.2.29", "@testing-library/dom": "^9.3.3", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.11", "@types/node": "20.3.1", "@types/react": "18.2.8", "@types/react-dom": "18.2.5", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^8.29.0", "autoprefixer": "10.4.15", "encoding": "^0.1.13", "eslint": "8.56.0", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-import": "^2.31.0", "husky": "^8.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "msw": "^2.1.1", "postcss": "8.4.29", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.10", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.1.3"}, "msw": {"workerDirectory": ["public"]}}