name: Pull Request Workflow

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  pull_request:
    branches:
      - main

jobs:
  pull_request_workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - uses: pnpm/action-setup@v2
        name: Install pnpm
        id: pnpm-install
        with:
          version: 8
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - uses: actions/cache@v3
        name: Setup pnpm cache
        with:
          path: $STORE_PATH
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run ESLint check
        run: pnpm lint

      - name: Run Prettier check
        run: pnpm format-check

      - name: Run type check
        run: pnpm type-check

      - name: Run tests
        run: pnpm test:ci
