const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

/** @type {import('next').NextConfig} */
const nextConfig = {
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  webpack: (config, { isServer }) => {
    // Fixes npm packages that depend on `fs` module
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      }
    }
    return config
  },
  images: {
    unoptimized: true,
    domains: [
      'shadcn-landing-page-livid.vercel.app',
      'avatars.githubusercontent.com',
      'aceternity.com',
      'www.playbook.com',
      'img.playbook.com',
      'utfs.io',
      'avatar.vercel.sh',
      'images.unsplash.com',
      'gallery.theportfolio.in',
    ],
  },
}

module.exports = withBundleAnalyzer(nextConfig)
