import { redirect } from 'next/navigation'
import React, { ReactNode } from 'react'

import { createClient } from '@/utils/supabase/server'
// import { useRouter } from 'next/navigation'

interface AuthProviderProps {
  children: ReactNode
}

const AuthProvider = async ({ children }: AuthProviderProps) => {
  const supabase = createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    return redirect('/login')
  }

  return <>{children}</>
}

export default AuthProvider
