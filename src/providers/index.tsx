'use client'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Analytics } from '@vercel/analytics/react'
import React from 'react'

import { Toaster } from '@/components/ui/sonner'

import { PHProvider } from './posthogProvider'
import ReactQueryProvider from './ReactQueryProvider'
import ThemeProvider from './ThemeProvider'

// import { SessionProvider, SessionProviderProps } from 'next-auth/react';
export default function Providers({
  // session,
  children,
}: {
  // session: SessionProviderProps['session'];
  children: React.ReactNode
}) {
  return (
    <>
      {/* <ThemeProvider attribute="class" defaultTheme="system" enableSystem> */}
      <ThemeProvider
        attribute="class"
        defaultTheme="dark"
        enableSystem
        disableTransitionOnChange
      >
        <ReactQueryProvider>
          <PHProvider>
            {/* <SessionProvider session={session}> */}
            {children}
            <Toaster position="bottom-right" richColors />
            <Analytics />
            <ReactQueryDevtools
              buttonPosition="bottom-left"
              initialIsOpen={false}
            />
            {/* </SessionProvider> */}
          </PHProvider>
        </ReactQueryProvider>
      </ThemeProvider>
    </>
  )
}
