import { unstable_cache } from 'next/cache'

import { Component } from '@/types/custom'
import { createClient } from '@/utils/supabase/client'

export const getCode = async (id: string) => {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from('components')
      .select('code')
      .eq('id', id)
      .single()
    // .maybeSingle()
    //   .range(0, 9)

    // console.log(data)
    return data?.code
  } catch (error) {}
}

export const getComponent = async (id: string) => {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from('components')
      .select('*')
      .eq('id', id)
      .single() // Assuming you want to fetch a single record

    // .maybeSingle()
    //   .range(0, 9)

    // console.log(data)
    return data
  } catch (error) {}
}

export const fetchCategoryComponents = unstable_cache(
  async (cat: string) => {
    try {
      const supabase = createClient()

      let query = supabase
        .from('components')
        .select(
          `
          id, name, description, image_url, is_pro, meta_desc, tags, slug, likes, category_id!inner(slug)
        `
        )
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      // Adjust query based on the category
      if (cat === 'free') {
        query = query.eq('is_pro', false)
      } else if (cat === 'pro') {
        query = query.eq('is_pro', true)
      } else if (cat === 'popular') {
        query = query.or(
          `tags.ilike.%${cat}%,description.ilike.%${cat}%,name.ilike.%${cat}%`
        )
      } else {
        // // Search by exact match in category_id.slug
        query = query.ilike('category_id.slug', cat)

        // Use Full-Text Search instead of multiple ilike conditions
        // query = query
        //   .textSearch('tags', cat, { type: 'websearch' })
        //   .textSearch('description', cat, { type: 'websearch' })
        //   .textSearch('name', cat, { type: 'websearch' })
        //   .textSearch('category_id.slug', cat, { type: 'websearch' })
      }

      const { data: components, error } = await query

      // console.log(components)

      return { components, error }
    } catch (error) {
      console.error('Error fetching components:', error)
      return { components: null, error }
    }
  },
  ['category-components'], // Cache key (can be dynamic based on `cat`)

  {
    tags: ['category-components'], // Tags for cache invalidation
    revalidate: 86400 * 5, // Revalidate  in every 5 days (adjust as needed)
    // revalidate: 1, // Revalidate cache every second (adjust as needed)
  }
)

export const fetchComponentWithPagination = async (
  page: number,
  limit: number
) => {
  const supabase = createClient()
  const offset = (page - 1) * limit

  try {
    const {
      data: components,
      count,
      error,
    } = await supabase
      .from('components')
      .select('*', {
        count: 'exact',
      })
      .order('created_at', { ascending: false }) // Excluding code field
      .range(offset, offset + limit - 1)

    return { components, count }
  } catch (error) {}
}

// fetching all components used in components page
export const fetchAllComponents = unstable_cache(
  async () => {
    const supabase = createClient()
    try {
      const { data: components, error } = (await supabase
        .from('components')
        .select(
          'id, name, description, image_url, tags,meta_desc , is_pro , slug '
        )
        .eq('is_active', true)
        .order('created_at', { ascending: false })) as {
        data: Component[] | null
        error: any
      }

      return { components, error }
    } catch (error) {}
  },
  ['all-components'], // add the
  {
    tags: ['components'],
    revalidate: 86400, // in seconds its equal to 24 hours
  }
)

export const searchComponents = async (query: string) => {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from('components')
      .select(
        'id, name, description, image_url, tags,meta_desc , is_pro , slug'
      )
      .or(
        `name.ilike.%${query}%,tags.ilike.%${query}%,description.ilike.%${query}%`
      )

    if (error) {
      console.error('Error searching components:', error)
      return []
    }

    // console.log(data)

    return data
  } catch (error) {
    console.error('Unexpected error:', error)
    return []
  }
}
