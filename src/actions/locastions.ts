// https://geolocation.onetrust.com/cookieconsentpub/v1/geo/location

// create a functoin that returns ppp(purchase power parity) based on the user's location and also the tax rate based on the user's location

export const fetchLocation = async () => {
  try {
    const response = await fetch(
      // 'https://geolocation.onetrust.com/cookieconsentpub/v1/geo/location',
      'https://api.omappapi.com/v3/geolocate/json',
      { cache: 'no-store' }
    )
    const data = await response.json()

    // console.log("location in fetch", data)
    // for 'AS' continent keep thee ppp as 1 and tax rate is 18% and for any other continent keep the ppp as 2 and tax rate as 10%
    // if (data.continent === 'AS') {
    if (data.country_iso === 'IN') {
      return { ppp: 1, taxRate: 18 }
    } else {
      return { ppp: 2, taxRate: 10 }
    }
  } catch (error) {
    console.error('fetch location error', error)
    return { ppp: 1, taxRate: 10 }
  }
}
