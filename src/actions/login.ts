'use server'

import { redirect } from 'next/navigation'
import { z } from 'zod'

import { createClient } from '@/utils/supabase/server'

// Validation Schema
const loginFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8, {
    message: 'Password must be at least 8 characters.',
  }),
})

export async function loginAction(formData: FormData) {
  const supabase = createClient()

  // Convert FormData to object
  const values = {
    email: formData.get('email'),
    password: formData.get('password'),
  }

  // Validate Inputs
  const result = loginFormSchema.safeParse(values)
  if (!result.success) {
    return { error: result.error.errors[0].message }
  }

  // Authenticate User
  const { error } = await supabase.auth.signInWithPassword(result.data)

  if (error) {
    return { error: error.message }
  }

  redirect('/')
}
