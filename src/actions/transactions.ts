import { createClient } from '@/utils/supabase/client'

import { checkAdminAccess } from './users'

// export async function fetchTransactionsWithPagination(
//   page: number,
//   limit: number
// ) {
//   const supabase = createClient()

//   // Verify admin access
//   await checkAdminAccess(supabase)

//   const from = (page - 1) * limit
//   const to = from + limit - 1
//   const { data, count, error } = await supabase
//     .from('transactions')
//     .select('*', { count: 'exact' })
//     .order('created_at', { ascending: false })
//     .range(from, to)
//   if (error) throw error
//   return { transactions: data, count }
// }

export const fetchTransactionsWithPagination = async (
  page: number,
  limit: number
) => {
  const supabase = createClient()
  const offset = (page - 1) * limit

  try {
    // Verify admin access
    await checkAdminAccess(supabase)

    const {
      data: transactions,
      count,
      error,
    } = await supabase
      .from('transactions')
      .select('*, profiles(email, first_name, last_name)', { count: 'exact' })
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false })

    if (error) throw error

    return {
      transactions: transactions?.map((order) => ({
        ...order,
        user_email: order.profiles?.email,
        user_name: `${order.profiles?.first_name} ${order.profiles?.last_name}`,
      })),
      count,
    }
  } catch (error) {
    console.error('Error fetching orders:', error)
    throw error
  }
}
