import { createClient } from '@/utils/supabase/client'

export const getProfile = async (id: string) => {
  const supabase = createClient()
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()
    if (error) throw error
    return data
  } catch (error) {
    throw error
  }
}

export const updateProfile = async (id: string, data: any) => {
  const supabase = createClient()
  try {
    const { data: user, error } = await supabase
      .from('profiles')
      .upsert({ id, ...data })
    if (error) throw error
    return user
  } catch (error) {
    throw error
  }
}

export const fetchProfilesWithPagination = async (
  page: number,
  limit: number
) => {
  const supabase = createClient()
  const offset = (page - 1) * limit

  try {
    const {
      data: profiles,
      count,
      error,
    } = await supabase
      .from('profiles')
      .select('*', {
        count: 'exact',
      })
      .range(offset, offset + limit - 1)

    if (error) throw error
    return { profiles, count }
  } catch (error) {
    throw error
  }
}
