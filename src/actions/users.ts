import { createClient } from '@/utils/supabase/client'

export type Role = 'admin' | 'editor' | 'user'

export const allowedRoles: Role[] = ['admin', 'editor']

/**
 * Check if a user has permission to access a resource
 */
export async function checkUserPermission(
  userId: string,
  requiredRoles: Role[] = ['admin']
): Promise<boolean> {
  const supabase = createClient()

  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .single()

    if (error || !profile) {
      return false
    }

    return requiredRoles.includes(profile.role as Role)
  } catch (error) {
    console.error('Error checking user permission:', error)
    return false
  }
}

/**
 * Middleware-like function to verify user permissions
 */
export async function verifyPermission(
  requiredRoles: Role[] = ['admin']
): Promise<{ userId: string | null; hasPermission: boolean }> {
  const supabase = createClient()

  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser()

    if (error || !user) {
      return { userId: null, hasPermission: false }
    }

    const hasPermission = await checkUserPermission(user.id, requiredRoles)

    return { userId: user.id, hasPermission }
  } catch (error) {
    console.error('Error verifying permission:', error)
    return { userId: null, hasPermission: false }
  }
}

export const fetchUsersWithPagination = async (page: number, limit: number) => {
  const supabase = createClient()
  const offset = (page - 1) * limit

  try {
    const {
      data: users,
      count,
      error,
    } = await supabase
      .from('profiles')
      .select('*', {
        count: 'exact',
      })
      .range(offset, offset + limit - 1)

    return { users, count }
  } catch (error) {}
}

// export const getUser = async (id: string) => {
//   const supabase = createClient()

//   try {
//     let { data, error } = await supabase
//       .from('profiles')
//       .select('*')
//       .eq('id', id)
//       .single()

//     return data
//   } catch (error) {}
// }

export const getProfile = async (id: string) => {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Profile fetch error:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Profile error:', error)
    throw error
  }
}

// export const createUser = async (data: any) => {
//   const supabase = createClient()

//   try {
//     let { data: user, error } = await supabase.from('profiles').insert(data)

//     return user
//   } catch (error) {}
// }

export const updateUser = async (id: string, data: any) => {
  const supabase = createClient()

  try {
    const { data: user, error } = await supabase
      .from('profiles')
      .upsert({ id, ...data })

    return user
  } catch (error) {}
}

//
export const checkAdminAccess = async (supabase: any) => {
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()
  if (userError || !user) {
    throw new Error('Unauthorized')
  }

  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()

  if (profileError || !profile || profile.role !== 'admin') {
    throw new Error('Unauthorized: Admin access required')
  }

  return user
}
