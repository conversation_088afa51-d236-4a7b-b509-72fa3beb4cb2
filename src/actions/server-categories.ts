'use server'
import { createClient } from '@/utils/supabase/client'

// Fetch categories from Supabase
export const fetchCategories = async (isTemplate?: boolean) => {
  try {
    const supabase = createClient()

    let query = supabase.from('categories').select('*').eq('is_active', true)

    // Filter based on template/component type
    if (isTemplate === true) {
      // For templates: only get categories that are template-only
      query = query.eq('is_template_only', true)
    } else if (isTemplate === false) {
      // For components: only get categories that are component-only
      query = query.eq('is_component_only', true)
    }
    // If isTemplate is undefined, fetch all categories (no filtering)

    const { data: categories, error } = await query.order('sequence', {
      ascending: true,
    }) // Sorting by sequence order

    if (error) throw error
    return categories || []
  } catch (error) {
    console.error('Error fetching categories:', error)
    return []
  }
}

// Fetch category by slug
export const fetchCategoryBySlug = async (slug: string) => {
  try {
    const supabase = createClient()

    const { data: category, error } = await supabase
      .from('categories')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single()

    if (error) throw error
    return category
  } catch (error) {
    console.error('Error fetching category by slug:', error)
    return null
  }
}
