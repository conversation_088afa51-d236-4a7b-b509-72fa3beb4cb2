import { createClient } from '@/utils/supabase/server'

export async function getProfileServer(id: string) {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Profile fetch error:', error)
      throw error
    }

    return data
  } catch (error) {
    console.error('Profile error:', error)
    throw error
  }
}

export async function getCurrentUser() {
  const supabase = createClient()

  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser()

    if (error || !user) {
      return { user: null, profile: null }
    }

    // Get the user's profile
    try {
      const profile = await getProfileServer(user.id)
      return { user, profile }
    } catch (profileError) {
      // If profile fetch fails, still return the user
      return { user, profile: null }
    }
  } catch (error) {
    console.error('Error getting current user:', error)
    return { user: null, profile: null }
  }
}
