import { Profile } from '@/types/custom'
import { createClient } from '@/utils/supabase/client'

export async function toggleLike(componentId: string) {
  const supabase = createClient()

  try {
    // Get current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return { error: 'User not authenticated' }
    }

    // Get user's profile with current liked components
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('liked_components')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return { error: 'Failed to fetch user profile' }
    }

    const currentLikedComponents = profile.liked_components || []
    const isCurrentlyLiked = currentLikedComponents.includes(componentId)

    let updatedLikedComponents: string[]
    let likesChange: number

    if (isCurrentlyLiked) {
      // Unlike: remove from array
      updatedLikedComponents = currentLikedComponents.filter(
        (id: string) => id !== componentId
      )
      likesChange = -1
    } else {
      // Like: add to array
      updatedLikedComponents = [...currentLikedComponents, componentId]
      likesChange = 1
    }

    // Update user's liked components
    const { error: updateProfileError } = await supabase
      .from('profiles')
      .update({ liked_components: updatedLikedComponents })
      .eq('id', user.id)

    if (updateProfileError) {
      return { error: 'Failed to update liked components' }
    }

    // Update component's like count
    const { data: component, error: componentError } = await supabase
      .from('components')
      .select('likes')
      .eq('id', componentId)
      .single()

    if (componentError) {
      return { error: 'Failed to fetch component' }
    }

    const currentLikes = component.likes || 0
    const newLikesCount = Math.max(0, currentLikes + likesChange)

    const { error: updateComponentError } = await supabase
      .from('components')
      .update({ likes: newLikesCount })
      .eq('id', componentId)

    if (updateComponentError) {
      return { error: 'Failed to update component likes' }
    }

    // Revalidate relevant paths
    // revalidatePath('/components')
    // revalidatePath('/templates')
    // revalidatePath('/profile')

    return {
      success: true,
      isLiked: !isCurrentlyLiked,
      newLikesCount,
    }
  } catch (error) {
    console.error('Error toggling like:', error)
    return { error: 'An unexpected error occurred' }
  }
}

//get user liked components
// Ensure you have your Supabase client creation logic
// e.g., import { createClient } from '@/utils/supabase/client'; // or server
//
// Define your Profile type if you haven't
// interface Profile {
//   liked_components?: (string | number)[]; // Array of strings (UUIDs) or numbers
//   // ... other properties
// }

export async function getUserLikedComponents(profile: Profile) {
  const supabase = createClient() // Make sure this initializes your Supabase client correctly

  try {
    const rawLikedComponentIds = profile.liked_components

    // Log the initial state of liked_components for debugging
    console.log(
      '[getUserLikedComponents] Raw profile.liked_components:',
      rawLikedComponentIds
    )

    // Handle cases where liked_components is not an array or is empty
    if (
      !Array.isArray(rawLikedComponentIds) ||
      rawLikedComponentIds.length === 0
    ) {
      console.log(
        '[getUserLikedComponents] No liked component IDs found or input is not a valid array.'
      )
      return []
    }

    // Clean and validate the component IDs
    // This ensures IDs are strings (common for UUIDs) and filters out any empty/nullish values.
    // Adjust String(id) to Number(id) if your component IDs are numeric.
    const componentIds = rawLikedComponentIds
      .map((id) => (id !== null && id !== undefined ? String(id).trim() : null))
      .filter((id) => id && id.length > 0) as string[] // Cast to string[] if you expect strings

    // Log the processed componentIds that will be used in the query
    console.log(
      '[getUserLikedComponents] Processed componentIds for query:',
      componentIds
    )

    if (componentIds.length === 0) {
      console.log(
        '[getUserLikedComponents] After processing, no valid component IDs to query.'
      )
      return []
    }

    const { data: components, error: componentsError } = await supabase
      .from('components')
      .select(
        `
          id,
          name,
          description,
          image_url,
          is_pro,
          meta_desc,
          tags,
          slug,
          likes,
          category:category_id (name, slug)
        `
      )
      .in('id', componentIds) // 'id' is the column in 'components' table to match against

    if (componentsError) {
      console.error(
        '[getUserLikedComponents] Error fetching components from Supabase:',
        componentsError
      )
      return {
        error: `Failed to fetch liked components: ${componentsError.message}`,
      }
    }

    console.log(
      '[getUserLikedComponents] Successfully fetched components:',
      components
    )

    return components || [] // Ensure it returns an array even if components is null
  } catch (error) {
    console.error('[getUserLikedComponents] Unexpected error:', error)
    const errorMessage =
      error instanceof Error ? error.message : 'An unknown error occurred'
    return { error: `An unexpected error occurred: ${errorMessage}` }
  }
}
