'use server'
import { unstable_cache } from 'next/cache'

import { Component } from '@/types/custom'
import { createClient } from '@/utils/supabase/client'

// fetching all components used in components page
export const fetchAllComponents = async () => {
  const supabase = createClient()
  try {
    const { data: components, error } = (await supabase
      .from('components')
      .select(
        `
        id, name, description, image_url,
        tags, meta_desc, is_pro, slug, likes,
        category:category_id(name, slug)
        `
      )
      .eq('is_active', true)
      .not('tags', 'ilike', '%wireframe%') // Exclude components with wireframe tag
      .order('created_at', { ascending: false })) as {
      data:
        | (Component & {
            category: {
              id: string
              name: string
              slug: string
            } | null
          })[]
        | null
      error: any
    }

    return { components, error }
  } catch (error) {
    console.error('Error fetching components:', error)
    return { components: null, error }
  }
}

export const fetchCategoryComponents = async (cat: string) => {
  try {
    const supabase = createClient()
    let query = supabase
      .from('components')
      .select(
        `
          id, name, description, image_url, is_pro, meta_desc, tags, slug, likes,
          category:category_id!inner(name, slug)
        `
      )
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    // Adjust query based on the category
    if (cat === 'free') {
      query = query.eq('is_pro', false)
    } else if (cat === 'pro') {
      query = query.eq('is_pro', true)
    } else if (cat === 'popular') {
      // query = query.or(
      //   `tags.ilike.%${cat}%,description.ilike.%${cat}%,name.ilike.%${cat}%`
      // )
      query = query.gte('likes', 1000).order('likes', { ascending: false })
    } else if (cat === 'wireframes') {
      // For wireframes, search for 'wireframe' tag in the comma-separated tags string
      query = query.ilike('tags', '%wireframe%')
    } else {
      query = query.eq('category.slug', cat)
    }

    const { data: components, error } = await query

    return { components, error }
  } catch (error) {
    console.error('Error fetching components:', error)
    return { components: null, error }
  }
}

export const getCode = unstable_cache(
  async (id: string) => {
    const supabase = createClient()

    try {
      const { data, error } = await supabase
        .from('components')
        .select('code')
        .eq('id', id)
        .single()

      if (error) {
        console.error('Error fetching code:', error)
        return null
      }

      return data?.code
    } catch (error) {
      console.error('Unexpected error:', error)
      return null
    }
  },
  [`component-code`], // Include id in the cache key
  {
    tags: [`component-code`], // Add both general and specific tags
    revalidate: 86400, // Cache for 1 day
  }
)

export const fetchComponentBySlug = async (slug: string) => {
  const supabase = createClient()
  try {
    const { data: component, error } = (await supabase
      .from('components')
      .select(
        `id, name, description, image_url, category_id, is_pro, meta_desc, tags, slug, likes`
      )
      .eq('slug', slug)
      .single()) as {
      data: Component | null
      error: any
    }

    return { component, error }
  } catch (error) {
    console.error('Error fetching component:', error)
    return { component: null, error }
  }
}

export const fetchComponentMetadata = async (slug: string) => {
  const supabase = createClient()
  try {
    const { data: component, error } = await supabase
      .from('components')
      .select('name, description, meta_desc, image_url')
      .eq('slug', slug)
      .single()

    return { component, error }
  } catch (error) {
    console.error('Error fetching component metadata:', error)
    return { component: null, error }
  }
}
