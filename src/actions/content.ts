import { notifyIndexNow } from '@/utils/indexnow'

export async function updateContent(data: any) {
  try {
    // Your existing content update logic here

    // Notify search engines about the change
    await notifyIndexNow([
      `https://copyelement.com/${data.slug}`,
      // Add any other related URLs that were affected
    ])

    return { success: true }
  } catch (error) {
    console.error('Failed to update content:', error)
    return { success: false, error }
  }
}
