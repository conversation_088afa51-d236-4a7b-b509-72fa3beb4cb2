import { createClient } from '@/utils/supabase/client'

import { checkAdminAccess } from './users'

export const fetchPaymentsWithPagination = async (
  page: number,
  limit: number
) => {
  const supabase = createClient()
  const offset = (page - 1) * limit

  try {
    // Verify admin access
    await checkAdminAccess(supabase)

    const {
      data: payments,
      count,
      error,
    } = await supabase
      .from('payments')
      .select('*, orders!inner(*)', { count: 'exact' })
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false })

    if (error) throw error

    return {
      payments: payments?.map((payment) => ({
        ...payment,
        amount: payment.orders?.amount,
        currency: payment.orders?.currency,
      })),
      count,
    }
  } catch (error) {
    console.error('Error fetching payments:', error)
    throw error
  }
}

export const getPayment = async (paymentId: string) => {
  const supabase = createClient()

  try {
    // Verify admin access
    await checkAdminAccess(supabase)

    const { data, error } = await supabase
      .from('payments')
      .select('*, orders!inner(*)')
      .eq('id', paymentId)
      .single()

    if (error) throw error

    return {
      ...data,
      amount: data.orders?.amount,
      currency: data.orders?.currency,
    }
  } catch (error) {
    console.error('Error fetching payment:', error)
    throw error
  }
}
