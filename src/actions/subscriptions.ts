'use server'

import { cache } from 'react'

import { createClient } from '@/utils/supabase/server'

export const fetchSubscriptionPlans = cache(async () => {
  const supabase = createClient()
  try {
    const { data, error } = await supabase
      .from('subscription_plans')
      .select(`*`)
      .eq('is_active', true)
      .order('sequence', { ascending: true })

    // console.log(data)
    return data
  } catch (error) {}
})
// ['subscription_plans'], // add the
// {
//   tags: ['subscription_plans'],
//   revalidate: 60 * 60 * 1, // in seconds its equal to 1 hours
// },

export const fetchSubscriptionPlan = async (palnId: string) => {
  const supabase = createClient()
  try {
    const { data, error } = await supabase
      .from('subscription_plans')
      .select(
        `id ,name ,plan_type ,plan_image ,description ,unit_amount ,currency`
      )
      .eq('id', palnId)
      .single()

    // console.log(data)
    return data
  } catch (error) {}
}
