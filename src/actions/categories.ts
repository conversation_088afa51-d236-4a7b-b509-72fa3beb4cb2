import { cache } from 'react'

import { createClient } from '@/utils/supabase/client'

export const fetchCategoriesCached = cache(
  async () => {
    const supabase = createClient()

    try {
      const { data: categories, error } = await supabase
        .from('categories')
        .select('*')
        .eq('is_active', true)
        .order('sequence', { ascending: true }) // Excluding code field
      //   .range(0, 9)

      // console.log('categories', categories)
      return categories
    } catch (error) {}
  }
  // ['active-categories'], // add the
  // {
  //   tags: ['active-categories'],
  //   revalidate: 60 * 60 * 12, // 12 hours
  // },
)

export const fetchCategories = async () => {
  const supabase = createClient()

  try {
    const { data: categories, error } = await supabase
      .from('categories')
      .select('*')
      .order('sequence', { ascending: true }) // Excluding code field
    //   .range(0, 9)

    return categories
  } catch (error) {}
}

export const fetchCategoriesWithPagination = async (
  page: number,
  limit: number
) => {
  const supabase = createClient()
  const offset = (page - 1) * limit

  try {
    const {
      data: categories,
      count,
      error,
    } = await supabase
      .from('categories')
      .select('*', {
        count: 'exact',
      })
      .order('sequence', { ascending: true })
      .range(offset, offset + limit - 1)

    if (error) throw error

    return { categories, count }
  } catch (error) {
    console.error('Error fetching categories:', error)
    return { categories: [], count: 0 }
  }
}

export const getCategory = async (id: string) => {
  const supabase = createClient()

  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single()

    return data
  } catch (error) {}
}
