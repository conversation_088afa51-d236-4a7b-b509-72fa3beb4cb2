import { createClient } from '@/utils/supabase/client'

import { checkAdminAccess } from './users'

export const fetchOrdersWithPagination = async (
  page: number,
  limit: number
) => {
  const supabase = createClient()
  const offset = (page - 1) * limit

  try {
    // Verify admin access
    await checkAdminAccess(supabase)

    const {
      data: orders,
      count,
      error,
    } = await supabase
      .from('orders')
      .select('*, profiles(email, first_name, last_name)', { count: 'exact' })
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false })

    if (error) throw error

    return {
      orders: orders?.map((order) => ({
        ...order,
        user_email: order.profiles?.email,
        user_name: `${order.profiles?.first_name} ${order.profiles?.last_name}`,
      })),
      count,
    }
  } catch (error) {
    console.error('Error fetching orders:', error)
    throw error
  }
}

export const fetchOrdersWithPaginationComplete = async (
  page: number,
  limit: number
) => {
  const supabase = createClient()
  const offset = (page - 1) * limit

  try {
    // Verify admin access
    await checkAdminAccess(supabase)

    const {
      data: orders,
      count,
      error,
    } = await supabase
      .from('orders')
      .select(
        `
        *,
        profiles(id, email, first_name, last_name, username, avatar_url),
        subscription_plans(id, name, plan_type, description),
        coupons(id, code, discount_percentage, discount_amount),
        payments(*)
      `,
        { count: 'exact' }
      )
      .range(offset, offset + limit - 1)
      .order('created_at', { ascending: false })

    if (error) throw error

    return {
      orders: orders?.map((order) => ({
        ...order,
        // User information
        user_email: order.profiles?.email,
        user_name: `${order.profiles?.first_name || ''} ${
          order.profiles?.last_name || ''
        }`.trim(),
        user_id: order.profiles?.id,
        username: order.profiles?.username,
        user_avatar: order.profiles?.avatar_url,

        // Subscription plan information
        plan_name: order.subscription_plans?.name,
        plan_type: order.subscription_plans?.plan_type,
        plan_description: order.subscription_plans?.description,

        // Coupon information
        coupon_code: order.coupons?.code,
        discount_percentage: order.coupons?.discount_percentage,
        discount_amount: order.coupons?.discount_amount,

        // Payment information
        payment_status: order.payments?.payment_status || order.status || 'N/A',
        payment_method: order.payments?.payment_method || order.payment_option,
        transaction_id:
          order.payments?.transaction_id || order.payment_id || 'N/A',
      })),
      count,
    }
  } catch (error) {
    console.error('Error fetching orders with complete data:', error)
    throw error
  }
}

export const getOrder = async (orderId: string) => {
  const supabase = createClient()

  try {
    // Verify admin access
    await checkAdminAccess(supabase)

    const { data, error } = await supabase
      .from('orders')
      .select('*, profiles(email, first_name, last_name)')
      .eq('id', orderId)
      .single()

    if (error) throw error

    return {
      ...data,
      user_email: data.profiles?.email,
      user_name: `${data.profiles?.first_name} ${data.profiles?.last_name}`,
    }
  } catch (error) {
    console.error('Error fetching order:', error)
    throw error
  }
}
