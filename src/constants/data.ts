import { SideLink } from '@/types'

export type User = {
  id: number
  title: string
  company: string
  role: string
  verified: boolean
  status: string
}
export const users: User[] = [
  {
    id: 1,
    title: 'Candice Schiner',
    company: '<PERSON>',
    role: 'Frontend Developer',
    verified: false,
    status: 'Active',
  },
  {
    id: 2,
    title: '<PERSON>',
    company: '<PERSON><PERSON>or<PERSON>',
    role: 'Backend Developer',
    verified: true,
    status: 'Active',
  },
  {
    id: 3,
    title: '<PERSON>',
    company: 'WebTech',
    role: 'UI Designer',
    verified: true,
    status: 'Active',
  },
  {
    id: 4,
    title: '<PERSON>',
    company: 'Innovate Inc.',
    role: 'Fullstack Developer',
    verified: false,
    status: 'Inactive',
  },
  {
    id: 5,
    title: '<PERSON>',
    company: 'TechGuru',
    role: 'Product Manager',
    verified: true,
    status: 'Active',
  },
  {
    id: 6,
    title: '<PERSON>',
    company: 'CodeGenius',
    role: 'QA Engineer',
    verified: false,
    status: 'Active',
  },
  {
    id: 7,
    title: '<PERSON>',
    company: 'SoftWorks',
    role: 'UX Designer',
    verified: true,
    status: 'Active',
  },
  {
    id: 8,
    title: '<PERSON>',
    company: '<PERSON><PERSON><PERSON>',
    role: 'DevOps Engineer',
    verified: false,
    status: 'Active',
  },
  {
    id: 9,
    title: '<PERSON>',
    company: 'WebSolutions',
    role: 'Frontend Developer',
    verified: true,
    status: 'Active',
  },
  {
    id: 10,
    title: 'Robert Taylor',
    company: 'DataTech',
    role: 'Data Analyst',
    verified: false,
    status: 'Active',
  },
]

export type Employee = {
  id: number
  first_title: string
  last_title: string
  email: string
  phone: string
  gender: string
  date_of_birth: string // Consider using a proper date type if possible
  street: string
  city: string
  state: string
  country: string
  zipcode: string
  longitude?: number // Optional field
  latitude?: number // Optional field
  job: string
  profile_picture?: string | null // Profile picture can be a string (URL) or null (if no picture)
}

export const dashboardNavItems: SideLink[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: 'dashboard',
    isAdmin: true,
    // label: 'Dashboard',
  },
  {
    title: 'Users',
    href: '/dashboard/users',
    icon: 'user',
    isAdmin: true,
    // label: 'user',
  },
  {
    title: 'Categories',
    href: '/dashboard/categories',
    icon: 'post',
    isAdmin: true,

    // label: 'categories',
  },
  {
    title: 'Components',
    href: '/dashboard/components',
    icon: 'post',
    // label: 'component',
  },
  {
    title: 'Templates',
    href: '/dashboard/templates',
    icon: 'post',
    // label: 'component',
  },
  {
    title: 'Transactions',
    href: '/dashboard/transactions',
    icon: 'post',
    isAdmin: true,
  },
  {
    title: 'Orders',
    href: '/dashboard/orders',
    icon: 'post',
    isAdmin: true,
  },
  {
    title: 'Payments',
    href: '/dashboard/payments',
    icon: 'post',
    isAdmin: true,
  },
  {
    title: 'Revalidate',
    href: '/dashboard/revalidate',
    icon: 'post',
    isAdmin: true,
  },

  {
    title: 'Profile',
    href: '/dashboard/profile',
    icon: 'profile',
    // label: 'profile',
    isAdmin: true,
  },
]
export const componentNavItems: SideLink[] = [
  {
    title: 'Hero',
    href: '/components/hero',
    icon: 'dashboard',
    // label: 'Hero',
  },
  {
    title: 'Footer',
    href: '/components/footer',
    icon: 'dashboard',
    // label: 'Footer',
  },
  {
    title: 'Pricing',
    href: '/components/pricing',
    icon: 'dashboard',
    // label: 'Pricing',
  },
  {
    title: 'Testimonial',
    href: '/components/testimonial',
    icon: 'dashboard',
    // label: 'Testimonial',
  },
  {
    title: 'Dropdown',
    href: '/components/header',
    icon: 'dashboard',
    // label: 'Header',
    sub: [
      {
        title: 'Card',
        href: '/components/card',
        icon: 'dashboard',
        // label: 'Card',
      },
      {
        title: 'Button',
        href: '/components/button',
        icon: 'dashboard',
        // label: 'Button',
      },
      {
        title: 'Form',
        href: '/components/form',
        icon: 'dashboard',
        // label: 'Form',
      },
      {
        title: 'Modal',
        href: '/components/modal',
        icon: 'dashboard',
        // label: 'Modal',
      },
      {
        title: 'Navbar',
        href: '/components/navbar',
        icon: 'dashboard',
        // label: 'Navbar',
      },
    ],
  },
  {
    title: 'Card',
    href: '/components/card',
    icon: 'dashboard',
    // label: 'Card',
  },
  {
    title: 'Button',
    href: '/components/button',
    icon: 'dashboard',
    // label: 'Button',
  },
  {
    title: 'Form',
    href: '/components/form',
    icon: 'dashboard',
    // label: 'Form',
  },
  {
    title: 'Modal',
    href: '/components/modal',
    icon: 'dashboard',
    // label: 'Modal',
  },
  {
    title: 'Navbar',
    href: '/components/navbar',
    icon: 'dashboard',
    // label: 'Navbar',
  },
  {
    title: 'Hero',
    href: '/components/hero',
    icon: 'dashboard',
    // label: 'Hero',
  },
  {
    title: 'Footer',
    href: '/components/footer',
    icon: 'dashboard',
    // label: 'Footer',
  },
  {
    title: 'Pricing',
    href: '/components/pricing',
    icon: 'dashboard',
    // label: 'Pricing',
  },
  {
    title: 'Testimonial',
    href: '/components/testimonial',
    icon: 'dashboard',
    // label: 'Testimonial',
  },
  {
    title: 'Header',
    href: '/components/header',
    icon: 'dashboard',
    // label: 'Header',
    sub: [
      {
        title: 'Card',
        href: '/components/card',
        icon: 'dashboard',
        // label: 'Card',
      },
      {
        title: 'Button',
        href: '/components/button',
        icon: 'dashboard',
        // label: 'Button',
      },
      {
        title: 'Form',
        href: '/components/form',
        icon: 'dashboard',
        // label: 'Form',
      },
      {
        title: 'Modal',
        href: '/components/modal',
        icon: 'dashboard',
        // label: 'Modal',
      },
      {
        title: 'Navbar',
        href: '/components/navbar',
        icon: 'dashboard',
        // label: 'Navbar',
      },
    ],
  },
  {
    title: 'Card',
    href: '/components/card',
    icon: 'dashboard',
    // label: 'Card',
  },
  {
    title: 'Button',
    href: '/components/button',
    icon: 'dashboard',
    // label: 'Button',
  },
  {
    title: 'Form',
    href: '/components/form',
    icon: 'dashboard',
    // label: 'Form',
  },
  {
    title: 'Modal',
    href: '/components/modal',
    icon: 'dashboard',
    // label: 'Modal',
  },
  {
    title: 'Navbar',
    href: '/components/navbar',
    icon: 'dashboard',
    // label: 'Navbar',
  },
  {
    title: 'Hero',
    href: '/components/hero',
    icon: 'dashboard',
    // label: 'Hero',
  },
  {
    title: 'Footer',
    href: '/components/footer',
    icon: 'dashboard',
    // label: 'Footer',
  },
  {
    title: 'Pricing',
    href: '/components/pricing',
    icon: 'dashboard',
    // label: 'Pricing',
  },
  {
    title: 'Testimonial',
    href: '/components/testimonial',
    icon: 'dashboard',
    // label: 'Testimonial',
  },
  {
    title: 'Header',
    href: '/components/header',
    icon: 'dashboard',
    // label: 'Header',
    sub: [
      {
        title: 'Card',
        href: '/components/card',
        icon: 'dashboard',
        // label: 'Card',
      },
      {
        title: 'Button',
        href: '/components/button',
        icon: 'dashboard',
        // label: 'Button',
      },
      {
        title: 'Form',
        href: '/components/form',
        icon: 'dashboard',
        // label: 'Form',
      },
      {
        title: 'Modal',
        href: '/components/modal',
        icon: 'dashboard',
        // label: 'Modal',
      },
      {
        title: 'Navbar',
        href: '/components/navbar',
        icon: 'dashboard',
        // label: 'Navbar',
      },
    ],
  },
  {
    title: 'Card',
    href: '/components/card',
    icon: 'dashboard',
    // label: 'Card',
  },
  {
    title: 'Button',
    href: '/components/button',
    icon: 'dashboard',
    // label: 'Button',
  },
  {
    title: 'Form',
    href: '/components/form',
    icon: 'dashboard',
    // label: 'Form',
  },
  {
    title: 'Modal',
    href: '/components/modal',
    icon: 'dashboard',
    // label: 'Modal',
  },
  {
    title: 'Navbar',
    href: '/components/navbar',
    icon: 'dashboard',
    // label: 'Navbar',
  },
]

type Country = {
  name: string
  value: string
}
export const countries: Country[] = [
  {
    name: 'India',
    value: 'IN',
  },
  {
    name: 'United Kingdom',
    value: 'UK',
  },
  {
    name: 'United States',
    value: 'US',
  },
  {
    name: 'Australia',
    value: 'AU',
  },
  {
    name: 'Brazil',
    value: 'BR',
  },
  {
    name: 'Canada',
    value: 'CA',
  },
  {
    name: 'China',
    value: 'CN',
  },
  {
    name: 'France',
    value: 'FR',
  },
  {
    name: 'Germany',
    value: 'DE',
  },

  {
    name: 'Japan',
    value: 'JP',
  },
  {
    name: 'South Africa',
    value: 'ZA',
  },
]
