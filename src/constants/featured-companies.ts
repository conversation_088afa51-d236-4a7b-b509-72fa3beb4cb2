export interface FeaturedCompany {
  name: string
  url: string
  image: string
}

export const featuredCompanies: FeaturedCompany[] = [
  {
    name: 'Capterra',
    url: 'https://www.capterra.in/software/1075558/CopyElement',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093502829-24.png',
  },
  {
    name: 'Dealify',
    url: 'https://dealify.com/products/copyelement',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093497259-22.png',
  },
  {
    name: 'ProductCanyon',
    url: 'https://productcanyon.com/product/copyelement-elementor-components/',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093496589-23.png',
  },

  {
    name: 'Product Hunt',
    url: 'https://www.producthunt.com/products/copy-element/reviews',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093491255-1.png',
  },
  {
    name: 'F6S',
    url: 'https://www.f6s.com/software/copyelement',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093493226-2.png',
  },
  {
    name: 'DealMirror',
    url: 'https://dealmirror.com/product/copyelement/',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093502120-3.png',
  },
  {
    name: 'G2',
    url: 'https://www.g2.com/products/copyelement/reviews',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093493895-4.png',
  },
  {
    name: 'Software Advice',
    url: 'https://www.softwareadvice.com/product/527735-CopyElement/',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093494601-5.png',
  },
  {
    name: 'Webriy',
    url: 'https://webriy.com/portfolio/copy-element/',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093495258-6.png',
  },
  {
    name: 'StackSocial',
    url: 'https://www.stacksocial.com/sales/copyelement-lifetime-plan-subscription',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093497932-7.png',
  },
  {
    name: 'Slashdot',
    url: 'https://slashdot.org/software/p/CopyElement/',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093495944-8.png',
  },
  {
    name: 'GetApp',
    url: 'https://www.getapp.com/website-ecommerce-software/a/copyelement/',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093506452-9.png',
  },
  {
    name: 'Trustpilot',
    url: 'https://www.trustpilot.com/review/copyelement.com',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093498645-10.png',
  },
  {
    name: 'Techjockey',
    url: 'https://www.techjockey.com/detail/copyelement',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093499382-12.png',
  },
  {
    name: 'PCMag',
    url: 'https://www.pcmag.com/deals/speed-up-site-builds-with-1500-plus-copy-paste-blocks-now-just-7999',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093501459-11.png',
  },
  {
    name: 'Financial Post',
    url: 'https://financialpost.com/personal-finance/business-essentials/copyelement-web-builder',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093500782-13.png',
  },
  {
    name: 'XDA Depot',
    url: 'https://depot.xda-developers.com/sales/copyelement-lifetime-plan-subscription',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093500102-14.png',
  },
  {
    name: 'SaaSpirate',
    url: 'https://saaspirate.com/deals/copyelement/',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093505716-15.png',
  },
  {
    name: 'Yahoo',
    url: 'https://tech.yahoo.com/apps/deals/articles/speed-builds-1-500-copy-210000602.html',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093507908-16.png',
  },
  {
    name: 'DealFuel',
    url: 'https://www.dealfuel.com/seller/copyelement-elementor-elements-kit/',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093507138-17.png',
  },
  {
    name: 'TopTool',
    url: 'https://www.toptool.app/en/product/copyelements',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093508710-18.png',
  },
  {
    name: 'SourceForge',
    url: 'https://sourceforge.net/software/product/CopyElement/',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093503537-19.png',
  },
  {
    name: 'Substack',
    url: 'https://nocodeexits.substack.com/p/how-saurabh-built-a-no-code-component',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093504345-20.png',
  },
  {
    name: 'TrustRadius',
    url: 'https://www.trustradius.com/products/copyelement-elementor-component-library/reviews',
    image: 'https://gallery.theportfolio.in/CopyElements/1750093505041-21.png',
  },
]
