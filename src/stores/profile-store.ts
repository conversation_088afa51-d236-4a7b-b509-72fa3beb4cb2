import { create } from 'zustand'

import { getProfile } from '@/actions/users'
import { Profile } from '@/types/custom'
import { createClient } from '@/utils/supabase/client'

interface ProfileStore {
  profile: Profile | null
  isLoading: boolean
  error: Error | null
  user: any | null
  fetchUser: () => Promise<void>
  fetchProfile: (userId: string) => Promise<void>
  setProfile: (data: Profile | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: Error | null) => void
  reset: () => void
}

export const useProfileStore = create<ProfileStore>((set, get) => ({
  profile: null,
  user: null,
  isLoading: false, // Start with false, let the query handle initial loading
  error: null,

  fetchUser: async () => {
    try {
      set({ isLoading: true, error: null })
      const supabase = createClient()
      const { data, error } = await supabase.auth.getUser()

      if (error) {
        console.error('Auth error:', error)
        set({
          user: null,
          profile: null,
          isLoading: false,
          error: error as Error,
        })
        return
      }

      // Set user first, then fetch profile
      set({ user: data.user })

      if (data.user) {
        // Fetch profile but don't set loading here since fetchProfile handles it
        await get().fetchProfile(data.user.id)
      } else {
        // No user, clear everything
        set({ profile: null, isLoading: false })
      }
    } catch (error) {
      console.error('Error fetching user:', error)
      set({
        error: error as Error,
        isLoading: false,
        user: null,
        profile: null,
      })
    }
  },

  fetchProfile: async (userId: string) => {
    try {
      // Don't set loading here since fetchUser already handles it
      const data = await getProfile(userId)
      set({ profile: data, isLoading: false }) // Make sure to set loading false here
    } catch (error) {
      console.error('Error fetching profile:', error)
      set({ error: error as Error, isLoading: false })
    }
  },

  setProfile: (data) => set({ profile: data, isLoading: false }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error, isLoading: false }),
  reset: () =>
    set({ profile: null, user: null, isLoading: false, error: null }),
}))
