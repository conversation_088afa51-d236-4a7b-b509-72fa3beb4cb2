import { toast } from 'sonner'

import { getCode } from '@/actions/server-components'
import { getTemplateCode } from '@/actions/template'
import { useProfileStore } from '@/stores/profile-store'

import { createClient } from './supabase/client'

const requestClipboardPermission = async (): Promise<boolean> => {
  try {
    // Check if clipboard-write permission is available
    if (!navigator?.permissions) {
      return true // Older browsers without Permissions API default to true
    }

    const result = await navigator.permissions.query({
      name: 'clipboard-write' as PermissionName,
    })
    if (result.state === 'granted') return true
    if (result.state === 'prompt') {
      // This will trigger the permission prompt
      await navigator.clipboard.writeText('')
      return true
    }
    return false
  } catch (error) {
    console.error('Permission check failed:', error)
    return false
  }
}

export const copyToClipBoard = async (
  id: string,
  isPro: boolean,
  isTemplate: boolean = false
) => {
  const supabase = createClient()
  const profile = useProfileStore.getState().profile // Read profile from Zustand store
  const toastId = toast.loading(
    ` Copying ${isTemplate ? 'template' : 'component'}
    `
  )

  try {
    // Check for active session
    const { data, error: sessionError } = await supabase.auth.getSession()
    if (sessionError) {
      console.error('Session error:', sessionError)
      throw new Error('Failed to fetch session')
    }

    const user = data.session?.user
    if (isPro) {
      // Check if the user is logged in
      if (!user) {
        toast.error('You need to be logged in to copy this', {
          id: toastId,
        })
        return
      }

      // Check if the user is a pro user
      if (!profile?.is_pro) {
        toast.info('Upgrade to a pro plan to copy this', {
          id: toastId,
        })
        return
      }
    }

    // Check clipboard permission
    const hasPermission = await requestClipboardPermission()
    if (!hasPermission) {
      toast.error('Clipboard permission denied. ', {
        id: toastId,
        duration: 5000,
        description: 'Please enable clipboard access in your browser settings.',
      })
      return
    }

    // Fetch the code
    const code = isTemplate ? await getTemplateCode(id) : await getCode(id)
    if (!code) {
      throw new Error('No code found for the provided ID')
    }
    // Try different clipboard methods
    try {
      await navigator.clipboard.writeText(code)
    } catch (clipboardError) {
      // Fallback for older browsers or when Clipboard API fails
      const textArea = document.createElement('textarea')
      textArea.value = code
      textArea.style.position = 'fixed'
      textArea.style.opacity = '0'
      document.body.appendChild(textArea)
      textArea.select()

      try {
        document.execCommand('copy')
        document.body.removeChild(textArea)
      } catch (fallbackError) {
        document.body.removeChild(textArea)
        throw new Error('Failed to copy code')
      }
    }

    toast.success(`${isTemplate ? 'Template' : 'Component'} copied!`, {
      id: toastId,
    })
  } catch (error) {
    console.error('Failed to copy code:', error)
    toast.error(
      error instanceof Error ? error.message : 'An unexpected error occurred',
      { id: toastId }
    )
  }
}
