export const getInitials = (firstName: string, lastName: string): string => {
  return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || '?'
}

export const getBadgeVariant = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'success' // green
    case 'pending':
      return 'default' // yellow
    case 'cancelled':
      return 'warning' // gray
    case 'failed':
      return 'destructive' // red
    default:
      return 'secondary' // fallback
  }
}
