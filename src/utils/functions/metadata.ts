import { Metadata } from 'next'

export const generateMetadata = ({
  title = `${
    process.env.NEXT_PUBLIC_APP_NAME ?? 'CopyElement'
  } - Largest Elementor Components Library`,
  description = `Build stunning WordPress sites in minutes with CopyElement’s 3500+ Elementor components & 2000+ Templates with just Copy - Paste. No extra plugins`,
  image = '/copyelement-thumbnail.webp',
  icons = [
    {
      rel: 'apple-touch-icon',
      sizes: '32x32',
      url: '/apple-touch-icon.png',
    },
    {
      rel: 'icon',
      sizes: '32x32',
      url: '/favicon-32x32.png',
    },
    {
      rel: 'icon',
      sizes: '16x16',
      url: '/favicon-16x16.png',
    },
  ],
  noIndex = false,
}: {
  title?: string
  description?: string
  image?: string | null
  icons?: Metadata['icons']
  noIndex?: boolean
} = {}): Metadata => ({
  title,
  description,
  icons,
  openGraph: {
    title,
    description,
    ...(image && { images: [{ url: image }] }),
  },
  twitter: {
    title,
    description,
    ...(image && { card: 'summary_large_image', images: [image] }),
    creator: '@ThePortfolyo',
  },
  // metadataBase: new URL(process.env.APP_DOMAIN!),
  ...(noIndex && { robots: { index: false, follow: false } }),
})
