export interface RouteProps {
  href: string
  label: string
}

export interface FeatureProps {
  title: string
  description: string
  href?: string
  isExternal?: boolean
}

export const routeList: RouteProps[] = [
  {
    href: '/components',
    label: 'Components',
  },
  {
    href: '/templates',
    label: 'Templates',
  },
  {
    href: '/pricing',
    label: 'Pricing',
  },
  {
    href: '/about-us',
    label: 'About',
  },
  {
    href: '/contact-us',
    label: 'Contact',
  },
  // {
  //   href: '/faq',
  //   label: 'FAQ',
  // },
]
// feature about CopyElement
export const resources: FeatureProps[] = [
  {
    title: 'ShowCase',
    description: 'Discover Community Built Components and Templates.',
    href: 'https://showcase.copyelement.com/',
    isExternal: true,
  },
  {
    title: 'Changelog',
    description: 'Stay updated with the latest features and improvements.',
    href: 'https://changelog.copyelement.com/',
    isExternal: true,
  },
  {
    title: 'Blog',
    description:
      ' Read articles, tutorials, and tips on building with CopyElement.',
    isExternal: true,
    href: 'https://blog.copyelement.com/',
  },
  // {
  //   title: 'Optimized for you',
  //   description:
  //     'CopyElement is optimized for you. Choose from 3500+ components to build your next project.',
  // },
]

export const excludesUrls = [
  '/components',
  '/templates',
  '/dashboard',
  '/login',
  '/signup',
  '/forgot-password',
  '/reset-password',
  '/email-verification-error',
  '/auth/confirm',
  '/auth/signout',
]
