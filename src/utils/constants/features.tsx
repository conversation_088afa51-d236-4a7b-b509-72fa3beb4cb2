import {
  Box,
  Code,
  Layout,
  Lock,
  Search,
  Settings,
  Sparkles,
} from 'lucide-react'

export interface FeatureCardProps {
  area: string
  icon: any
  title: string
  description: string
  image: string
}

export const featureCards: FeatureCardProps[] = [
  {
    area: 'md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/7]',
    icon: Box,
    title: 'Easy to customize layouts',
    description:
      'CopyElement layouts are crafted to be highly user-friendly and versatile, enabling you to easily personalize every component.',
    image:
      'https://gallery.theportfolio.in/CopyElements/1748021507889-easy-to-customize.webp',
  },

  {
    area: 'md:[grid-area:1/7/2/13] xl:[grid-area:1/7/2/13]',
    icon: Settings,
    title: 'Fully Responsive Components',
    description:
      'Our components are expertly engineered to adapt automatically, ensuring a seamless viewing experience on all screen sizes.',
    image:
      'https://gallery.theportfolio.in/CopyElements/1748021506201-fully-responsive.webp',
  },

  {
    area: 'md:[grid-area:2/1/3/7] xl:[grid-area:2/1/3/5]',

    icon: Code,
    title: 'Lightning-Fast',
    description:
      'Expertly crafted layouts so you can enjoy Elementor templates that are as fast as they are beautiful.',
    image:
      'https://gallery.theportfolio.in/CopyElements/1748021503986-lightening-fast.webp',
  },

  {
    area: 'md:[grid-area:2/7/3/13] xl:[grid-area:2/5/3/9]',
    icon: Sparkles,
    title: 'Weekly New Components',
    description:
      'Stay up-to-date with our weekly drops of new components and templates, constantly expanding your design possibilities.',
    image:
      'https://gallery.theportfolio.in/CopyElements/1748021487388-overview.webp',
  },

  {
    area: 'md:[grid-area:3/1/4/13] xl:[grid-area:2/9/3/13]',

    icon: Layout,
    title: 'Consistent Design System',
    description:
      'Built with a cohesive design system ensuring visual harmony and professional consistency across all components.',
    image:
      'https://gallery.theportfolio.in/CopyElements/1748021507151-design-system.webp',
  },

  {
    area: 'md:[grid-area:4/1/5/7] xl:[grid-area:3/1/4/7]',

    icon: Lock,
    title: 'Premium Components & Templates',
    description:
      'Access exclusive premium components and templates with our Pro subscription, designed for professional developers.',
    image:
      'https://gallery.theportfolio.in/CopyElements/1748021490937-premium-features.webp',
  },

  {
    area: 'md:[grid-area:4/7/5/13] xl:[grid-area:3/7/4/13]',

    icon: Search,
    title: 'Smart Search',
    description:
      'Quickly find the perfect component with our intelligent search system, filtering by categories and features.',
    image:
      'https://gallery.theportfolio.in/CopyElements/1748021490019-smart-search.webp',
  },
]
