const INDEXNOW_KEY =
  process.env.INDEXNOW_KEY || '63fe543bd6ea4c44ad384fa65a1add34' // Replace with your key
const SITE_URL = 'https://copyelement.com'

type IndexNowPayload = {
  host: string
  key: string
  urlList: string[]
}

export async function notifyIndexNow(urls: string[]) {
  // Ensure URLs are from our domain
  const validUrls = urls.filter((url) => url.startsWith(SITE_URL))

  if (validUrls.length === 0) return

  const payload: IndexNowPayload = {
    host: new URL(SITE_URL).host,
    key: INDEXNOW_KEY,
    urlList: validUrls,
  }

  try {
    // Notify Bing
    await fetch('https://www.bing.com/indexnow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    })

    console.log('Successfully notified IndexNow')
  } catch (error) {
    console.error('Failed to notify IndexNow:', error)
  }
}
