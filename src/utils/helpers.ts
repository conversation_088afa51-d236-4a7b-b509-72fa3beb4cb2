// src/utils/helpers.ts
import { createClient } from '@/utils/supabase/server'
// const supabase = createClient()

export function generateOrderId(
  subscriptionPlanType: string,
  email: string
): string {
  const emailPrefix = email
    .split('@')[0]
    .replace(/[^a-zA-Z0-9]/g, '')
    .slice(0, 10)
  const shortTimestamp = Date.now().toString().slice(-6)
  const randomSuffix = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, '0')

  const orderId =
    `C_${subscriptionPlanType}_${emailPrefix}_${shortTimestamp}_${randomSuffix}`
      .slice(0, 35)
      .toUpperCase()

  return orderId
}

export async function applyDiscount(amount: number, couponCode?: string) {
  if (!couponCode) return { amount, coupon: null }
  const supabase = createClient()

  const { data: discount, error } = await supabase
    .from('coupons')
    .select('*')
    .eq('code', couponCode)
    .gte('valid_to', new Date().toISOString())
    .lte('valid_from', new Date().toISOString())
    .single()

  if (error || !discount) throw new Error('Invalid or expired coupon code.')

  if (discount.discount_percentage) {
    amount -= (amount * discount.discount_percentage) / 100
  } else if (discount.discount_amount) {
    amount -= discount.discount_amount
  }

  return { amount, coupon: discount }
}

export async function CreateSupabaseOrder(
  order_id: string,
  subscription_plan_id: string,
  user_id: string,
  amount: number,
  payment_option: 'paypal' | 'phonepe' | 'Coupon' | 'FREE',
  currency?: 'INR' | 'USD',
  coupon_id?: string | null
) {
  const supabase = createClient()

  console.log('Creating order record...')
  const { data: order, error: orderError } = await supabase
    .from('orders')
    .insert({
      subscription_plan_id,
      amount: amount, // actual amount not unit amount
      status: 'PENDING',
      currency: currency,
      user_id: user_id,
      coupon_id: coupon_id ? coupon_id : null,
      order_id: order_id,
      payment_option: payment_option,
    })
    .select('id')
    .single()

  console.log('Order created:', order)

  if (orderError || !order) {
    console.error('Order creation error:', orderError)
    throw new Error('Failed to create order record.')
  }
  return order
}

export async function updateSubabaseOrder(
  payment_id: string,
  order_id: string
) {
  const supabase = createClient()

  try {
    const { error: updateError } = await supabase
      .from('orders')
      .update({ payment_id: payment_id })
      .eq('id', order_id)

    return { updateError }
  } catch (error) {}
}
