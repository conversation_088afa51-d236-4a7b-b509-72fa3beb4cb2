const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID!
const PAYPAL_SECRET = process.env.PAYPAL_SECRET!
const PAYPAL_API_BASE_URL =
  process.env.PAYPAL_API_BASE_URL || 'https://api-m.sandbox.paypal.com'

export const PayPalPayment = {
  async getAccessToken(): Promise<string> {
    const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_SECRET}`).toString(
      'base64'
    )
    const response = await fetch(`${PAYPAL_API_BASE_URL}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        Authorization: `Basic ${auth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'grant_type=client_credentials',
    })

    const data = await response.json()
    if (!response.ok || !data.access_token) {
      // console.error('PayPal access token error:', data)
      throw new Error('Failed to retrieve PayPal access token.')
    }

    return data.access_token
  },

  async createOrder(amount: number, currency: string, orderId: string) {
    const accessToken = await this.getAccessToken()

    const orderData = {
      intent: 'CAPTURE',
      purchase_units: [
        {
          reference_id: orderId,
          amount: {
            // value: amount * 100,
            // value: (amount / 100).toFixed(2),
            value: Math.round(amount).toFixed(2),
            currency_code: currency,
          },
        },
      ],

      // this is for the new version of paypal but it don't have approve link as mentioned in docs
      // payment_source: {
      //   paypal: {
      //     experience_context: {
      //       brand_name: 'CopyElement',
      //       return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback/paypal`,
      //       cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback/paypal`,
      //       // return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status/success`,
      //       // cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status/failed`,
      //     },
      //   },
      // },
      application_context: {
        return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback/paypal`,
        cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback/paypal`,
        // return_url: `${process.env.NEXT_PUBLIC_BASE_URL}/payment-success`,
        // cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/payment-cancel`,
      },
    }

    const response = await fetch(`${PAYPAL_API_BASE_URL}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    })

    const data = await response.json()

    // console.log('response', response)
    // console.log('data', data)
    if (!response.ok || !data.id) {
      console.error('PayPal order creation error:', data)
      throw new Error('Failed to create PayPal order.')
    }

    return data
  },

  async checkStatus(orderId: string) {
    const accessToken = await this.getAccessToken()

    const response = await fetch(
      `${PAYPAL_API_BASE_URL}/v2/checkout/orders/${orderId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    )

    const data = await response.json()

    if (!response.ok) {
      console.error('PayPal order status check error:', data)
      throw new Error('Failed to check PayPal order status.')
    }

    return data
  },
}
