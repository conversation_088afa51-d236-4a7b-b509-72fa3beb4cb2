// src/utils/payment/phonepe.ts
import crypto from 'crypto'

export const EXCHANGE_RATE = 84
const PHONEPE_MERCHANT_ID = process.env.PHONEPE_MERCHANT_ID!
const PHONEPE_SALT_KEY = process.env.PHONEPE_SALT_KEY!
const PHONEPE_BASE_URL =
  process.env.PHONEPE_BASE_URL || 'https://api.phonepe.com/apis/hermes'

export class PhonePePayment {
  static async createOrder(amount: number, userId: string, orderId: string) {
    console.log('Creating PhonePe order...')

    const requestBody = {
      merchantId: PHONEPE_MERCHANT_ID,
      merchantTransactionId: orderId.slice(0, 33),
      merchantUserId: userId.slice(0, 34),
      // amount: 100,
      amount: Math.round(amount * 100),

      // redirectUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status`,
      redirectUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback/phonepe`,
      redirectMode: 'REDIRECT', // REDIRECT or POST
      // when post we use callbackUrl
      // callbackUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/api/payment/callback/phonepe`,
      paymentInstrument: {
        type: 'PAY_PAGE',
      },
    }

    const payload = JSON.stringify(requestBody)
    const base64Payload = Buffer.from(payload).toString('base64')
    const checksumData = base64Payload + '/pg/v1/pay' + PHONEPE_SALT_KEY
    const sha256Hash = crypto
      .createHash('sha256')
      .update(checksumData)
      .digest('hex')
    const checksum = sha256Hash + '###' + 1

    const response = await fetch(`${PHONEPE_BASE_URL}/pg/v1/pay`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum,
      },
      body: JSON.stringify({ request: base64Payload }),
    })

    if (!response.ok) {
      const errorResponse = await response.json()
      throw new Error(`PhonePe API Error: ${errorResponse.message}`)
    }
    console.log('PhonePe order created:', response)

    return await response.json()
  }

  static async checkStatus(merchantTransactionId: string) {
    console.log('Checking PhonePe order status...')

    const path = `/pg/v1/status/${PHONEPE_MERCHANT_ID}/${merchantTransactionId}`
    const checksumData = path + PHONEPE_SALT_KEY
    const sha256Hash = crypto
      .createHash('sha256')
      .update(checksumData)
      .digest('hex')
    const checksum = sha256Hash + '###' + 1

    const response = await fetch(`${PHONEPE_BASE_URL}${path}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum,
        'X-MERCHANT-ID': PHONEPE_MERCHANT_ID,
      },
    })

    if (!response.ok) {
      const errorResponse = await response.json()
      throw new Error(`PhonePe API Error: ${errorResponse.message}`)
    }

    console.log('PhonePe order status retrieved:', response)

    return await response.json()
  }
}
