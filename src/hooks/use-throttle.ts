'use client'

import { useCallback, useRef } from 'react'

/**
 * Custom hook for throttling function calls
 * @param callback - Function to throttle
 * @param delay - Delay in milliseconds
 * @returns Throttled function
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now())

  return useCallback(
    ((...args: Parameters<T>) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args)
        lastRun.current = Date.now()
      }
    }) as T,
    [callback, delay]
  )
}

/**
 * Custom hook for throttling async function calls with loading state
 * @param callback - Async function to throttle
 * @param delay - Delay in milliseconds
 * @returns Object with throttled function and loading state
 */
export function useAsyncThrottle<T extends (...args: any[]) => Promise<any>>(
  callback: T,
  delay: number = 1000
) {
  const lastRun = useRef(Date.now())
  const isRunning = useRef(false)

  const throttledCallback = useCallback(
    async (...args: Parameters<T>) => {
      if (isRunning.current || Date.now() - lastRun.current < delay) {
        return
      }

      isRunning.current = true
      lastRun.current = Date.now()

      try {
        await callback(...args)
      } finally {
        isRunning.current = false
      }
    },
    [callback, delay]
  )

  return {
    execute: throttledCallback as T,
    isThrottling: isRunning.current,
  }
}
