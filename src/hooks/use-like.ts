'use client'

import { useMutation } from '@tanstack/react-query'
import { useMemo } from 'react'
import { toast } from 'sonner'

import { toggleLike } from '@/actions/likes'
import { useProfileStore } from '@/stores/profile-store'

interface UseLikeProps {
  componentId: string
  initialLikes?: number
}

export function useLike({ componentId }: UseLikeProps) {
  const { user, profile, fetchProfile } = useProfileStore()

  // Check if user likes this component from profile data
  const isLiked = useMemo(() => {
    if (!profile?.liked_components) return false
    return profile.liked_components.includes(componentId)
  }, [profile?.liked_components, componentId])

  // Toggle like mutation
  const toggleLikeMutation = useMutation({
    mutationFn: () => toggleLike(componentId),
    onSuccess: (data: any) => {
      if (data?.error) {
        toast.error(data.error)
        return
      }

      // Refresh profile data to get updated liked_components
      if (user?.id) {
        fetchProfile(user.id)
      }

      // Show success message
      toast.success(
        data?.isLiked ? 'Added to favorites!' : 'Removed from favorites'
      )
    },
    onError: () => {
      toast.error('Failed to update like status')
    },
  })

  const handleToggleLike = () => {
    if (!user) {
      toast.error('Please log in to like components')
      return
    }

    toggleLikeMutation.mutate()
  }

  return {
    isLiked,
    isLoading: toggleLikeMutation.isPending,
    toggleLike: handleToggleLike,
    isAuthenticated: !!user,
  }
}
