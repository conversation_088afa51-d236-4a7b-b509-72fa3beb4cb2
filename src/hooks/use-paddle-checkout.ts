'use client'

import { useState } from 'react'

import {
  createPaddleCheckout,
  createPaddleCustomer,
} from '@/lib/paddle/paddle-client'

interface UsePaddleCheckoutProps {
  priceId: string
  onSuccess?: (data: any) => void
  onError?: (error: Error) => void
}

export function usePaddleCheckout({
  priceId,
  onSuccess,
  onError,
}: UsePaddleCheckoutProps) {
  const [isLoading, setIsLoading] = useState(false)

  const startCheckout = async ({
    email,
    name,
    userId,
    customData = {},
    discountId,
  }: {
    email: string
    name?: string
    userId?: string
    customData?: Record<string, any>
    discountId?: string
  }) => {
    setIsLoading(true)

    try {
      // Create or find customer
      let customerId: string | undefined

      if (email) {
        try {
          const customer = await createPaddleCustomer({
            email,
            name,
            customData: {
              userId,
              ...customData,
            },
          })
          customerId = customer.id
        } catch (customerError) {
          console.warn(
            'Customer creation failed, proceeding without customer ID:',
            customerError
          )
        }
      }

      // Create checkout session
      const checkout = await createPaddleCheckout({
        priceId,
        customerId,
        customData: {
          userId,
          email,
          ...customData,
        },
        discountId,
      })

      // Handle checkout result based on actual Paddle response structure
      // The exact structure depends on how Paddle returns checkout URLs
      console.log('Checkout created:', checkout)

      if (onSuccess) {
        onSuccess(checkout)
      }

      return checkout
    } catch (error) {
      console.error('Checkout error:', error)
      if (onError) {
        onError(error as Error)
      }
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  return {
    startCheckout,
    isLoading,
  }
}

// Hook for one-time payments
export function usePaddlePayment() {
  const [isLoading, setIsLoading] = useState(false)

  const createPayment = async ({
    priceId,
    email,
    name,
    userId,
    customData = {},
  }: {
    priceId: string
    email: string
    name?: string
    userId?: string
    customData?: Record<string, any>
  }) => {
    setIsLoading(true)

    try {
      // Create customer first
      let customerId: string | undefined

      try {
        const customer = await createPaddleCustomer({
          email,
          name,
          customData: {
            userId,
            ...customData,
          },
        })
        customerId = customer.id
      } catch (customerError) {
        console.warn('Customer creation failed:', customerError)
      }

      // Create payment
      const payment = await createPaddleCheckout({
        priceId,
        customerId,
        customData: {
          userId,
          email,
          ...customData,
        },
      })

      return payment
    } catch (error) {
      console.error('Payment error:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  return {
    createPayment,
    isLoading,
  }
}
