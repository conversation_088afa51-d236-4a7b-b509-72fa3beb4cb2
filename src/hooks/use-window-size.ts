'use client'
import { useCallback, useEffect, useState } from 'react'

interface WindowSize {
  width: number
  height: number
}

export function useWindowSize(debounceTime: number = 100): WindowSize {
  const [windowSize, setWindowSize] = useState<WindowSize>({
    width: typeof window !== 'undefined' ? window.innerWidth : 1200,
    height: typeof window !== 'undefined' ? window.innerHeight : 800,
  })

  const debounce = useCallback((func: () => void, wait: number) => {
    let timeout: ReturnType<typeof setTimeout>
    return () => {
      clearTimeout(timeout)
      timeout = setTimeout(func, wait)
    }
  }, [])

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    const debouncedHandleResize = debounce(handleResize, debounceTime)

    window.addEventListener('resize', debouncedHandleResize)
    handleResize()

    return () => window.removeEventListener('resize', debouncedHandleResize)
  }, [debounceTime, debounce])

  return windowSize
}
