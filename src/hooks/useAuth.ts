import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'

import { useProfileStore } from '@/stores/profile-store'
import { createClient } from '@/utils/supabase/client'

export const useAuth = () => {
  const queryClient = useQueryClient()
  const { user, profile, isLoading, error, fetchUser } = useProfileStore()

  // Simple query without complex loading logic
  const { refetch, isRefetching } = useQuery({
    queryKey: ['user-profile'],
    queryFn: fetchUser,
    staleTime: 10 * 1000, // 10 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    retry: 1,
  })

  // Listen for auth state changes
  useEffect(() => {
    const supabase = createClient()

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        // User signed in or token refreshed, refetch profile
        setTimeout(() => refetch(), 100) // Small delay to ensure auth is settled
      } else if (event === 'SIGNED_OUT') {
        // User signed out, clear the cache and reset store
        queryClient.clear()
        useProfileStore.getState().reset()
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [refetch, queryClient])

  return {
    user,
    profile,
    isLoading: isLoading && !user, // Only show loading if we don't have a user yet
    isRefetching,
    error,
    refetch,
    refreshAuth: refetch,
    isAuthenticated: !!user,
  }
}
