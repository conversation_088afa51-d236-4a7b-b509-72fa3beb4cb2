'use client'

import { ArrowRightIcon, CheckIcon } from '@radix-ui/react-icons'
import { useState } from 'react'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/utils/tailwind'

interface Feature {
  name: string
  description: string
  included: boolean
}

interface PricingTier {
  name: string
  price: {
    monthly: number
    yearly: number
  }
  description: string
  features: Feature[]
  highlight?: boolean
  badge?: string
  icon: React.ReactNode
}

interface PricingSectionProps {
  tiers: PricingTier[]
  className?: string
}

function PricingSection({ tiers, className }: PricingSectionProps) {
  const [isYearly, setIsYearly] = useState(false)

  const buttonStyles = {
    default: cn(
      'h-12 bg-white dark:bg-zinc-900',
      'hover:bg-zinc-50 dark:hover:bg-zinc-800',
      'text-zinc-900 dark:text-zinc-100',
      'border border-zinc-200 dark:border-zinc-800',
      'hover:border-zinc-300 dark:hover:border-zinc-700',
      'shadow-sm hover:shadow-md',
      'text-sm font-medium'
    ),
    highlight: cn(
      'h-12 bg-zinc-900 dark:bg-zinc-100',
      'hover:bg-zinc-800 dark:hover:bg-zinc-300',
      'text-white dark:text-zinc-900',
      'shadow-[0_1px_15px_rgba(0,0,0,0.1)]',
      'hover:shadow-[0_1px_20px_rgba(0,0,0,0.15)]',
      'font-semibold text-base'
    ),
  }

  const badgeStyles = cn(
    'px-4 py-1.5 text-sm font-medium',
    'bg-zinc-900 dark:bg-zinc-100',
    'text-white dark:text-zinc-900',
    'border-none shadow-lg'
  )

  return (
    <section
      className={cn(
        'relative bg-background text-foreground',
        'px-4 py-12 md:py-24 lg:py-32',
        'overflow-hidden',
        className
      )}
    >
      <div className="mx-auto w-full max-w-5xl">
        <div className="mb-12 flex flex-col items-center gap-4">
          <h2 className="text-3xl font-bold text-zinc-900 dark:text-zinc-50">
            Simple, transparent pricing
          </h2>
          <div className="inline-flex items-center rounded-full border border-zinc-200 bg-white p-1.5 shadow-sm dark:border-zinc-700 dark:bg-zinc-800/50">
            {['Monthly', 'Yearly'].map((period) => (
              <button
                key={period}
                onClick={() => setIsYearly(period === 'Yearly')}
                className={cn(
                  'rounded-full px-8 py-2.5 text-sm font-medium transition-all duration-300',
                  (period === 'Yearly') === isYearly
                    ? 'bg-zinc-900 text-white shadow-lg dark:bg-zinc-100 dark:text-zinc-900'
                    : 'text-zinc-600 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-zinc-100'
                )}
              >
                {period}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
          {tiers.map((tier) => (
            <div
              key={tier.name}
              className={cn(
                'group relative backdrop-blur-sm',
                'rounded-3xl transition-all duration-300',
                'flex flex-col',
                tier.highlight
                  ? 'bg-gradient-to-b from-zinc-100/80 to-transparent dark:from-zinc-400/[0.15]'
                  : 'bg-white dark:bg-zinc-800/50',
                'border',
                tier.highlight
                  ? 'border-zinc-400/50 shadow-xl dark:border-zinc-400/20'
                  : 'border-zinc-200 shadow-md dark:border-zinc-700',
                'hover:translate-y-0 hover:shadow-lg'
              )}
            >
              {tier.badge && tier.highlight && (
                <div className="absolute -top-4 left-6">
                  <Badge className={badgeStyles}>{tier.badge}</Badge>
                </div>
              )}

              <div className="flex-1 p-8">
                <div className="mb-4 flex items-center justify-between">
                  <div
                    className={cn(
                      'rounded-xl p-3',
                      tier.highlight
                        ? 'bg-zinc-100 text-zinc-900 dark:bg-zinc-800 dark:text-zinc-100'
                        : 'bg-zinc-100 text-zinc-600 dark:bg-zinc-800 dark:text-zinc-400'
                    )}
                  >
                    {tier.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-zinc-900 dark:text-zinc-100">
                    {tier.name}
                  </h3>
                </div>

                <div className="mb-6">
                  <div className="flex items-baseline gap-2">
                    <span className="text-4xl font-bold text-zinc-900 dark:text-zinc-100">
                      ${isYearly ? tier.price.yearly : tier.price.monthly}
                    </span>
                    <span className="text-sm text-zinc-500 dark:text-zinc-400">
                      /{isYearly ? 'year' : 'month'}
                    </span>
                  </div>
                  <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-400">
                    {tier.description}
                  </p>
                </div>

                <div className="space-y-4">
                  {tier.features.map((feature) => (
                    <div key={feature.name} className="flex gap-4">
                      <div
                        className={cn(
                          'mt-1 rounded-full p-0.5 transition-colors duration-200',
                          feature.included
                            ? 'text-emerald-600 dark:text-emerald-400'
                            : 'text-zinc-400 dark:text-zinc-600'
                        )}
                      >
                        <CheckIcon className="h-4 w-4" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-zinc-900 dark:text-zinc-100">
                          {feature.name}
                        </div>
                        <div className="text-sm text-zinc-500 dark:text-zinc-400">
                          {feature.description}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-auto p-8 pt-0">
                <Button
                  className={cn(
                    'relative w-full transition-all duration-300',
                    tier.highlight
                      ? buttonStyles.highlight
                      : buttonStyles.default
                  )}
                >
                  <span className="relative z-10 flex items-center justify-center gap-2">
                    {tier.highlight ? (
                      <>
                        Buy now
                        <ArrowRightIcon className="h-4 w-4" />
                      </>
                    ) : (
                      <>
                        Get started
                        <ArrowRightIcon className="h-4 w-4" />
                      </>
                    )}
                  </span>
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export { PricingSection }
