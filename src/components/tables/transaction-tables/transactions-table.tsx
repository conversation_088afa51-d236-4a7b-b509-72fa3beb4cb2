'use client'
import { DataTable } from '@/components/tables/reusable-table'
import { Transaction } from '@/types/custom'

import { columns } from './columns'

interface TransactionsTableProps {
  data: Transaction[]
  pageNo: number
  totalCount: number
  pageCount: number
  isLoading: boolean
}

export function TransactionsTable({
  data,
  pageNo,
  totalCount,
  pageCount,
  isLoading,
}: TransactionsTableProps) {
  return (
    <DataTable<Transaction, string>
      columns={columns}
      data={data}
      pageNo={pageNo}
      searchKey="id"
      totalCount={totalCount}
      pageCount={pageCount}
      isLoading={isLoading}
    />
  )
}
