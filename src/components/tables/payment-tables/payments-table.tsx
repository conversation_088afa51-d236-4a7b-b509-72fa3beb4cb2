'use client'
import { Payment } from '@/types/custom'

import { columns } from './columns'
import { DataTable } from '../reusable-table'

interface PaymentsTableProps {
  data: Payment[]
  pageNo: number
  totalCount: number
  pageCount: number
  isLoading: boolean
  pageSizeOptions?: number[]
}

export function PaymentsTable({
  data,
  pageNo,
  totalCount,
  pageCount,
  isLoading,
  pageSizeOptions = [10, 20, 30, 40, 50],
}: PaymentsTableProps) {
  return (
    <DataTable<Payment, string>
      columns={columns}
      data={data}
      searchKey="payment_id"
      pageNo={pageNo}
      totalCount={totalCount}
      pageCount={pageCount}
      isLoading={isLoading}
      pageSizeOptions={pageSizeOptions}
    />
  )
}
