'use client'
import { ColumnDef } from '@tanstack/react-table'
import { format } from 'date-fns'
import { Eye } from 'lucide-react'

import LinkItem from '@/components/LInkItem/LinkItem'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { DataTableColumnHeader } from '@/components/ui/data-table-column-header'
import { Payment } from '@/types/custom'

export const columns: ColumnDef<Payment>[] = [
  {
    accessorKey: 'payment_id',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Payment ID" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          <span className="font-medium">{row.getValue('payment_id')}</span>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'order_id',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Order ID" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center">
          <span className="font-medium">{row.getValue('order_id')}</span>
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'payment_status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue('payment_status') as string
      return (
        <Badge
          variant={
            status?.toLowerCase() === 'success' ||
            status?.toLowerCase() === 'completed'
              ? 'success'
              : status?.toLowerCase() === 'pending'
                ? 'destructive'
                : 'destructive'
          }
        >
          {status}
        </Badge>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'transaction_id',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Transaction ID" />
    ),
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('transaction_id')}</div>
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'payer_email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Payer Email" />
    ),
    cell: ({ row }) => {
      return <div className="font-medium">{row.getValue('payer_email')}</div>
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'created_at',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Date" />
    ),
    cell: ({ row }) => {
      const date = row.getValue('created_at') as string
      return date ? (
        <div>{format(new Date(date), 'MMM dd, yyyy HH:mm')}</div>
      ) : null
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center justify-end gap-2">
          <LinkItem href={`/dashboard/payments/${row.original.id}`}>
            <Button variant="ghost" size="icon">
              <Eye className="h-4 w-4" />
            </Button>
          </LinkItem>
        </div>
      )
    },
    enableSorting: false,
    enableHiding: false,
  },
]
