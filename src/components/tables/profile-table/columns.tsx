'use client'

import { ColumnDef } from '@tanstack/react-table'

import LinkItem from '@/components/LInkItem/LinkItem'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Profile } from '@/types/custom'

export const columns: ColumnDef<Profile>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'avatar_url',
    header: 'Avatar',
    cell: ({ row }) => (
      <img
        src={row.original.avatar_url || ''}
        alt="avatar"
        className="h-8 w-8 rounded-full"
      />
    ),
  },
  { accessorKey: 'first_name', header: 'First Name' },
  { accessorKey: 'last_name', header: 'Last Name' },
  { accessorKey: 'email', header: 'Email' },
  { accessorKey: 'role', header: 'Role' },
  { accessorKey: 'username', header: 'Username' },
  { accessorKey: 'created_at', header: 'Created At' },
  { accessorKey: 'updated_at', header: 'Updated At' },
  { accessorKey: 'subscription_status', header: 'Subscription Status' },
  { accessorKey: 'subscription_start_date', header: 'Sub Start' },
  { accessorKey: 'subscription_end_date', header: 'Sub End' },
  { accessorKey: 'current_subscription_plan_id', header: 'Plan ID' },
  {
    accessorKey: 'is_pro',
    header: 'Pro',
    cell: ({ row }) => (row.original.is_pro ? 'Yes' : 'No'),
  },
  {
    accessorKey: 'is_lifetime_pro',
    header: 'Lifetime Pro',
    cell: ({ row }) => (row.original.is_lifetime_pro ? 'Yes' : 'No'),
  },
  {
    accessorKey: 'is_lifetime_subscription',
    header: 'Lifetime Sub',
    cell: ({ row }) => (row.original.is_lifetime_subscription ? 'Yes' : 'No'),
  },
  { accessorKey: 'submitted_components_count', header: 'Submitted Components' },
  {
    accessorKey: 'liked_components',
    header: 'Liked Components',
    cell: ({ row }) =>
      Array.isArray(row.original.liked_components)
        ? row.original.liked_components.length
        : 0,
  },
  {
    accessorKey: 'username_attempt_failed',
    header: 'Username Failed',
    cell: ({ row }) => (row.original.username_attempt_failed ? 'Yes' : 'No'),
  },
  {
    id: 'actions',
    header: 'Actions',
    cell: ({ row }) => (
      <LinkItem href={`/dashboard/profile/${row.original.id}`}>
        <Button size="sm">Edit</Button>
      </LinkItem>
    ),
  },
]
