'use client'
import { DataTable } from '@/components/tables/reusable-table'
import { CompleteOrder } from '@/types/custom'

import { columns } from './columns'

interface OrdersTableProps {
  data: CompleteOrder[]
  pageNo: number
  totalCount: number
  pageCount: number
  isLoading: boolean
}

export function OrdersTable({
  data,
  pageNo,
  totalCount,
  pageCount,
  isLoading,
}: OrdersTableProps) {
  return (
    <DataTable<CompleteOrder, string>
      columns={columns}
      data={data}
      pageNo={pageNo}
      searchKey="order_id"
      totalCount={totalCount}
      pageCount={pageCount}
      isLoading={isLoading}
    />
  )
}
