'use client'
import { Edit, MoreH<PERSON>zontal, Trash } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { toast } from 'sonner'

import { AlertModal } from '@/components/modal/alert-modal'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { canDelete, canEdit } from '@/lib/permissions'
import { useProfileStore } from '@/stores/profile-store'
import { Component } from '@/types/custom'
import { createClient } from '@/utils/supabase/client'

interface CellActionProps {
  data: Component
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const router = useRouter()
  const { profile } = useProfileStore()
  const userRole = profile?.role as 'admin' | 'editor' | null

  const onConfirm = async () => {
    // Check permission before proceeding
    if (!canDelete(userRole)) {
      toast.error("You don't have permission to delete components")
      setOpen(false)
      return
    }

    const toastId = toast.loading('Deleting...')
    try {
      setLoading(true)
      const supabase = createClient()

      const { data: deleteResult, error } = await supabase
        .from('components')
        .delete()
        .eq('id', data.id)
        .select()

      if (error) {
        toast.error(error.message ?? 'Something went wrong!')
        return
      }

      router.refresh()
      toast.success(`Component ${data.name} deleted successfully!`)
    } catch (error: any) {
      toast.error(error.response?.data?.message ?? 'Something went wrong!')
    } finally {
      setLoading(false)
      setOpen(false)
      toast.dismiss(toastId)
    }
  }

  return (
    <>
      <AlertModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={onConfirm}
        loading={loading}
      />
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          {canEdit(userRole) && (
            <DropdownMenuItem
              onClick={() => router.push(`/dashboard/components/${data.id}`)}
            >
              <Edit className="mr-2 h-4 w-4" /> Update
            </DropdownMenuItem>
          )}

          {canDelete(userRole) && (
            <DropdownMenuItem onClick={() => setOpen(true)}>
              <Trash className="mr-2 h-4 w-4" /> Delete
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}
