// @ts-nocheck
'use client'
import { ColumnDef } from '@tanstack/react-table'
import Image from 'next/image'

import { Checkbox } from '@/components/ui/checkbox'
import { Component } from '@/types/custom'

import { CellAction } from './cell-action'

export const columns: ColumnDef<Component>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'name',
    header: 'NAME',
  },
  {
    accessorKey: 'description',
    header: 'DESCRIPTION',
    cell: ({ row }) => (
      <div className="flex w-40 items-center space-x-2 truncate">
        <div>{row.getValue('description')}</div>
      </div>
    ),
  },
  {
    accessorKey: 'is_pro',
    header: 'IS PRO',
  },
  {
    accessorKey: 'image_url',
    header: 'Image',
    cell: ({ row }) => (
      <Image
        src={row.getValue('image_url')}
        width={64}
        height={50}
        alt="image-preview"
      />
      // <Checkbox
      //   checked={row.getIsSelected()}
      //   onCheckedChange={(value) => row.toggleSelected(!!value)}
      //   aria-label="Select row"
      // />
    ),
  },
  {
    accessorKey: 'tags',
    header: 'TAGS',
  },
  {
    id: 'actions',
    cell: ({ row }) => <CellAction data={row.original as Component} />,
  },
]
