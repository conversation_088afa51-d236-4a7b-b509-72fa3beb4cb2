'use client'
import { Edit, MoreHorizontal, Trash } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { toast } from 'sonner'

import { AlertModal } from '@/components/modal/alert-modal'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { canDelete, canEdit } from '@/lib/permissions'
import { useProfileStore } from '@/stores/profile-store'
import { Profile } from '@/types/custom'
import { createClient } from '@/utils/supabase/client'

interface CellActionProps {
  data: Profile
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const router = useRouter()
  const { profile } = useProfileStore()
  const userRole = profile?.role as 'admin' | 'editor' | null

  const onConfirm = async () => {
    // Check permission before proceeding
    if (!canDelete(userRole)) {
      toast.error("You don't have permission to delete users")
      setOpen(false)
      return
    }

    // Only admin can delete users
    if (data.role === 'admin' && userRole !== 'admin') {
      toast.error("You don't have permission to delete admin users")
      setOpen(false)
      return
    }

    const toastId = toast.loading('Deleting user...')
    try {
      setLoading(true)
      const supabase = createClient()

      // First delete auth user
      const { error: authError } = await supabase.auth.admin.deleteUser(data.id)

      if (authError) {
        toast.error(authError.message ?? 'Failed to delete user authentication')
        return
      }

      // Then delete profile
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', data.id)

      if (profileError) {
        toast.error(profileError.message ?? 'Failed to delete user profile')
        return
      }

      router.refresh()
      toast.success(`User ${data.email} deleted successfully!`)
    } catch (error: any) {
      toast.error(error.response?.data?.message ?? 'Something went wrong!')
    } finally {
      setLoading(false)
      setOpen(false)
      toast.dismiss(toastId)
    }
  }

  return (
    <>
      <AlertModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={onConfirm}
        loading={loading}
      />
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          {canEdit(userRole) && (
            <DropdownMenuItem
              onClick={() => router.push(`/dashboard/users/${data.id}`)}
            >
              <Edit className="mr-2 h-4 w-4" /> Update
            </DropdownMenuItem>
          )}

          {/* Only show delete option for admin users, and prevent deleting other admins unless you're an admin */}
          {canDelete(userRole) &&
            (data.role !== 'admin' || userRole === 'admin') && (
              <DropdownMenuItem onClick={() => setOpen(true)}>
                <Trash className="mr-2 h-4 w-4" /> Delete
              </DropdownMenuItem>
            )}
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}
