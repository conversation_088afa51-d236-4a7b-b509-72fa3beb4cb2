'use client'
import { ColumnDef } from '@tanstack/react-table'
import { format } from 'date-fns'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { DataTableColumnHeader } from '@/components/ui/data-table-column-header'
import { Profile } from '@/types/custom'

import { CellAction } from './cell-action'

export const columns: ColumnDef<Profile>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <Avatar className="h-8 w-8">
          <AvatarImage src={row.original.avatar_url || ''} />
          <AvatarFallback>
            {typeof row.getValue('email') === 'string'
              ? (row.getValue('email') as string).substring(0, 2).toUpperCase()
              : 'N/A'}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col">
          <span className="font-medium">{row.getValue('email')}</span>
        </div>
      </div>
    ),
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: 'name',
    accessorKey: 'first_name', // Use first_name as the accessorKey
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" />
    ),
    cell: ({ row }) => {
      const firstName = row.original.first_name
      const lastName = row.original.last_name
      const fullName = [firstName, lastName].filter(Boolean).join(' ')

      return <span className="font-medium">{fullName || 'Not provided'}</span>
    },

    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'is_pro',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const isPro = row.getValue('is_pro') as boolean // Explicit type assertion
      return (
        <Badge
          variant={isPro ? 'default' : 'secondary'}
          className="whitespace-nowrap"
        >
          {isPro ? 'PRO' : 'FREE'}
        </Badge>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'submitted_components_count',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Components" />
    ),
    cell: ({ row }) => {
      const count = row.getValue('submitted_components_count') as number // Explicit type assertion
      return <div className="text-center font-medium">{count || 0}</div>
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    accessorKey: 'updated_at',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Last Updated" />
    ),
    cell: ({ row }) => {
      const date = row.getValue('updated_at') as string | undefined // Explicit type
      return (
        <div className="whitespace-nowrap font-medium">
          {date ? format(new Date(date), 'MMM dd, yyyy') : 'Never'}
        </div>
      )
    },
    enableSorting: true,
    enableHiding: true,
  },
  {
    id: 'actions',
    cell: ({ row }) => <CellAction data={row.original} />,
    enableSorting: false,
    enableHiding: false,
  },
]
