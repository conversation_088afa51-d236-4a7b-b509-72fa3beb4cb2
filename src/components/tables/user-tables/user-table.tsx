'use client'

import { Profile } from '@/types/custom'

import { DataTable } from '../reusable-table'
import { columns } from './columns'

interface ProfileTableProps {
  data: Profile[]
  pageNo: number
  totalCount: number
  pageCount: number
  isLoading: boolean
}

export function ProfileTable({
  data,
  pageNo,
  totalCount,
  pageCount,
  isLoading,
}: ProfileTableProps) {
  return (
    <>
      <DataTable<Profile, keyof Profile>
        pageNo={pageNo}
        columns={columns}
        data={data}
        searchKey="email"
        totalCount={totalCount}
        pageCount={pageCount}
        isLoading={isLoading}
      />
    </>
  )
}
