'use client'

import { useState } from 'react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'

interface PaddleSubscriptionManagerProps {
  userId: string
  userEmail: string
  subscriptionPlans: Array<{
    id: string
    name: string
    priceId: string
    price: number
    currency: string
    description: string
  }>
}

export function PaddleSubscriptionManager({
  userId,
  userEmail,
  subscriptionPlans,
}: PaddleSubscriptionManagerProps) {
  const [isLoading, setIsLoading] = useState<string | null>(null)

  const handleSubscribe = async (plan: any) => {
    setIsLoading(plan.id)

    try {
      const response = await fetch('/api/paddle/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: plan.priceId,
          email: userEmail,
          userId: userId,
          customData: {
            planId: plan.id,
            planName: plan.name,
          },
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout')
      }

      toast.success('Your payment session has been created successfully.')

      // For now, show the transaction info
      // In a real implementation, you would redirect to Paddle's checkout
      console.log('Transaction created:', data.transaction)

      // You can integrate Paddle's frontend SDK here to complete the payment
      // Example: window.Paddle.Checkout.open({ transactionId: data.transactionId })
    } catch (error) {
      console.error('Subscription error:', error)
      toast.error(
        error instanceof Error ? error.message : 'Failed to start checkout'
      )
    } finally {
      setIsLoading(null)
    }
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Choose Your Plan</h2>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {subscriptionPlans.map((plan) => (
          <div key={plan.id} className="rounded-lg border p-6 shadow-sm">
            <h3 className="text-lg font-semibold">{plan.name}</h3>
            <p className="text-2xl font-bold">
              {plan.currency} {plan.price}
            </p>
            <p className="mb-4 text-sm text-muted-foreground">
              {plan.description}
            </p>
            <Button
              onClick={() => handleSubscribe(plan)}
              disabled={isLoading === plan.id}
              className="w-full"
            >
              {isLoading === plan.id ? 'Processing...' : 'Subscribe'}
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}
