'use client'

import { useEffect, useState } from 'react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

interface UserSubscription {
  id: string
  status: string
  nextBilledAt?: string
  canceledAt?: string
  pausedAt?: string
  items: Array<{
    price: {
      unitPrice: {
        amount: string
        currencyCode: string
      }
    }
  }>
}

interface SubscriptionCardProps {
  userId: string
  subscriptionId?: string
}

export function SubscriptionCard({
  userId,
  subscriptionId,
}: SubscriptionCardProps) {
  const [subscription, setSubscription] = useState<UserSubscription | null>(
    null
  )
  const [isLoading, setIsLoading] = useState(false)
  const [isActionLoading, setIsActionLoading] = useState(false)

  useEffect(() => {
    const fetchSubscriptionData = async () => {
      if (!subscriptionId) return

      setIsLoading(true)
      try {
        const response = await fetch(
          `/api/paddle/subscription?subscriptionId=${subscriptionId}&userId=${userId}`
        )
        const data = await response.json()

        if (response.ok) {
          setSubscription(data.subscription)
        } else {
          toast.error(data.error || 'Failed to fetch subscription')
        }
      } catch (error) {
        toast.error('Failed to fetch subscription details')
        console.error('Error fetching subscription:', error)
      } finally {
        setIsLoading(false)
      }
    }

    if (subscriptionId) {
      fetchSubscriptionData()
    }
  }, [subscriptionId, userId])

  const fetchSubscription = async () => {
    if (!subscriptionId) return

    setIsLoading(true)
    try {
      const response = await fetch(
        `/api/paddle/subscription?subscriptionId=${subscriptionId}&userId=${userId}`
      )
      const data = await response.json()

      if (response.ok) {
        setSubscription(data.subscription)
      } else {
        toast.error(data.error || 'Failed to fetch subscription')
      }
    } catch (error) {
      toast.error('Failed to fetch subscription details')
      console.error('Error fetching subscription:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubscriptionAction = async (
    action: 'cancel' | 'pause' | 'resume'
  ) => {
    if (!subscriptionId) return

    setIsActionLoading(true)
    try {
      const response = await fetch('/api/paddle/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          subscriptionId,
          userId,
        }),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(data.message)
        await fetchSubscription() // Refresh subscription data
      } else {
        toast.error(data.error || `Failed to ${action} subscription`)
      }
    } catch (error) {
      toast.error(`Failed to ${action} subscription`)
      console.error(`Error ${action}ing subscription:`, error)
    } finally {
      setIsActionLoading(false)
    }
  }

  if (!subscriptionId) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Active Subscription</CardTitle>
          <CardDescription>
            You don&apos;t have an active subscription. Choose a plan to get
            started.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading Subscription...</CardTitle>
        </CardHeader>
      </Card>
    )
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Subscription Not Found</CardTitle>
          <CardDescription>
            Unable to load subscription details.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  const formatAmount = (amount: string, currency: string) => {
    const numAmount = parseInt(amount) / 100
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(numAmount)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'text-green-600'
      case 'canceled':
        return 'text-red-600'
      case 'paused':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  const item = subscription.items[0]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Current Subscription
          <span
            className={`text-sm font-medium ${getStatusColor(
              subscription.status
            )}`}
          >
            {subscription.status.toUpperCase()}
          </span>
        </CardTitle>
        <CardDescription>
          Manage your subscription settings and billing
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {item && (
          <div>
            <p className="text-lg font-semibold">
              {formatAmount(
                item.price.unitPrice.amount,
                item.price.unitPrice.currencyCode
              )}
              <span className="text-sm font-normal text-muted-foreground">
                {' '}
                / month
              </span>
            </p>
          </div>
        )}

        {subscription.nextBilledAt && subscription.status === 'active' && (
          <div>
            <p className="text-sm text-muted-foreground">
              Next billing date:{' '}
              {new Date(subscription.nextBilledAt).toLocaleDateString()}
            </p>
          </div>
        )}

        {subscription.canceledAt && (
          <div>
            <p className="text-sm text-muted-foreground">
              Canceled on:{' '}
              {new Date(subscription.canceledAt).toLocaleDateString()}
            </p>
          </div>
        )}

        {subscription.pausedAt && (
          <div>
            <p className="text-sm text-muted-foreground">
              Paused on: {new Date(subscription.pausedAt).toLocaleDateString()}
            </p>
          </div>
        )}

        <div className="flex gap-2 pt-4">
          {subscription.status === 'active' && (
            <>
              <Button
                variant="outline"
                onClick={() => handleSubscriptionAction('pause')}
                disabled={isActionLoading}
                size="sm"
              >
                {isActionLoading ? 'Processing...' : 'Pause'}
              </Button>
              <Button
                variant="destructive"
                onClick={() => handleSubscriptionAction('cancel')}
                disabled={isActionLoading}
                size="sm"
              >
                {isActionLoading ? 'Processing...' : 'Cancel'}
              </Button>
            </>
          )}

          {subscription.status === 'paused' && (
            <Button
              onClick={() => handleSubscriptionAction('resume')}
              disabled={isActionLoading}
              size="sm"
            >
              {isActionLoading ? 'Processing...' : 'Resume'}
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
