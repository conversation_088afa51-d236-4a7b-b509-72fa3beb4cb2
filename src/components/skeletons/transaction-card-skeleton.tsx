import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import Skeleton from '@/components/ui/skeleton'

export default function TransactionCardSkeleton() {
  return (
    <Card className="animate-pulse overflow-hidden border-muted bg-background shadow-md">
      <CardHeader className="bg-muted/40 pb-0">
        <div className="flex items-center gap-2">
          <Skeleton className="h-6 w-32 rounded" />
          <Skeleton className="h-5 w-16 rounded" />
        </div>
        <Skeleton className="mt-2 h-3 w-20 rounded" />
      </CardHeader>
      <CardContent className="py-5">
        <div className="grid grid-cols-2 gap-x-6 gap-y-3">
          <Skeleton className="col-span-1 h-4 w-16 rounded" />
          <Skeleton className="col-span-1 h-4 w-20 justify-self-end rounded" />
          <Skeleton className="col-span-1 h-4 w-16 rounded" />
          <Skeleton className="col-span-1 h-4 w-20 justify-self-end rounded" />
          <Skeleton className="col-span-1 h-4 w-24 rounded" />
          <Skeleton className="col-span-1 h-4 w-16 justify-self-end rounded" />
        </div>
      </CardContent>
    </Card>
  )
}
