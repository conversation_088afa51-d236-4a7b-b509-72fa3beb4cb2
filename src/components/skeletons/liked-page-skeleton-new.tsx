'use client'

import { motion } from 'framer-motion'

import { Card } from '@/components/ui/card'
import Skeleton from '@/components/ui/skeleton'

export function LikedPageSkeleton() {
  return (
    <div className="bg-background">
      <div className="container mx-auto max-w-7xl px-4 py-8">
        {/* Banner Skeleton */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Card className="overflow-hidden rounded-xl border bg-card p-8">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <Skeleton className="mb-6 h-8 w-32 rounded-lg" />
                <div className="mb-4 flex items-center gap-3">
                  <Skeleton className="h-12 w-12 rounded-xl" />
                  <div>
                    <Skeleton className="mb-2 h-8 w-48" />
                    <Skeleton className="h-4 w-64" />
                  </div>
                </div>
              </div>
              <div className="flex flex-col items-end gap-3">
                <Skeleton className="h-8 w-32 rounded-lg" />
                <Skeleton className="h-6 w-24 rounded-lg" />
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Components Grid Skeleton */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="overflow-hidden rounded-xl border bg-card">
                  <Skeleton className="aspect-video w-full" />
                  <div className="p-4">
                    <Skeleton className="mb-2 h-5 w-3/4" />
                    <Skeleton className="mb-3 h-4 w-1/2" />
                    <Skeleton className="mb-3 h-4 w-full" />
                    <div className="flex items-center justify-between">
                      <Skeleton className="h-6 w-16" />
                      <Skeleton className="h-6 w-6 rounded" />
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  )
}
