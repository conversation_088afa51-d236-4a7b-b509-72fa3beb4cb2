import { InspectionPanelIcon } from 'lucide-react'
import { ReactNode } from 'react'

import Heading from '../custom/heading'
// eslint-disable-next-line import/order
import Card from './card'

type Feature = {
  title: string
  desc: string
  icon: ReactNode
}

const Features = () => {
  const features: Feature[] = [
    {
      title: 'Easy to customize layouts',
      desc: 'CopyElement layouts are crafted to be highly user-friendly and versatile, enabling you to easily personalize every component to match your unique vision. Save valuable time and streamline your design workflow with CopyElement’s intuitive customization options, designed to make editing effortless.',
      icon: <InspectionPanelIcon />,
    },
    {
      title: 'Fully Responsive',
      desc: 'CopyElement components are expertly engineered to adapt automatically, ensuring a seamless viewing experience on all screen sizes. Enjoy the confidence that your website will look stunning and function perfectly across any device.',
      icon: <InspectionPanelIcon />,
    },
    {
      title: 'Consistent by design principles',
      desc: 'CopyElement’s design principles ensure a professional, cohesive look with consistent typography and spacing. Our templates bring visual harmony to every project, making each component work seamlessly together. Achieve a polished, unified website effortlessly with CopyElement.',
      icon: <InspectionPanelIcon />,
    },
  ]

  return (
    <section className="custom-screen-lg container relative z-10 mb-[80px] mt-20 h-full ">
      {/* <Image
        alt="bgback"
        src={bgback}
        className="absolute -top-40 left-0 opacity-60"
      /> */}
      <div className="mx-auto max-w-3xl space-y-4 px-3 text-center md:px-0">
        <Heading>Elementor Template & Component Library</Heading>
        <p className="text-zinc-400">
          Being an agency we know that time is money, so we created the
          Elementor Template & Component Library to streamline the building
          process. Search from hundreds of components, copy with one click and
          paste them into your Elementor project.
        </p>
      </div>
      <ul className="custom-screen-lg mx-auto mt-8 grid-cols-2 gap-6 space-y-6 sm:grid sm:space-y-0 lg:grid-cols-3">
        {features.map((item: Feature, key: number) => (
          <Card
            key={key}
            icon={item.icon}
            title={item.title}
            desc={item.desc}
          />
        ))}
      </ul>
    </section>
  )
}

export default Features
