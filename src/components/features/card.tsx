'use client'

import { ChevronRight } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import React, { ReactNode, useRef } from 'react'
// import featureCover from 'public/feature-cover.svg'

// import Link from 'next/link'

import { Button } from '../ui/button'

const Card = ({
  icon,
  title,
  desc,
}: {
  icon: ReactNode
  title: string
  desc: string
}) => {
  const cardRef = useRef<HTMLLIElement>(null)
  const shadowRef = useRef<HTMLDivElement>(null)

  const handleMouseMove = (e: React.MouseEvent<HTMLLIElement>) => {
    if (cardRef.current) {
      const { left, top } = cardRef.current.getBoundingClientRect()
      const x = e.clientX - left // x position within the element.
      const y = e.clientY - top // y position within the element.

      if (shadowRef.current) {
        shadowRef.current.style.top = `${y}px`
        shadowRef.current.style.left = `${x}px`
        shadowRef.current.style.transform = 'translate(-50%, -50%)'
        ;(cardRef.current as any).style =
          `--cursor-x: ${x}px; --cursor-y: ${y}px`
      }
    }
  }

  return (
    <li
      ref={cardRef}
      onMouseMove={handleMouseMove}
      className="group relative z-20 h-full overflow-hidden rounded-xl border border-zinc-800 bg-[radial-gradient(500px_circle_at_var(--cursor-x)_var(--cursor-y),#6d22ee_0,transparent,transparent_70%)]"
    >
      <div className="relative z-10 h-full space-y-4 bg-[linear-gradient(180deg,_rgba(24,_24,_27,_0.00)_0%,_rgba(24,_24,_27,_0.00)_100%)] p-5 pb-6">
        <div className="flex h-12 w-12 items-center justify-center rounded-full border border-zinc-700 bg-[linear-gradient(180deg,_rgba(39,_39,_42,_0.68)_0%,_rgba(39,_39,_42,_0.00)_100%)] text-gray-500">
          {icon}
        </div>
        <h3 className="font-geist  pt-2 text-left text-xl font-normal uppercase text-zinc-100 md:text-2xl lg:text-2xl">
          {title}
        </h3>
        <p className="text-zinc-300">{desc}</p>
        <Link
          prefetch={true}
          href="/components"
          // variant="default"
          className=" absolute bottom-3 right-3"
          // className="bg-page-gradient group mr-auto inline-flex w-fit items-center rounded-lg border   border-input bg-purple-950  py-3 transition-colors hover:bg-transparent/90"
        >
          <Button variant={'link'}>
            Explore more
            <ChevronRight className="ml-2 h-4 w-4 duration-300 group-hover:translate-x-1" />
          </Button>
          {/* Explore more */}
          {/* <ChevronRight className="ml-2 h-4 w-4 duration-300 group-hover:translate-x-1" /> */}
        </Link>
        <div>
          <Image
            width={100}
            height={80}
            src={'feature-cover.svg'}
            alt="FarmUI UI"
            className="absolute inset-0 -z-10 h-full w-full"
          />
        </div>
      </div>
      <div
        ref={shadowRef}
        className="absolute left-0 top-0 h-4/5 w-4/5 bg-[linear-gradient(180deg,_#1E293B_0%,_rgba(200,130,_246,_0.00)_137.53%,_rgba(32,_69,_129,_0.00)_195%)] opacity-0 blur-[70px] duration-150 group-hover:opacity-90"
      />
      <div className="absolute inset-[1px] -z-10 rounded-xl bg-gradient-to-tr from-black/90 via-transparent/80 to-transparent/20" />
    </li>
  )
}

export default Card
