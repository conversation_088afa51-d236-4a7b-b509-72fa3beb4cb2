'use client'

import { cn } from '@/lib/utils'

import { ContainerTextFlip } from '../ui/container-text-flip'

const Elevate = () => {
  return (
    <section className="relative my-10 flex w-full items-center justify-center overflow-hidden bg-transparent px-4">
      {/* Main content container */}
      <div className="relative z-10 text-center">
        <h2 className="text-center font-heading text-4xl font-medium md:text-5xl lg:text-7xl">
          <span className="bg-gradient-to-b from-black to-black/70 bg-clip-text text-transparent dark:from-white dark:to-white/70">
            Components, Built for{' '}
          </span>

          <ContainerTextFlip
            words={['Community', 'Clients', 'Yourself']}
            className={cn(
              'mx-auto mt-4 block',
              '[background:linear-gradient(to_bottom,#9333ea,#6b21a8)]', // Purple gradient for light mode
              'dark:[background:linear-gradient(to_bottom,#a855f7,#7e22ce)]', // Brighter purple gradient for dark mode
              'shadow-[inset_0_-1px_#6b21a8,inset_0_0_0_1px_#9333ea,_0_4px_12px_rgba(147,51,234,0.5)]',
              'dark:shadow-[inset_0_-1px_#7e22ce,inset_0_0_0_1px_#a855f7,_0_4px_12px_rgba(168,85,247,0.5)]'
            )}
            textClassName={cn('font-semibold', 'text-white')}
          />
        </h2>
      </div>

      {/* Background grid effect */}
      <RetroGrid />
    </section>
  )
}

function RetroGrid({ className }: { className?: string }) {
  return (
    <div
      className={cn(
        'pointer-events-none absolute inset-0 overflow-hidden opacity-50 [perspective:200px]',
        className
      )}
    >
      {/* Rotated grid container */}
      <div className="absolute inset-0 [transform:rotateX(35deg)]">
        <div
          className={cn(
            'animate-grid',
            '[background-repeat:repeat] [background-size:120px_120px] [height:300vh] [inset:0%_0px] [margin-left:-50%] [transform-origin:100%_0_0] [width:600vw]',
            '[background-image:linear-gradient(to_right,rgba(0,0,0,0.2)_1px,transparent_0),linear-gradient(to_bottom,rgba(0,0,0,0.3)_1px,transparent_0)]',
            'dark:[background-image:linear-gradient(to_right,rgba(255,255,255,0.25)_1px,transparent_0),linear-gradient(to_bottom,rgba(255,255,255,0.2)_1px,transparent_0)]'
          )}
        />
      </div>

      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-background via-background/80 to-transparent" />
    </div>
  )
}

export default Elevate
