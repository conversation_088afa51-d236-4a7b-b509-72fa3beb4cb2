'use client'

import Image from 'next/image'

import { GlowingEffect } from '@/components/ui/glowing-effect'
import { featureCards, type FeatureCardProps } from '@/utils/constants/features'

import Heading from '../custom/heading'

export function NewFeatures() {
  return (
    <section className="custom-screen-lg container relative z-10 mb-[80px] mt-20 h-full ">
      <div className="mx-auto mb-8 max-w-3xl space-y-4 px-3 text-center md:px-0">
        <Heading>Elementor Template & Component Library</Heading>
        <p className="text-zinc-400">
          Being an agency we know that time is money, so we created the
          Elementor Template & Component Library to streamline the building
          process. Search from hundreds of components, copy with one click and
          paste them into your Elementor project.
        </p>
      </div>
      <ul className="grid grid-cols-1 grid-rows-none gap-6  md:grid-cols-12 md:grid-rows-4  lg:gap-8 xl:grid-rows-3">
        {featureCards.map((feature, index) => (
          <GridItem
            key={index}
            area={feature.area}
            icon={
              <feature.icon className="h-4 w-4 text-black dark:text-neutral-400" />
            }
            title={feature.title}
            description={feature.description}
            image={feature.image}
          />
        ))}
      </ul>
    </section>
  )
}

const GridItem = ({
  area,
  icon,
  title,
  description,
  image,
}: FeatureCardProps) => {
  return (
    <li className={`min-h-[26rem] list-none ${area}`}>
      <div className="relative h-full rounded-2xl border p-2 md:rounded-3xl md:p-3">
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
        />
        <div className="border-0.75 relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 dark:shadow-[0px_0px_27px_0px_#2D2D2D] md:p-6">
          <div className="relative flex flex-1 flex-col justify-between gap-3">
            {/* <div className="w-fit rounded-lg border border-gray-600 p-2">
              {icon}
            </div> */}
            {/* Image */}

            <div className="relative h-full w-full overflow-hidden rounded-lg">
              <Image
                src={image}
                alt={title}
                fill
                className="h-full w-full object-cover"
              />
            </div>

            <div className="space-y-3">
              <h3 className="-tracking-4 text-balance pt-0.5 font-sans text-xl/[1.375rem] font-semibold text-black dark:text-white md:text-2xl/[1.875rem]">
                {title}
              </h3>
              <h2 className="font-sans text-sm/[1.125rem] text-black dark:text-neutral-400 md:text-base/[1.375rem] [&_b]:md:font-semibold [&_strong]:md:font-semibold">
                {description}
              </h2>
            </div>
          </div>
        </div>
      </div>
    </li>
  )
}
