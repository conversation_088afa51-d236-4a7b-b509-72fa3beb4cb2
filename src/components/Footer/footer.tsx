'use client'
import { DiscordLogoIcon } from '@radix-ui/react-icons'
import { InstagramIcon, LinkedinIcon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

import { excludesUrls } from '@/utils/constants/nav'

import { Badge } from '../ui/badge'
import { SocialIcon } from '../ui/social-icon'
import { TextHoverEffect } from '../ui/text-hover-effect'

const FooterCategories = ['Free', 'Pro', 'Popular']

type Props = {
  name: string
  link: string
  external?: boolean
  badge?: string
}

const FooterLink = ({ name, link, external, badge }: Props) => {
  return (
    <li>
      {external ? (
        <a
          href={link}
          target="_blank"
          rel="noreferrer"
          className="text-sm  text-muted-foreground  transition-colors duration-300 hover:text-purple-500 hover:underline dark:hover:text-purple-400 md:text-base"
        >
          {name}{' '}
          {badge && (
            <Badge variant="hot" className=" -translate-y-3 rounded-lg ">
              {badge}
            </Badge>
          )}
        </a>
      ) : (
        <Link
          prefetch={true}
          href={link}
          className="text-sm  text-muted-foreground  transition-colors duration-300 hover:text-purple-500 hover:underline dark:hover:text-purple-400 md:text-base"
        >
          {badge && (
            <Badge variant="secondary" className="ml-2 rounded-lg bg-primary">
              {badge}
            </Badge>
          )}{' '}
          {name}
        </Link>
      )}
    </li>
  )
}

const Footer = () => {
  const pathname = usePathname()
  // console.log("pathname", pathname);
  // const footerRef = useRef()

  // useEffect(() => {
  //   if (footerRef?.current) {
  //     if (excludesUrls?.includes(pathname)) {
  //       footerRef.current.style.display = 'none'
  //     } else {
  //       footerRef.current.style.display = 'block'
  //     }
  //   }
  // }, [pathname, footerRef])

  if (excludesUrls?.some((url) => pathname.startsWith(url))) {
    // console.log('excluded')
    return
  }

  return (
    <footer
      //ref={footerRef}
      className="relative mt-20 border-t border-gray-900 bg-white pt-10 dark:bg-black sm:pt-16 lg:pt-24"
    >
      {/* creating gradient effect */}
      <div className="absolute inset-0 h-full w-screen bg-black">
        <Image
          // src={'/background3.webp'}
          // src="https://gallery.theportfolio.in/background-images/1750953291767-section-bg-2.webp"
          src="https://gallery.theportfolio.in/background-images/1750953293834-section-bg-13.webp"
          className="relative inset-0 h-full w-full"
          width={1600}
          height={900}
          alt="background"
        />
      </div>
      <div className="absolute inset-0  h-full w-full items-center bg-gradient-to-r from-black/80 to-black/60 px-5  py-24" />
      {/* background effect ends */}

      {/* <div className="absolute inset-0  h-full w-full items-center px-5 py-24 [background:radial-gradient(125%_125%_at_50%_10%,#fff_40%,#63e_100%)] dark:[background:radial-gradient(125%_125%_at_50%_10%,#000_40%,#63e_100%)]"></div> */}

      <div className="relative z-0 mx-auto max-w-7xl px-5 sm:px-6 lg:px-8 ">
        <div className="grid grid-cols-2 gap-x-8 gap-y-12 md:grid-cols-4 lg:grid-cols-12 xl:gap-x-12">
          <div className="col-span-2 md:col-span-4 xl:pr-8">
            <Image
              className="rounded-lg"
              width={300}
              height={200}
              // src="https://utfs.io/f/LhUzHyAuqPAKtGs8uAzbrMeFkTElZHgXis9x5fGAdyCW7zjN"
              src="https://utfs.io/f/LhUzHyAuqPAKT1NfdlSHcPsEJOpX4SgZ9bfFVuR0l82QnyMG"
              alt="Theportfolyo Logo"
            />
            <p className="mb-5 mt-2 text-base leading-relaxed text-muted-foreground dark:text-muted-foreground">
              Stop designing Elementor websites from scratch. Build your next
              site with World&apos;s first Elementor Component Library in
              minutes with our perfectly designed components.
            </p>
            {/* <Button
              className="mt-5 gap-1 rounded-lg font-bold"
              size={'lg'}
              asChild
            >
              <Link href={`/contact-us`} title="">
                <svg
                  className="h-6 w-6"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                  />
                </svg>
                Contact
              </Link>
            </Button> */}

            <a
              href="https://www.producthunt.com/products/copy-element/reviews?utm_source=badge-product_rating&utm_medium=badge&utm_source=badge-copy-element"
              target="_blank"
            >
              <img
                src="https://api.producthunt.com/widgets/embed-image/v1/product_rating.svg?product_id=593318&theme=light"
                alt="Copy Element - World's First Elementor Templates & Component Library | Product Hunt"
                style={{ width: 242, height: 108 }}
                width={242}
                height={108}
              />
            </a>
          </div>
          <div className="lg:col-span-2">
            <p className="font-heading text-lg font-semibold text-muted-foreground underline dark:dark:text-slate-50 md:text-xl">
              Company
            </p>
            <ul className="mt-6 space-y-5">
              <FooterLink name={"FAQ's"} link={'/faq'} />
              <FooterLink name={'Components'} link={'/components'} />
              <FooterLink name={'Templates'} link={'/templates'} />
              <FooterLink
                name={'Affiliate'}
                link={'https://tally.so/r/w8WdRz'}
                external
                badge="NEW"
              />
              <FooterLink
                name={'Blogs'}
                link={'https://blog.copyelement.com/'}
                external
              />
              <FooterLink
                name={'Change Log'}
                link={'https://changelog.copyelement.com/'}
                external
                badge="NEW"
              />
              {/* <FooterLink name={"Works"} link={"#"} /> */}
              {/* <FooterLink name={"Career"} link={"/"} /> */}
            </ul>
          </div>
          <div className="lg:col-span-2">
            <p className="text-lg font-semibold text-muted-foreground underline dark:dark:text-slate-50 md:text-xl">
              Help
            </p>
            <ul className="mt-6 space-y-4">
              <FooterLink name={'Customer Support'} link={'/contact-us'} />
              {/* <FooterLink name={"Delivery Details"} link={"#"} /> */}
              <FooterLink
                name={'Terms & Conditions'}
                link={'/terms-and-conditions'}
              />
              <FooterLink name={'Privacy Policy'} link={'/privacy-policy'} />
              <FooterLink
                name={'Cancellation & Refunds'}
                link={'/cancellation-and-refunds'}
              />
            </ul>
          </div>
          <div className="lg:col-span-2">
            <p className="text-lg font-semibold text-muted-foreground underline dark:dark:text-slate-50 md:text-xl">
              Categories
            </p>
            <ul className="mt-6 space-y-5">
              <FooterLink name={'All Components'} link={`/components`} />

              {FooterCategories?.map((cat, i) => (
                <FooterLink
                  key={i}
                  name={cat}
                  link={`/components/${cat?.toLowerCase()}`}
                />
              ))}
            </ul>
          </div>
          <div className="lg:col-span-2">
            <p className="text-lg font-semibold text-muted-foreground underline dark:dark:text-slate-50 md:text-xl">
              Extra Links
            </p>
            <ul className="mt-6 space-y-5">
              <FooterLink name={'Customer Support'} link={'/contact'} />

              <FooterLink name={'Privacy Policy'} link={'/privacy-policy'} />
              <FooterLink name={'About Us'} link={'/about-us'} />
              <FooterLink name={'Sitemap'} link={'/sitemap.xml'} />

              {/* <FooterLink name={"+91-9507250821"} link={"tel:+9507250821"} /> */}
            </ul>
          </div>
        </div>

        {/* <hr className="mb-10 mt-16 border-card" /> */}
        <hr className="mb-10 mt-16" />

        <div className="pb-16 sm:flex sm:items-center sm:justify-between md:pb-0">
          <p className="flex flex-col gap-2 text-center text-sm text-muted-foreground  sm:flex-row md:text-start ">
            © Copyright {new Date()?.getFullYear()}, All Rights Reserved by{' '}
            <Link
              href={`/`}
              prefetch={true}
              className="cursor-pointer text-muted-foreground underline dark:text-slate-100"
            >
              {' '}
              CopyElement{' '}
            </Link>
          </p>
          <ul className="mt-5 flex items-center justify-center space-x-3 sm:mt-0 md:order-3 md:justify-end">
            <li>
              <SocialIcon
                platform="instagram"
                href="https://instagram.com/copyelement"
                icon={<InstagramIcon className="size-3 p-0.5 " />}
              />
            </li>
            <li>
              <SocialIcon
                platform="linkedin"
                href="https://linkedin.com/company/copyelement"
                icon={<LinkedinIcon className="size-3 p-0.5" />}
              />
            </li>
            <li>
              <SocialIcon
                platform="discord"
                href="https://discord.gg/tzmn3SKGfc"
                icon={<DiscordLogoIcon className="size-4" />}
              />
            </li>
          </ul>
        </div>
      </div>

      <div className="relative z-0 hidden h-[20rem] w-full items-center justify-center md:flex lg:h-[20rem] ">
        <TextHoverEffect text="COPYELEMENT" />
      </div>
    </footer>
  )
}

export default Footer
