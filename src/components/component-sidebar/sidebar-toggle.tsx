'use client'

import { ChevronLeftIcon } from 'lucide-react'
import { useState } from 'react'

import { Button } from '@/components/ui/button'

export default function SidebarToggle() {
  const [isCollapsed, setIsCollapsed] = useState(false)

  return (
    <Button
      onClick={() => setIsCollapsed(!isCollapsed)}
      size="icon"
      variant="outline"
      className="absolute -right-5 top-1/2 z-50 hidden rounded-full md:inline-flex"
    >
      <ChevronLeftIcon
        className={`h-5 w-5 ${isCollapsed ? 'rotate-180' : ''}`}
      />
    </Button>
  )
}
