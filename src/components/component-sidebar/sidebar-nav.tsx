'use client'

import { useEffect, useState } from 'react'

import Nav from '@/components/custom/nav'
import { useSidebarNav } from '@/hooks/useSidebarNav'

interface SidebarNavProps {
  links: Array<{
    title: string
    href: string
    label?: string
  }>
}

export default function SidebarNav({ links }: SidebarNavProps) {
  const { navOpened } = useSidebarNav()
  const [filteredLinks, setFilteredLinks] = useState(links)

  useEffect(() => {
    if (navOpened) {
      document.body.classList.add('overflow-hidden')
    } else {
      document.body.classList.remove('overflow-hidden')
    }
  }, [navOpened])

  const handleSearch = (query: string) => {
    if (!query) {
      setFilteredLinks(links)
      return
    }

    const filtered = links.filter((link) =>
      link.title.toLowerCase().includes(query.toLowerCase())
    )
    setFilteredLinks(filtered)
  }

  return (
    <>
      <div
        onClick={() => useSidebarNav.getState().setNavOpened(false)}
        className={`absolute inset-0 transition-[opacity] delay-100 duration-700 ${
          navOpened ? 'h-svh opacity-50' : 'h-0 opacity-0'
        } w-full bg-gradient-to-br from-primary/60 to-primary/30 md:hidden`}
      />

      <Nav
        id="sidebar-menu"
        className={`z-40 h-full flex-1 overflow-auto ${
          navOpened ? 'max-h-screen' : 'max-h-0 py-0 md:max-h-screen md:py-2'
        }`}
        closeNav={() => useSidebarNav.getState().setNavOpened(false)}
        isCollapsed={false}
        isLoading={false}
        links={filteredLinks}
        onSearch={handleSearch}
      />
    </>
  )
}
