'use client'

import { useQuery } from '@tanstack/react-query'
import { MenuIcon, XIcon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

import { Layout } from '@/components/custom/layout'
import { UserNav } from '@/components/layout/user-nav'
import { Button } from '@/components/ui/button'
import MagicBadge from '@/components/ui/magic-badge'
import { useSidebarNav } from '@/hooks/useSidebarNav'
import { useProfileStore } from '@/stores/profile-store'
import { createClient } from '@/utils/supabase/client'

export default function SidebarHeader() {
  const { navOpened, toggleNav } = useSidebarNav()
  const supabase = createClient()

  const { isLoading: userLoading, data: user } = useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const { data } = await supabase.auth.getUser()
      return data
    },
  })

  const { profile } = useProfileStore()

  return (
    <Layout.Header
      sticky
      className="z-50 flex justify-between px-4 py-1 shadow-sm md:px-4 md:py-3"
    >
      <Link prefetch={true} href={'/'} className="flex items-center gap-2">
        <div className="visible flex w-auto flex-col justify-end truncate">
          <Image
            className="rounded-lg"
            width={80}
            height={20}
            src="https://utfs.io/f/LhUzHyAuqPAKUSSOY3WlNcqwD9Fjd5n0CPIg1bEmyO3u7oRA"
            alt="CopyElement Logo"
          />
        </div>
      </Link>

      <div className="flex items-center justify-end gap-2">
        <div className="flex gap-3 md:hidden">
          {!profile?.is_pro && (
            <Link prefetch={true} aria-label="Login button" href="/pricing">
              <MagicBadge title="✨ Get PRO" />
            </Link>
          )}
          {user ? (
            <UserNav />
          ) : (
            <Button asChild aria-label="Login" size={'sm'}>
              <Link prefetch={true} aria-label="Login button" href="/login">
                Login
              </Link>
            </Button>
          )}
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden"
          aria-label="Toggle Navigation"
          aria-controls="sidebar-menu"
          aria-expanded={navOpened}
          onClick={toggleNav}
        >
          {navOpened ? <XIcon /> : <MenuIcon />}
        </Button>
      </div>
    </Layout.Header>
  )
}
