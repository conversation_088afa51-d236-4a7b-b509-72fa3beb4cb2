'use client'

import { MenuIcon, XIcon, ChevronLeftIcon } from 'lucide-react'
import { useState, useEffect } from 'react'

import { Button } from '@/components/ui/button'

interface SidebarInteractivityProps {
  isCollapsed: boolean
  toggle: () => void
}

export function SidebarInteractivity({
  isCollapsed,
  toggle,
}: SidebarInteractivityProps) {
  const [navOpened, setNavOpened] = useState(false)

  useEffect(() => {
    if (navOpened) {
      document.body.classList.add('overflow-hidden')
    } else {
      document.body.classList.remove('overflow-hidden')
    }
  }, [navOpened])

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="md:hidden"
        aria-label="Toggle Navigation"
        aria-controls="sidebar-menu"
        aria-expanded={navOpened}
        onClick={() => setNavOpened((prev) => !prev)}
      >
        {navOpened ? <XIcon /> : <MenuIcon />}
      </Button>

      <Button
        onClick={toggle}
        size="icon"
        variant="outline"
        className="absolute -right-5 top-1/2 z-50 hidden rounded-full md:inline-flex"
      >
        <ChevronLeftIcon
          className={`h-5 w-5 ${isCollapsed ? 'rotate-180' : ''}`}
        />
      </Button>

      {/* Overlay in mobile */}
      <div
        onClick={() => setNavOpened(false)}
        className={`absolute inset-0 transition-[opacity] delay-100 duration-700 ${
          navOpened ? 'h-svh opacity-50' : 'h-0 opacity-0'
        } w-full bg-black md:hidden`}
      />
    </>
  )
}
