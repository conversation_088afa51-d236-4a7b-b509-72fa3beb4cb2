import { fetchCategories } from '@/actions/server-categories'
import { Layout } from '@/components/custom/layout'

import SidebarHeader from './sidebar-header'
import SidebarNav from './sidebar-nav'

type SidebarProps = React.HTMLAttributes<HTMLElement> & {
  isTemplate?: boolean
}

export default async function ComponentSidebar({
  className,
  isTemplate = false,
}: SidebarProps) {
  const categories = await fetchCategories(isTemplate)

  const constantLinks = [
    {
      title: 'All',
      href: isTemplate ? '/templates' : '/components',
    },
    {
      title: 'Free',
      href: isTemplate ? '/templates/free' : '/components/free',
    },
    {
      title: 'Pro',
      href: isTemplate ? '/templates/pro' : '/components/pro',
      label: 'Premium',
      labelVariant: 'pro',
    },
    {
      title: 'Popular',
      href: isTemplate ? '/templates/popular' : '/components/popular',
    },
  ]

  // Add WireFrames link only when isTemplate is false
  if (!isTemplate) {
    constantLinks.push({
      title: 'WireFrames',
      href: '/components/wireframes',
      label: 'New',
      labelVariant: 'new',
    })
  }

  const transformedCategories = categories
    ? [
        ...constantLinks,
        ...categories.map(({ name, slug }) => ({
          title: name,
          href: isTemplate ? `/templates/${slug}` : `/components/${slug}`,
        })),
      ]
    : constantLinks

  return (
    <aside className="md:h-svh fixed left-0 right-0 top-0 z-50 w-full border-r-2 border-r-muted transition-[width] md:bottom-0 md:right-auto md:w-64">
      <Layout fixed>
        <SidebarHeader />
        <SidebarNav links={transformedCategories} />
      </Layout>
    </aside>
  )
}
