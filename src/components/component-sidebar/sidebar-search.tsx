'use client'

import { useState } from 'react'

import { Input } from '@/components/ui/input'

interface SidebarSearchProps {
  onSearch: (query: string) => void
}

export default function SidebarSearch({ onSearch }: SidebarSearchProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
    onSearch(query)
  }

  return (
    <Input
      placeholder="Search categories..."
      className="w-full rounded-none"
      type="search"
      value={searchQuery}
      onChange={handleSearch}
    />
  )
}
