'use client'

import { Eye, EyeOff } from 'lucide-react'
import { useState, useTransition } from 'react'
import { toast } from 'sonner'

import { registerAction } from '@/actions/auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

const RegisterForm = () => {
  const [showPassword, setShowPassword] = useState(false)
  const [isPending, startTransition] = useTransition()

  async function handleSubmit(formData: FormData) {
    startTransition(async () => {
      const result = await registerAction(formData)
      if (result?.error) {
        toast.error(result.error)
      } else {
        toast.success(
          'Registration successful! Please check your email to verify your account.'
        )
      }
    })
  }

  return (
    <form action={handleSubmit} className="space-y-6">
      <div className="flex w-full items-center justify-center gap-2">
        <div className="flex-1 space-y-2">
          <Label htmlFor="first_name" className="text-white">
            First Name
          </Label>
          <Input
            id="first_name"
            name="first_name"
            type="text"
            placeholder="John"
            required
            minLength={2}
            className="bg-white/10 text-white placeholder:text-gray-400"
          />
        </div>
        <div className="flex-1 space-y-2">
          <Label htmlFor="last_name" className="text-white">
            Last Name
          </Label>
          <Input
            id="last_name"
            name="last_name"
            type="text"
            placeholder="Doe"
            required
            minLength={2}
            className="bg-white/10 text-white placeholder:text-gray-400"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email" className="text-white">
          Email
        </Label>
        <Input
          id="email"
          name="email"
          type="email"
          placeholder="Enter your email"
          required
          className="bg-white/10 text-white placeholder:text-gray-400"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="password" className="text-white">
          Password
        </Label>
        <div className="relative">
          <Input
            id="password"
            name="password"
            type={showPassword ? 'text' : 'password'}
            placeholder="Enter your password"
            required
            minLength={8}
            className="bg-white/10 text-white placeholder:text-gray-400"
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white"
          >
            {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
          </button>
        </div>
      </div>

      <Button type="submit" className="w-full" disabled={isPending}>
        {isPending ? 'Signing up...' : 'Sign up'}
      </Button>
    </form>
  )
}

export default RegisterForm
