'use client'

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import * as z from 'zod'

import { getProfile, updateUser } from '@/actions/users'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Heading } from '@/components/ui/heading'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { UploadButton } from '@/types/uploadthing'

// Zod schema for form validation
const formSchema = z.object({
  avatar_url: z.string().url().nullable(),
  email: z.string().email(),
  first_name: z
    .string()
    .min(2, 'First name must be at least 2 characters')
    .nullable(),
  last_name: z
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .nullable(),
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .nullable(),
  is_pro: z.boolean().default(false),
  role: z.enum(['admin', 'editor', 'user']).default('user'),
})

type UserProfileFormValues = z.infer<typeof formSchema>

export default function ProfileForm() {
  const params = useParams<{ userId: string }>()
  const router = useRouter()
  const { userId } = params
  const [isUploading, setIsUploading] = useState(false)

  // Fetch user data using useQuery
  const {
    isLoading,
    error,
    data: user,
  } = useQuery({
    queryKey: ['profile', userId],
    queryFn: () => getProfile(userId),
    enabled: userId !== 'new',
  })

  // Initialize form with react-hook-form and zod validation
  const form = useForm<UserProfileFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      avatar_url: '',
      email: '',
      first_name: '',
      last_name: '',
      username: '',
      is_pro: false,
      role: 'user',
    },
  })

  // When user data is fetched, reset the form values
  useEffect(() => {
    if (user) {
      form.reset({
        avatar_url: user.avatar_url || '',
        email: user.email || '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        username: user.username || '',
        is_pro: user.is_pro || false,
        role: user.role || 'user',
      })
    }
  }, [user, form])

  // Mutation for updating user profile
  const mutation = useMutation({
    mutationFn: (data: UserProfileFormValues) => updateUser(userId, data),
    onSuccess: () => {
      toast.success('Profile updated successfully')
      router.push('/dashboard/users')
    },
    onError: () => {
      toast.error('Failed to update profile')
    },
  })

  // Handle form submission
  const onSubmit = (data: UserProfileFormValues) => {
    mutation.mutate(data)
  }

  // Get initials for avatar fallback
  const getInitials = () => {
    const firstName = form.getValues('first_name') || ''
    const lastName = form.getValues('last_name') || ''
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || '?'
  }

  // Handle loading and error states
  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex h-full flex-col items-center justify-center gap-2">
        <p className="text-destructive">Error: {(error as Error).message}</p>
        <Button
          variant="outline"
          onClick={() => router.push('/dashboard/users')}
        >
          Go Back
        </Button>
      </div>
    )
  }

  return (
    <ScrollArea className="h-full">
      <div className="flex-1 space-y-4 p-5 md:p-8">
        <div className="flex items-center justify-between">
          <Heading
            title={userId === 'new' ? 'Create User' : 'Edit User Profile'}
            description={
              userId === 'new'
                ? 'Add a new user to the system'
                : 'Make changes to user profile'
            }
          />
        </div>
        <Separator />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Avatar Upload */}
            <FormField
              control={form.control}
              name="avatar_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Profile Picture</FormLabel>
                  <div className="flex items-center gap-4">
                    <Avatar className="h-20 w-20">
                      <AvatarImage
                        src={field.value || ''}
                        alt="Profile picture"
                      />
                      <AvatarFallback className="text-lg">
                        {isUploading ? (
                          <Loader2 className="h-6 w-6 animate-spin" />
                        ) : (
                          getInitials()
                        )}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col gap-2">
                      <UploadButton
                        className="ut-button:bg-primary ut-button:text-primary-foreground ut-button:hover:bg-primary/90"
                        endpoint="imageUploader"
                        onUploadBegin={() => setIsUploading(true)}
                        onClientUploadComplete={(res) => {
                          setIsUploading(false)
                          const data: any = res
                          if (data) {
                            form.setValue('avatar_url', data[0].url)
                            toast.success(
                              'Profile picture uploaded successfully'
                            )
                          }
                        }}
                        onUploadError={(error: Error) => {
                          setIsUploading(false)
                          toast.error(`Upload failed: ${error.message}`)
                        }}
                      />
                      <p className="text-xs text-muted-foreground">
                        Supported formats: JPG, PNG, GIF (max 4MB)
                      </p>
                    </div>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Card className="p-6">
              <div className="grid gap-6 md:grid-cols-2">
                {/* Username */}
                <FormField
                  control={form.control}
                  name="username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Username</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="johndoe"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          required
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* First Name */}
                <FormField
                  control={form.control}
                  name="first_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="John"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Last Name */}
                <FormField
                  control={form.control}
                  name="last_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Doe"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Role */}
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a role" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="user">User</SelectItem>
                          <SelectItem value="editor">Editor</SelectItem>
                          <SelectItem value="admin">Admin</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        User role determines permissions in the system
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Is Pro Checkbox */}
                <FormField
                  control={form.control}
                  name="is_pro"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Pro User</FormLabel>
                        <FormDescription>
                          Pro users have access to premium features and content
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </Card>

            {/* Submit and Cancel Buttons */}
            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push('/dashboard/users')}
                disabled={mutation.isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Changes'
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </ScrollArea>
  )
}
