'use client'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQuery } from '@tanstack/react-query'
import { Trash } from 'lucide-react'
import Image from 'next/image'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import slugify from 'react-slugify'
import { toast } from 'sonner'
import * as z from 'zod'

import { fetchCategories } from '@/actions/categories'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Heading } from '@/components/ui/heading'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Textarea } from '@/components/ui/textarea'
import { Template } from '@/types/custom'
import { UploadButton } from '@/types/uploadthing'
import { createClient } from '@/utils/supabase/client'

export const IMG_MAX_LIMIT = 3

const formSchema = z.object({
  name: z.string().min(3, { message: ' Name must be at least 3 characters' }),
  // slug: z.string().min(1, { message: 'Please enter a slug.' }),
  slug: z.string(),
  image_url: z.string().min(1, { message: 'Please enter an image url.' }),
  // image_url: z
  //   .array(ImgSchema)
  //   .max(IMG_MAX_LIMIT, { message: 'You can only add up to 3 images' })
  //   .min(1, { message: 'At least one image must be added.' }),
  description: z
    .string()
    .min(3, { message: 'Template description must be at least 3 characters' }),
  code: z.string().min(1, { message: 'Please enter the template code.' }),
  is_pro: z.boolean(),
  tags: z.string().min(1, { message: 'Please enter at least one tag.' }),
  likes: z.number().min(0, { message: 'Likes must be a positive number' }),
  meta_desc: z.string().min(1, { message: 'Please enter a meta description.' }),
  category_id: z.string().min(1, { message: 'Please select a category' }),
})

type ComponentFormValues = z.infer<typeof formSchema>

interface ComponentFormProps {
  initialData: Template | any
}

export const TemplateForm: React.FC<ComponentFormProps> = ({ initialData }) => {
  const params = useParams()
  const router = useRouter()

  const [open, setOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [imgLoading, setImgLoading] = useState(false)
  const title = initialData ? 'Edit template' : 'Create template'
  const description = initialData ? 'Edit a template.' : 'Add a new template'
  const toastMessage = initialData ? 'Template updated.' : 'Template created.'
  const action = initialData ? 'Save changes' : 'Create'

  const {
    isLoading,
    error,
    data: categories,
  } = useQuery({
    queryKey: ['categories'],
    queryFn: fetchCategories,
  })

  const defaultValues = initialData
    ? initialData
    : {
        name: '',
        slug: '',
        image_url: '',
        code: '',
        description: '',
        is_pro: false,
        tags: '',
        meta_desc: '',
        category_id: '',
        likes: 0,
      }

  const form = useForm<ComponentFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  })

  const onSubmit = async (data: ComponentFormValues) => {
    const supabase = createClient()
    try {
      setLoading(true)
      if (initialData) {
        // await axios.post(`/api/Components/edit-component/${initialData._id}`, data);
        const { data: component, error } = await supabase
          .from('templates')
          .update({
            id: initialData.id,
            name: data.name,
            slug: data.slug ? slugify(data.slug) : slugify(data.name),
            image_url: data.image_url,
            code: data.code,
            description: data.description,
            is_pro: data.is_pro,
            tags: data.tags,
            meta_desc: data.meta_desc,
            category_id: data.category_id,
            likes: data.likes,
          })
          .eq('id', initialData.id)
          .select()

        console.log('template', component)
        console.error('error', error)
        if (component) {
          router.refresh()
          router.push(`/dashboard/templates`)
          toast.success('success', {
            description: 'Template Updated!.',
          })
        } else {
          toast.error(error.message ?? 'Something went wrong while updating!')
        }
      } else {
        // const res = await axios.post(`/api/components/create-component`, data);
        // console.log("component", res);
        const { data: component, error } = await supabase
          .from('templates')
          .insert({
            name: data.name,
            slug: data.slug ? slugify(data.slug) : slugify(data.name),
            image_url: data.image_url,
            code: data.code,
            description: data.description,
            is_pro: data.is_pro,
            tags: data.tags,
            meta_desc: data.meta_desc,
            category_id: data.category_id,
            likes: data.likes,
          })
          .select()
        // console.log('component', component)
        // console.error('error', error)

        if (component) {
          router.refresh()
          router.push(`/dashboard/templates`)
          toast.success('success', {
            description: 'Template created!.',
          })
        } else {
          toast.error(error.message ?? 'Something went wrong while creating!')
        }
      }
    } catch (error: any) {
      toast.error('Uh oh! Something went wrong.', {
        description: 'There was a problem with your request.',
      })
    } finally {
      setLoading(false)
    }
  }

  // const triggerImgUrlValidation = () => form.trigger('image_url')

  return (
    <>
      {/* <AlertModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={onDelete}
        loading={loading}
      /> */}
      <div className="flex items-center justify-between">
        <Heading title={title} description={description} />
        {initialData && (
          <Button
            disabled={loading}
            variant="destructive"
            size="sm"
            onClick={() => setOpen(true)}
          >
            <Trash className="h-4 w-4" />
          </Button>
        )}
      </div>
      <Separator />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="relative w-full space-y-8"
        >
          <FormField
            control={form.control}
            name="image_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Image</FormLabel>
                <FormControl>
                  {/* <FileUpload
                    onChange={field.onChange}
                    value={field.value}
                    onRemove={field.onChange}
                  /> */}

                  <div className="w-full">
                    {field.value !== '' && (
                      <div className="relative h-[200px] w-[200px] overflow-hidden rounded-md">
                        <div>
                          <Image
                            loading="lazy"
                            // fill
                            width={300}
                            height={300}
                            className="h-20 w-20 object-cover md:h-28 md:w-28"
                            alt="Image"
                            src={field.value || ''}
                          />
                        </div>
                      </div>
                    )}

                    <UploadButton
                      className="rounded-md border border-slate-400 p-5 dark:border-slate-700"
                      endpoint="imageUploader"
                      onClientUploadComplete={(res) => {
                        // Do something with the response
                        // console.log('Files: ', res)
                        // alert('Upload Completed')
                        toast.success('Image uploaded successfully')
                        const data: any = res
                        console.log(data)
                        if (data) {
                          // field.onChange(data?.[0]?.url)
                          form.setValue('image_url', data?.[0]?.url)
                        }
                      }}
                      onUploadError={(error: Error) => {
                        // Do something with the error.
                        // alert(`ERROR! ${error.message}`)
                        toast.error(`ERROR! ${error.message}`)
                      }}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="gap-8 md:grid md:grid-cols-3">
            <FormField
              control={form.control}
              name="image_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Image Url</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Enter Image Url or Upload Image"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Template name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Template Slug"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Template description"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="is_pro"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      disabled={loading}
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Pro Template</FormLabel>
                    <FormDescription>
                      This template will only be available to pro users
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                  <FormControl>
                    <Input disabled={loading} placeholder="Tags" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="meta_desc"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Meta Description</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Meta description"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select
                    disabled={loading}
                    onValueChange={field.onChange}
                    value={field.value}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue
                          defaultValue={field.value}
                          placeholder="Select a category"
                        />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="max-h-[250px] overflow-y-auto">
                      {categories?.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="likes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Likes</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      disabled={loading}
                      placeholder="Number of likes"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                      value={field.value}
                    />
                  </FormControl>
                  <FormDescription>
                    Number of likes for this template
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem className="col-span-3">
                  <FormLabel>Code</FormLabel>
                  <FormControl>
                    <Textarea
                      disabled={loading}
                      placeholder="Paste your template code here"
                      className="min-h-[300px] font-mono"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="sticky bottom-0 flex w-full  items-center justify-center bg-background p-4 ">
            <Button disabled={loading} className=" w-full" type="submit">
              {action}
            </Button>
          </div>
        </form>
      </Form>
    </>
  )
}
