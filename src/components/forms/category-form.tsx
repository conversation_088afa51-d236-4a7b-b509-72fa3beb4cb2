'use client'
import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { Trash } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import slugify from 'react-slugify'
import { toast } from 'sonner'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Heading } from '@/components/ui/heading'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { createClient } from '@/utils/supabase/client'

import { Checkbox } from '../ui/checkbox'
import { Textarea } from '../ui/textarea'

// Define the label enum type
const CATEGORY_LABELS = [
  'new',
  'hot',
  'beta',
  'pro',
  'premium',
  'default',
] as const

// Zod schema for category validation
const formSchema = z.object({
  name: z
    .string()
    .min(3, { message: 'Category name must be at least 3 characters' }),
  slug: z.string(),
  description: z
    .string()
    .min(3, { message: 'Description must be at least 3 characters' }),
  icon: z.string().optional(),
  preview_image: z.string().optional(), // New field
  label: z.enum(CATEGORY_LABELS).default('default'), // Changed default to 'default'
  is_active: z.boolean().optional(),
  is_component_only: z.boolean().optional(), // New field
  is_template_only: z.boolean().optional(), // New field
  sequence: z.string(),
})

type CategoryFormValues = z.infer<typeof formSchema>

interface CategoryFormProps {
  initialData: any
}

export const CategoryForm: React.FC<CategoryFormProps> = ({ initialData }) => {
  const router = useRouter()

  const [loading, setLoading] = useState(false)
  const title = initialData ? 'Edit category' : 'Create category'
  const description = initialData ? 'Edit a category.' : 'Add a new category'
  const toastMessage = initialData ? 'Category updated.' : 'Category created.'
  const action = initialData ? 'Save changes' : 'Create'

  // Default values for the form - handle null values from database
  const defaultValues = initialData
    ? {
        name: initialData.name || '',
        slug: initialData.slug || '',
        description: initialData.description || '',
        icon: initialData.icon || '',
        preview_image: initialData.preview_image || '',
        label: initialData.label || 'default',
        is_active: initialData.is_active || false,
        is_component_only: initialData.is_component_only || false,
        is_template_only: initialData.is_template_only || false,
        sequence: initialData.sequence ? String(initialData.sequence) : '',
      }
    : {
        name: '',
        slug: '',
        description: '',
        icon: '',
        preview_image: '',
        label: 'default',
        is_active: false,
        is_component_only: false,
        is_template_only: false,
        sequence: '',
      }

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  })

  const onSubmit = async (data: CategoryFormValues) => {
    const supabase = createClient()

    try {
      setLoading(true)
      if (initialData) {
        // Update existing category
        const { data: category, error } = await supabase
          .from('categories')
          .update({
            name: data.name,
            slug: data.slug ? slugify(data.slug) : slugify(data.name),
            description: data.description,
            icon: data.icon,
            preview_image: data.preview_image,
            label: data.label,
            is_active: data.is_active,
            is_component_only: data.is_component_only,
            is_template_only: data.is_template_only,
            sequence: data.sequence,
          })
          .eq('id', initialData.id)
          .select()

        if (category) {
          router.push(`/dashboard/categories`)
          router.refresh()
          toast.success(toastMessage)
        } else {
          toast.error(error.message ?? 'Something went wrong while updating!')
        }
      } else {
        // Create a new category
        const { data: category, error } = await supabase
          .from('categories')
          .insert({
            name: data.name,
            slug: data.slug ? slugify(data.slug) : slugify(data.name),
            description: data.description,
            icon: data.icon,
            preview_image: data.preview_image,
            label: data.label,
            is_active: data.is_active,
            is_component_only: data.is_component_only,
            is_template_only: data.is_template_only,
            sequence: data.sequence,
          })
          .select()

        if (category) {
          router.push(`/dashboard/categories`)
          router.refresh()
          toast.success(toastMessage)
        } else {
          toast.error(error.message ?? 'Something went wrong while creating!')
        }

        // router.refresh()
        // router.push(`/dashboard/categories`)
        // toast('success', {
        //   description: toastMessage,
        // })
      }
    } catch (error: any) {
      toast.error('Uh oh! Something went wrong.', {
        description: 'There was a problem with your request.',
      })
    } finally {
      setLoading(false)
    }
  }

  const onDelete = async () => {
    try {
      setLoading(true)
      // Handle deletion logic here
      router.refresh()
      router.push(`/dashboard/categories`)
    } catch (error: any) {
    } finally {
      setLoading(false)
    }
  }

  return (
    <>
      <div className="flex items-center justify-between">
        <Heading title={title} description={description} />
        {initialData && (
          <Button
            disabled={loading}
            variant="destructive"
            size="sm"
            onClick={onDelete}
          >
            <Trash className="h-4 w-4" />
          </Button>
        )}
      </div>
      <Separator />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full space-y-8"
        >
          <div className="gap-8 md:grid md:grid-cols-2">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Category name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Category slug"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      disabled={loading}
                      placeholder="Category description"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="icon"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon (URL)</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Category icon URL"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* add is_active and sequence fields */}
            {/* for boolean is_active use different input */}

            <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      disabled={loading}
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Is Active</FormLabel>
                    <FormDescription>
                      This category will only be available on Webiste
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            {/* <FormField
              control={form.control}
              name="is_active"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Is Active</FormLabel>
                  <FormControl>
                    <Checkbox
                      disabled={loading}
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            /> */}

            <FormField
              control={form.control}
              name="sequence"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Sequence</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      disabled={loading}
                      placeholder="Category sequence"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="preview_image"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Preview Image URL</FormLabel>
                  <FormControl>
                    <Input
                      disabled={loading}
                      placeholder="Category preview image URL"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="label"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Label</FormLabel>
                  <Select
                    disabled={loading}
                    onValueChange={field.onChange}
                    value={field.value}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a label" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {CATEGORY_LABELS.map((label) => (
                        <SelectItem key={label} value={label}>
                          {label.charAt(0).toUpperCase() + label.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="is_component_only"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      disabled={loading}
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Components Only</FormLabel>
                    <FormDescription>
                      This category will only be available for components
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="is_template_only"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      disabled={loading}
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Templates Only</FormLabel>
                    <FormDescription>
                      This category will only be available for templates
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
          </div>
          <div className="sticky bottom-0 flex w-full  items-center justify-center bg-background p-4 ">
            <Button disabled={loading} className="w-full" type="submit">
              {action}
            </Button>
          </div>
        </form>
      </Form>
    </>
  )
}
