'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useMutation } from '@tanstack/react-query'
import { Loader2 } from 'lucide-react'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import * as z from 'zod'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { useProfileStore } from '@/stores/profile-store'
import { UploadButton } from '@/types/uploadthing'
import { createClient } from '@/utils/supabase/client'

const profileFormSchema = z.object({
  first_name: z
    .string()
    .min(2, 'First name must be at least 2 characters')
    .nullable(),
  last_name: z
    .string()
    .min(2, 'Last name must be at least 2 characters')
    .nullable(),
  avatar_url: z.string().url('Must be a valid URL').nullable(),
})

type ProfileFormValues = z.infer<typeof profileFormSchema>

interface ProfileUpdateFormProps {
  onSuccess?: () => void
}

export function ProfileUpdateForm({ onSuccess }: ProfileUpdateFormProps) {
  const { profile, setProfile } = useProfileStore()
  const supabase = createClient()
  const [isUploading, setIsUploading] = useState(false)

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      first_name: profile?.first_name || '',
      last_name: profile?.last_name || '',
      avatar_url: profile?.avatar_url || '',
    },
  })

  const updateProfile = useMutation({
    mutationFn: async (values: ProfileFormValues) => {
      const { data, error } = await supabase
        .from('profiles')
        .update({
          ...values,
          updated_at: new Date().toISOString(),
        })
        .eq('id', profile?.id)
        .select()
        .single()

      if (error) throw error
      return data
    },
    onSuccess: (data) => {
      setProfile(data)
      toast.success('Profile updated successfully')
      onSuccess?.()
    },
    onError: (error) => {
      toast.error('Failed to update profile')
      console.error('Profile update error:', error)
    },
  })

  const getInitials = () => {
    const firstName = form.getValues('first_name') || ''
    const lastName = form.getValues('last_name') || ''
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || '?'
  }

  const onSubmit = (values: ProfileFormValues) => {
    updateProfile.mutate(values)
  }

  return (
    <Card className="p-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Avatar Upload */}
          <FormField
            control={form.control}
            name="avatar_url"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Profile Picture</FormLabel>
                <div className="flex items-center gap-4">
                  <Avatar className="h-20 w-20">
                    <AvatarImage
                      src={field.value || ''}
                      alt="Profile picture"
                    />
                    <AvatarFallback className="text-lg">
                      {isUploading ? (
                        <Loader2 className="h-6 w-6 animate-spin" />
                      ) : (
                        getInitials()
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col gap-2">
                    <UploadButton
                      className="ut-button:bg-primary ut-button:text-primary-foreground ut-button:hover:bg-primary/90"
                      endpoint="imageUploader"
                      onClientUploadComplete={(res) => {
                        const data: any = res
                        if (data) {
                          form.setValue('avatar_url', data[0].url)
                          toast.success('Profile picture uploaded successfully')
                        }
                      }}
                      onUploadError={(error: Error) => {
                        toast.error(`Upload failed: ${error.message}`)
                      }}
                    />
                    <p className="text-xs text-muted-foreground">
                      Supported formats: JPG, PNG, GIF (max 4MB)
                    </p>
                  </div>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid gap-6 md:grid-cols-2">
            <FormField
              control={form.control}
              name="first_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="John"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="last_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Doe"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Read-only fields */}
          <div className="grid gap-6 md:grid-cols-2">
            <div>
              <FormLabel>Email</FormLabel>
              <Input
                value={profile?.email || ''}
                disabled
                className="bg-muted"
              />
            </div>
            <div>
              <FormLabel>Username</FormLabel>
              <Input
                value={profile?.username || ''}
                disabled
                className="bg-muted"
              />
            </div>
          </div>

          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={onSuccess}
              disabled={updateProfile.isPending}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={updateProfile.isPending}>
              {updateProfile.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </Card>
  )
}
