'use client'

import { useRouter } from 'next/navigation'
import { ReactNode, useEffect, useState } from 'react'

import { Role } from '@/actions/users'
import { useProfileStore } from '@/stores/profile-store'
import { createClient } from '@/utils/supabase/client'

import Loading from '../custom/loading'

interface ProtectedWrapperProps {
  children: ReactNode
  allowedRoles?: Role[]
  fallback?: ReactNode
}

export function ProtectedWrapper({
  children,
  allowedRoles = ['admin', 'editor'],
  fallback = (
    <div className="p-8 text-center">
      You don&apos;t have permission to access this page.
    </div>
  ),
}: ProtectedWrapperProps) {
  const [isAuthorized, setIsAuthorized] = useState<boolean | null>(null)
  const { profile } = useProfileStore()
  const router = useRouter()
  const supabase = createClient()

  useEffect(() => {
    const checkAuth = async () => {
      // Check if user is authenticated
      const { data, error } = await supabase.auth.getUser()

      if (error || !data.user) {
        router.push('/login')
        return
      }

      // If we already have profile in store
      if (profile) {
        const hasAccess = allowedRoles.includes(profile.role as Role)
        setIsAuthorized(hasAccess)
        if (!hasAccess) {
          // Redirect to home if not authorized but authenticated
          router.push('/')
        }
        return
      }

      // Fetch profile if not in store
      const { data: profileData } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', data.user.id)
        .single()

      if (!profileData || !allowedRoles.includes(profileData.role as Role)) {
        setIsAuthorized(false)
        router.push('/')
        return
      }

      setIsAuthorized(true)
    }

    checkAuth()
  }, [router, profile, allowedRoles, supabase])

  // Show loading state while checking
  if (isAuthorized === null) {
    return <Loading text="Checking permissions..." />
  }

  // Show fallback if not authorized
  if (!isAuthorized) {
    return fallback
  }

  // Show children if authorized
  return <>{children}</>
}
