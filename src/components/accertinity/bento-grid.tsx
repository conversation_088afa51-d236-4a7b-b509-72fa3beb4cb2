import {
  Clipboard<PERSON>heck,
  FileBarChartIcon,
  FileSignature,
  TableIcon,
} from 'lucide-react'
import Image from 'next/image'

import Elevate from '../features/elevate'
import { BentoGrid, BentoGridItem } from '../ui/bento-grid'
// import {
//   IconClipboardCopy,
//   IconFileBroken,
//   IconSignature,
//   IconTableColumn,
// } from '@tabler/icons-react'

export function BentoGridSecondDemo() {
  return (
    <div className="relative mb-10 w-full overflow-hidden bg-transparent px-0 py-12 lg:p-20 lg:pt-36">
      <Elevate />
      <BentoGrid className="container mx-auto">
        {items.map((item, i) => (
          <BentoGridItem
            key={i}
            title={item.title}
            description={item.description}
            header={item.header}
            className={item.className}
            icon={item.icon}
          />
        ))}
      </BentoGrid>
    </div>
  )
}

const Skeleton = () => (
  <div className="flex h-full min-h-[6rem] w-full flex-1 rounded-xl bg-gradient-to-br from-neutral-200 to-neutral-100 dark:from-neutral-900 dark:to-neutral-800" />
)

const CardImage = ({ image }: { image: string }) => {
  return (
    <div className="relative aspect-video h-full w-full rounded-sm bg-gradient-to-br from-neutral-200 to-neutral-100 dark:from-neutral-900 dark:to-neutral-800">
      <Image src={image} fill alt="" className="rounded-sm object-cover" />
    </div>
  )
}
const items = [
  {
    title: 'Best Themes & Components',
    description: 'Dive into the transformative power of technology.',
    header: (
      <CardImage
        image={
          'https://utfs.io/f/LhUzHyAuqPAKUlrwiUWlNcqwD9Fjd5n0CPIg1bEmyO3u7oRA'
        }
      />
    ),
    className: 'md:col-span-2',
    icon: <ClipboardCheck className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: 'The Art of Design',
    description: 'Discover the beauty of thoughtful and functional design.',
    header: (
      <CardImage
        image={
          // 'https://utfs.io/f/LhUzHyAuqPAKGXHOEdqviO8HaNLp9CfXBwksl5MYA01nUJzF'
          // 'https://utfs.io/f/LhUzHyAuqPAKlS82zXgEVFC5Z7TKIj90bioGDkA4Oyt8mMfX'
          'https://utfs.io/f/LhUzHyAuqPAKibzISmE9J6ZH4mMSt0hwEVivgxoBFpkTcL2I'
        }
      />
    ),

    className: 'md:col-span-1',
    icon: <FileBarChartIcon className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: 'Components & Templates',
    description:
      'Get the best of both worlds with our components and templates.',
    header: (
      <CardImage
        image={
          'https://utfs.io/f/LhUzHyAuqPAKZwdHvYhb9z0PnXqpVMKRFhI7aykWAjOftSxG'
        }
      />
    ),

    className: 'md:col-span-1',
    icon: <FileSignature className="h-4 w-4 text-neutral-500" />,
  },
  {
    title: 'Scope projects better',
    description:
      'Starting off with a ready to use design which reduces scope creep for new projects.',
    header: (
      <CardImage
        image={
          // 'https://utfs.io/f/LhUzHyAuqPAKGXHOEdqviO8HaNLp9CfXBwksl5MYA01nUJzF'
          'https://utfs.io/f/LhUzHyAuqPAK8SS5YZuIyc6GgMYNUKVbnvRDjP2wO0AkQhoJ'
        }
      />
    ),

    className: 'md:col-span-2',
    icon: <TableIcon className="h-4 w-4 text-neutral-500" />,
  },
]
