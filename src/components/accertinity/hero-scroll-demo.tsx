'use client'

import Heading from '../custom/heading'
import { ContainerScroll } from '../ui/container-scroll-animation'

export function HeroScrollDemo() {
  return (
    <div className="flex flex-col overflow-hidden">
      {/* https://gallery.theportfolio.in/CopyElements/1750953329821-laptop2.webp */}
      <ContainerScroll
        titleComponent={
          <>
            <Heading>Watch in Action</Heading>
          </>
        }
      >
        {/* <Image
          src={`/linear.webp`}
          alt="hero"
          height={720}
          width={1400}
          className="mx-auto rounded-2xl object-cover h-full object-left-top"
          draggable={false}
        /> */}
        <video
          className="mx-auto h-full rounded-2xl object-cover object-left-top"
          loop
          autoPlay
          muted
        >
          <source
            // src="https://utfs.io/f/LhUzHyAuqPAKZ82Sb1hb9z0PnXqpVMKRFhI7aykWAjOftSxG"
            src="https://utfs.io/f/LhUzHyAuqPAKN3ub6pDO04CiXQHtZ5Rbrd3aPIcWGqfmkhKA"
            type="video/mp4"
            // className="mx-auto h-full rounded-2xl object-cover object-left-top"
          />
          Your browser does not support the video tag.
        </video>
      </ContainerScroll>
    </div>
  )
}
