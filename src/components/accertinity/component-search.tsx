'use client'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

import { PlaceholdersAndVanishInput } from '../ui/placeholders-and-vanish-input'

interface ComponentSearchProps {
  isTemplate?: boolean
}

const ComponentSearch = ({ isTemplate = false }: ComponentSearchProps) => {
  const [query, setQuery] = useState('')

  const router = useRouter()
  const placeholders = isTemplate
    ? [
        'Search templates by tag or name',
        'Find free website templates',
        'Discover premium templates for advanced features',
        'Explore responsive templates for mobile and desktop',
        'Search templates for e-commerce sites',
        'Find templates with modern design elements',
        'Discover templates with built-in SEO optimization',
        'Explore templates for blogs and portfolios',
      ]
    : [
        'Search components by tag or name',
        'Find reusable UI components',
        'Discover free components for quick integration',
        'Explore pro components with advanced functionalities',
        'Search components for dashboard layouts',
        'Find accessible components for better usability',
        'Discover components with customizable styles',
        'Explore components for forms and inputs',
      ]

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchQuery = e.target.value
    setQuery(searchQuery)
  }
  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    router.push(
      `${
        isTemplate ? '/templates' : '/components'
      }/search?q=${encodeURIComponent(query)}`
    )
  }
  return (
    <>
      <PlaceholdersAndVanishInput
        placeholders={placeholders}
        onChange={handleChange}
        onSubmit={onSubmit}
      />
    </>
  )
}

export default ComponentSearch
