import Script from 'next/script'

export function MainJsonLd() {
  return (
    <Script
      id="product-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'Product',
          '@id': 'https://copyelement.com/#product',
          name: 'CopyElement',
          brand: {
            '@type': 'Brand',
            name: 'Webriy',
          },
          provider: {
            '@type': 'Organization',
            '@id': 'https://copyelement.com/#organization',
          },
          aggregateRating: {
            '@type': 'AggregateRating',
            ratingValue: '5',
            bestRating: '5',
            worstRating: '1',
            ratingCount: '115745',
          },
        }),
      }}
    />
  )
}

export function FAQJsonLd({ faqItems }: { faqItems: any[] }) {
  return (
    <Script
      id="faq-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'FAQPage',
          mainEntity: faqItems.map((faq) => ({
            '@type': 'Question',
            name: faq.question,
            acceptedAnswer: {
              '@type': 'Answer',
              text: faq.answer,
            },
          })),
        }),
      }}
    />
  )
}
