import { ArrowRightIcon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

import AnimationContainer from '../global/animation-container'
import MaxWidthWrapper from '../global/max-width-wrapper'
import { BorderBeam } from '../ui/border-beam'
import { Button } from '../ui/button'

const HeroSection2 = () => {
  return (
    <MaxWidthWrapper>
      <div className="flex w-full flex-col items-center justify-center bg-gradient-to-t from-background text-center">
        <AnimationContainer className="flex w-full flex-col items-center justify-center text-center">
          <button className="group relative grid overflow-hidden rounded-full px-4 py-1 shadow-[0_1000px_0_0_hsl(0_0%_20%)_inset] transition-colors duration-200">
            <span>
              <span className="spark mask-gradient absolute inset-0 h-[100%] w-[100%] animate-flip overflow-hidden rounded-full [mask:linear-gradient(white,_transparent_50%)] before:absolute before:aspect-square before:w-[200%] before:rotate-[-90deg] before:animate-rotate before:bg-[conic-gradient(from_0deg,transparent_0_340deg,white_360deg)] before:content-[''] before:[inset:0_auto_auto_50%] before:[translate:-50%_-15%]" />
            </span>
            <span className="backdrop absolute inset-[1px] rounded-full bg-neutral-950 transition-colors duration-200 group-hover:bg-neutral-900" />
            <span className="absolute inset-x-0 bottom-0 h-full w-full bg-gradient-to-tr from-primary/20 blur-md" />
            <span className="z-10 flex items-center justify-center gap-1 py-0.5 text-sm text-neutral-100">
              ✨ Manage links smarter
              <ArrowRightIcon className="size-3 ml-1 transition-transform duration-300 ease-in-out group-hover:translate-x-0.5" />
            </span>
          </button>
          <h1 className="text-balance w-full py-6 text-center font-heading text-5xl font-medium !leading-[1.15] tracking-normal text-foreground sm:text-6xl md:text-7xl lg:text-8xl">
            Smart Links with{' '}
            <span className="inline-bloc bg-gradient-to-r from-violet-500 to-fuchsia-500 bg-clip-text text-transparent">
              Precision
            </span>
          </h1>
          <p className="text-balance mb-12 text-lg tracking-tight text-muted-foreground md:text-xl">
            Effortlessly streamline your link management with Copyelement.
            <br className="hidden md:block" />
            <span className="hidden md:block">
              Shorten, track, and organize all your links in one place.
            </span>
          </p>
          <div className="z-50 flex items-center justify-center gap-4 whitespace-nowrap">
            <Button asChild>
              <Link
                prefetch={true}
                href={'/login'}
                className="flex items-center"
              >
                Start creating for free
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </AnimationContainer>

        <AnimationContainer
          delay={0.2}
          className="relative w-full bg-transparent px-2 pb-20 pt-20 md:py-32"
        >
          <div className="gradient absolute inset-0 left-1/2 h-1/4 w-3/4 -translate-x-1/2 animate-image-glow blur-[5rem] md:top-[10%] md:h-1/3" />

          <div className="-m-2 rounded-xl bg-opacity-50 p-2 ring-1 ring-inset ring-foreground/20 backdrop-blur-3xl lg:-m-4 lg:rounded-2xl">
            <BorderBeam size={250} duration={12} delay={9} />
            <Image
              src={
                'https://utfs.io/f/LhUzHyAuqPAKUjZ9PaWlNcqwD9Fjd5n0CPIg1bEmyO3u7oRA'
              }
              alt="CopyElement"
              width={1200}
              height={1200}
              quality={100}
              className="rounded-md bg-foreground/10 ring-1 ring-border lg:rounded-xl"
            />
            <div className="absolute inset-x-0 -bottom-4 z-40 h-1/2 w-full bg-gradient-to-t from-background" />
            <div className="absolute inset-x-0 bottom-0 z-50 h-1/4 w-full bg-gradient-to-t from-background md:-bottom-8" />
          </div>
        </AnimationContainer>
      </div>
    </MaxWidthWrapper>
  )
}

export default HeroSection2
