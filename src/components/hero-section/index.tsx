'use client'

import { motion, useInView } from 'framer-motion'
import { ArrowRightIcon, SparklesIcon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { useRef, useState } from 'react'
import YouTube from 'react-youtube'

import {
  FADE_DOWN_ANIMATION_VARIANTS,
  FADE_IN_ANIMATION_VARIANTS,
  FADE_UP_ANIMATION_VARIANTS,
} from '@/utils/constants/animations'

import AnimationContainer from '../global/animation-container'
import MaxWidthWrapper from '../global/max-width-wrapper'
import { YoutubePlayIcon } from '../icons/youtube-play'
import { BorderBeam } from '../ui/border-beam'
import { Button } from '../ui/button'
import Feeder from '../ui/feeder'
import Spotlight from '../ui/Spotlight'

export default function HeroSection() {
  const VIDEO_ID = 'FZVwAxdYTQ4' // Your video ID
  const ref = useRef(null)
  const isInView = useInView(ref)
  const [isVideoReady, setIsVideoReady] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)
  const playerRef = useRef<any>(null)

  const videoOptions = {
    height: '100%',
    width: '100%',
    playerVars: {
      autoplay: 0,
      controls: 0,
      rel: 0,
      showinfo: 0,
      mute: 0,
      modestbranding: 1,
      playsinline: 1,
    },
  }

  const handlePlay = () => {
    if (playerRef.current) {
      playerRef.current.playVideo()
      setIsPlaying(true)
    }
  }

  const onPlayerReady = (event: any) => {
    playerRef.current = event.target
    setIsVideoReady(true)
  }

  const onPlayerStateChange = (event: any) => {
    // Video ended
    if (event.data === 0) {
      setIsPlaying(false)
    }
  }

  return (
    <div className="relative min-h-[90vh] w-full overflow-hidden">
      {/* Enhanced background with overlay */}
      <div className="absolute left-0 right-0 top-0 -z-10">
        <Image
          src="https://gallery.theportfolio.in/background-images/1743147184230-new-background-image.webp"
          alt="Background"
          width={1920}
          height={1080}
          priority
          className="h-full w-full scale-125 object-contain opacity-70"
        />
        <div className="absolute inset-0 bg-background/20 " />
      </div>

      {/* Main content */}
      <div className="container relative z-[1] mx-auto px-6 pt-24 lg:pt-32">
        {/* Spotlight effect */}
        <div className="absolute left-1/2 top-1/2 max-w-4xl -translate-x-1/2 -translate-y-1/2">
          <Spotlight fill="hsl(var(--primary) / 0.2)" />
        </div>
        {/* <div className="absolute inset-0 items-center [background:radial-gradient(125%_125%_at_50%_10%,#000_40%,#63e_100%)]" /> */}

        <div className="mx-auto max-w-5xl text-center">
          <motion.div
            ref={ref}
            initial="hidden"
            animate={isInView ? 'show' : 'hidden'}
            variants={{
              hidden: {},
              show: {
                transition: {
                  staggerChildren: 0.1,
                },
              },
            }}
          >
            {/* Gradient orbs */}
            <div className="absolute left-1/2 top-0 -z-10 -translate-x-1/2">
              <div className="animate-pulse-slow h-[310px] w-[310px] rounded-full bg-primary/20 blur-[100px]" />
            </div>

            {/* Badge */}
            <motion.div
              variants={FADE_DOWN_ANIMATION_VARIANTS}
              className="mx-auto mb-6 w-fit"
            >
              <Feeder feed="✨ WORLD'S FIRST ELEMENTOR COMPONENT LIBRARY" />
            </motion.div>

            {/* Heading */}
            <motion.div
              variants={FADE_DOWN_ANIMATION_VARIANTS}
              className="relative"
            >
              <h1 className="bg-gradient-to-tr from-zinc-400/50 via-white to-white/60 bg-clip-text  pb-5  text-center font-heading text-4xl font-medium  text-transparent md:text-5xl lg:text-7xl ">
                Website Designed and built faster with CopyElement
              </h1>
            </motion.div>

            {/* Subheading */}
            <motion.p
              variants={FADE_UP_ANIMATION_VARIANTS}
              className="mx-auto mt-4 max-w-2xl font-heading text-base tracking-normal text-purple-100 sm:text-base md:text-lg"
            >
              With access to over <b>3500+ Components </b> and{' '}
              <b>2000+ Template</b> kits, you can build beautiful websites and
              save thousands of hours using the world&apos;s first largest
              Elementor component library.
            </motion.p>

            {/* CTA Buttons */}
            <motion.div
              variants={FADE_UP_ANIMATION_VARIANTS}
              className="mt-8 flex flex-col items-center justify-center gap-4 md:mt-10  md:flex-row"
            >
              <Button
                variant="outline-primary"
                size="hero"
                asChild
                className="w-full sm:w-auto"
              >
                <Link
                  prefetch={true}
                  href="/pricing"
                  className="flex items-center gap-2"
                >
                  <SparklesIcon className="h-5 w-5 text-yellow-400" />
                  <span className="font-semibold">Get PRO</span>
                  <ArrowRightIcon className="h-4 w-4 transition-transform duration-300 ease-in-out group-hover:translate-x-1" />
                </Link>
              </Button>

              <Button
                variant="gradient-primary"
                size="hero"
                asChild
                className="w-full sm:w-auto"
              >
                <Link
                  prefetch={true}
                  href="/components"
                  className="flex items-center gap-2"
                >
                  <span className="font-semibold">Start for free</span>
                  <ArrowRightIcon className="h-4 w-4 transition-transform duration-300 ease-in-out group-hover:translate-x-1" />
                </Link>
              </Button>
            </motion.div>

            <motion.p
              variants={FADE_UP_ANIMATION_VARIANTS}
              className="mt-4 font-heading text-xs tracking-normal text-primary-foreground"
            >
              No Credit Card or Signup required.
            </motion.p>
          </motion.div>
        </div>

        {/* Featured Image/Video Section */}
        <MaxWidthWrapper>
          <AnimationContainer
            delay={0.3}
            className="relative mt-5 w-full bg-transparent px-2 pb-10 pt-10 md:py-14"
          >
            <motion.div
              variants={FADE_IN_ANIMATION_VARIANTS}
              initial="hidden"
              animate="show"
              className="relative rounded-lg bg-foreground/5 p-2 ring-1 ring-foreground/10 backdrop-blur-sm lg:p-4"
            >
              <BorderBeam size={250} duration={12} delay={9} />

              <div className="relative aspect-video w-full overflow-hidden rounded-sm">
                {/* YouTube Video */}
                <div
                  className={`absolute inset-0 z-10 transition-opacity duration-500 ${
                    isPlaying ? 'opacity-100' : 'opacity-0'
                  }`}
                >
                  <YouTube
                    videoId={VIDEO_ID}
                    opts={videoOptions}
                    onReady={onPlayerReady}
                    onStateChange={onPlayerStateChange}
                    className="aspect-video h-full w-full"
                    iframeClassName="w-full h-full"
                  />
                </div>

                {/* Thumbnail Overlay */}
                <div
                  className={`absolute inset-0 z-20 transition-opacity duration-500 ${
                    isPlaying ? 'pointer-events-none opacity-0' : 'opacity-100'
                  }`}
                >
                  <Image
                    src={`https://img.youtube.com/vi/${VIDEO_ID}/maxresdefault.jpg`}
                    alt="Video Thumbnail"
                    width={1920}
                    height={1080}
                    priority
                    className="aspect-video h-full w-full object-cover"
                  />

                  {/* Play Button */}
                  <button
                    onClick={handlePlay}
                    className="group absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 transition-opacity hover:opacity-100"
                    disabled={!isVideoReady}
                  >
                    <div className="transform transition-transform duration-300 group-hover:scale-110">
                      <YoutubePlayIcon className="h-16 w-16" />
                    </div>
                  </button>
                </div>
              </div>

              {/* Gradient fade effect */}
              <div className="pointer-events-none absolute inset-x-0 bottom-0 h-32 bg-gradient-to-t from-background to-transparent" />
            </motion.div>
          </AnimationContainer>
        </MaxWidthWrapper>
      </div>
    </div>
  )
}
