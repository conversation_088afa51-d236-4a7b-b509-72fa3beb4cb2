'use client'

import { Settings, Share2, Upload, User } from 'lucide-react'
import { toast } from 'sonner'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface QuickAction {
  icon: React.ReactNode
  label: string
  description: string
  onClick: () => void
  variant?: 'default' | 'outline' | 'secondary'
  disabled?: boolean
}

interface QuickActionsCardProps {
  className?: string
}

export function QuickActionsCard({ className }: QuickActionsCardProps) {
  const quickActions: QuickAction[] = [
    {
      icon: <Settings className="h-4 w-4" />,
      label: 'Account Settings',
      description: 'Manage your account preferences',
      onClick: () => {
        // Navigate to settings
        console.log('Navigate to settings')
      },
      variant: 'outline',
    },
    {
      icon: <Share2 className="h-4 w-4" />,
      label: 'Share With Others',
      description: 'Share CopyElement with friends',
      onClick: () => {
        // Share profile logic
        if (navigator.share) {
          navigator.share({
            title: 'My Profile',
            url: window.origin,
          })
        } else {
          navigator.clipboard.writeText(window.location.href)
        }
      },
      variant: 'outline',
    },
    // {
    //   icon: <Download className="h-4 w-4" />,
    //   label: 'Export Data',
    //   description: 'Download your data',
    //   onClick: () => {
    //     // Export data logic
    //     console.log('Export user data')
    //   },
    //   variant: 'outline',
    // },
    {
      icon: <Upload className="h-4 w-4" />,
      label: 'Submit Component',
      description: 'Add a new component',
      onClick: () => {
        // Navigate to component submission
        // console.log('Navigate to component submission')
        toast.info(
          "Coming soon! We'll let you know when this feature is available."
        )
      },
      variant: 'default',
    },
  ]

  return (
    <Card
      className={cn(
        'border-0 bg-gradient-to-br from-background via-background to-accent/5',
        className
      )}
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5 text-primary" />
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {quickActions.map((action, index) => (
            <Button
              key={index}
              variant={action.variant || 'outline'}
              className={cn(
                'h-auto w-full justify-start p-4 text-left',
                action.variant === 'default'
                  ? 'bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80'
                  : 'hover:bg-accent/10'
              )}
              onClick={action.onClick}
              disabled={action.disabled}
            >
              <div className="flex items-center gap-3">
                <div
                  className={cn(
                    'rounded-md p-2',
                    action.variant === 'default'
                      ? 'bg-white/20'
                      : 'bg-primary/10 text-primary'
                  )}
                >
                  {action.icon}
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium">{action.label}</div>
                  <div
                    className={cn(
                      'text-xs',
                      action.variant === 'default'
                        ? 'text-white/80'
                        : 'text-muted-foreground'
                    )}
                  >
                    {action.description}
                  </div>
                </div>
              </div>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
