'use client'

import { LucideIcon } from 'lucide-react'

interface InfoCardProps {
  title: string
  value: string | number | undefined | null
  icon: LucideIcon
  color: 'primary' | 'secondary' | 'accent' | 'muted'
}

export function InfoCard({ title, value, icon: Icon, color }: InfoCardProps) {
  return (
    <div
      className={`group relative overflow-hidden rounded-xl border border-primary/30 p-4 transition-all duration-300 sm:rounded-2xl sm:p-6`}
    >
      <div className="relative">
        <div className="mb-3 flex items-center gap-2 sm:gap-3">
          <div
            className={`$ flex h-8 w-8 items-center justify-center rounded-lg border border-primary shadow-sm transition-transform duration-300 group-hover:scale-105 sm:h-10 sm:w-10 sm:rounded-xl`}
          >
            <Icon className={`h-4 w-4 sm:h-5 sm:w-5 `} />
          </div>
          <h3 className={`text-sm font-semibold sm:text-base `}>{title}</h3>
        </div>
        <p className="text-base font-medium text-foreground sm:text-lg">
          {value || 'Not set'}
        </p>
      </div>
    </div>
  )
}
