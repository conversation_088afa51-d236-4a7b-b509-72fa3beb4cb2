'use client'

import { Copy, ExternalLink } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { useThrottle } from '@/hooks/use-throttle'
import { cn } from '@/lib/utils'
import type { ComponentWithCategory } from '@/types/custom'
import { copyToClipBoard } from '@/utils/copy-to-clipboard'

import { LikeButton } from '../ui/like-button'

// import { LikeButton } from '../ui/like-button'
interface MainCardProps {
  component: ComponentWithCategory
  disableNavigation?: boolean
  isTemplate?: boolean
}

export function MainCard({
  component,
  disableNavigation = false,
  isTemplate = false,
}: MainCardProps) {
  const { id, image_url, is_pro, name, description, tags, slug, likes } =
    component
  const tagList = tags?.split(',') || []

  // Throttle copy function to prevent spam clicking
  const throttledCopy = useThrottle(async () => {
    await copyToClipBoard(id, is_pro, isTemplate)
  }, 2000)

  return (
    <Card className="group relative w-full rounded-lg text-center">
      {/* Screen reader only title and description */}
      <span className="sr-only">{name}</span>
      <span className="sr-only">{description}</span>

      {!disableNavigation && slug && (
        <Link
          href={`/${
            isTemplate ? 'templates' : 'components'
          }/${encodeURIComponent(
            component?.category?.slug
          )}/${encodeURIComponent(slug)}`}
          className="absolute right-2 top-2 z-[1] opacity-0 transition-opacity duration-200 group-hover:opacity-100"
        >
          <Button
            size="sm"
            variant="default"
            className="h-8 w-8 p-0 lg:h-10 lg:w-10"
          >
            <ExternalLink className="h-4 w-4" />
            <span className="sr-only">Open component details</span>
          </Button>
        </Link>
      )}

      <CardContent
        className={cn(
          'relative flex justify-center overflow-hidden  rounded-lg bg-slate-100 p-2 transition-all duration-300 md:p-3',
          'group-hover:bg-gradient-to-br',
          is_pro
            ? 'group-hover:from-orange-400 group-hover:to-yellow-400'
            : 'group-hover:from-brand-600 group-hover:to-brand-400'
        )}
      >
        <div className="flex min-h-[100px] w-full cursor-pointer items-center justify-center overflow-hidden rounded-sm bg-white transition-all">
          <Image
            src={image_url || '/placeholder.svg'}
            alt={name || 'Component preview'}
            className="h-full w-full object-contain transition-transform duration-500 group-hover:scale-[1.05]"
            width={2000}
            height={2000}
            loading="lazy"
          />
        </div>

        {/* Hover actions */}
        <div className="absolute bottom-2 right-2 hidden flex-col items-end space-x-2 group-hover:flex">
          <div className="flex w-full flex-row justify-end space-x-2 pr-1">
            <LikeButton
              componentId={id}
              initialLikes={likes && likes > 0 ? likes : 3234}
              variant="card"
              size="sm"
            />

            <Button
              variant="default"
              className={cn(
                'h-7 px-2 text-xs text-white',
                is_pro
                  ? 'bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600'
                  : 'bg-black hover:bg-gray-900'
              )}
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                throttledCopy()
              }}
            >
              <Copy className="mr-1 h-3 w-3" />
              <span className="font-semibold">Copy to clipboard</span>
            </Button>
          </div>

          {/* Tags */}
          <div className="mt-2 flex justify-end space-x-1.5 overflow-x-auto">
            {tagList.map((tag) => (
              <Badge
                key={tag}
                variant={is_pro ? 'outline' : 'secondary'}
                className={cn(
                  'border-secondary/50 bg-secondary/60 text-[10px]  ',
                  is_pro && 'border-yellow-200 bg-white/90 text-yellow-800'
                )}
              >
                {tag.trim()}
              </Badge>
            ))}
          </div>
        </div>

        {/* Pro indicator */}
        {is_pro && (
          <Badge
            variant={'premium'}
            className="absolute left-3 top-3 text-[10px]"
          >
            PRO
          </Badge>
        )}
        {/* bottom shadow when hover */}
      </CardContent>
    </Card>
  )
}
