'use client'

import { ArrowRightIcon, CheckIcon } from '@radix-ui/react-icons'
import Image from 'next/image'
import Link from 'next/link'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { useProfileStore } from '@/stores/profile-store'
import { moneyFormatter } from '@/utils/functions/money-formatter'
import { cn } from '@/utils/tailwind'

type Tab = 'monthly' | 'yearly'

type PricingCardProps = {
  plan: any
  activeTab: Tab
  discountPercentage: number
  openCheckout: (priceId: string) => void
}

const PricingCard = ({
  plan,
  activeTab,
  discountPercentage,
  openCheckout,
}: PricingCardProps) => {
  const isHighlighted =
    plan.plan_type === 'lifetime' || plan.plan_type === 'pro-m'
  const { profile } = useProfileStore()

  const originalPrice = plan.unit_amount / 100
  const discountedPrice = Math.floor(
    originalPrice * (1 - discountPercentage / 100)
  )

  const buttonStyles = {
    default: cn(
      'h-12 bg-white dark:bg-zinc-900',
      'hover:bg-zinc-50 dark:hover:bg-zinc-800',
      'text-zinc-900 dark:text-zinc-100',
      'border border-zinc-200 dark:border-zinc-800',
      'hover:border-zinc-300 dark:hover:border-zinc-700',
      'shadow-sm hover:shadow-md',
      'text-sm font-medium'
    ),
    highlight: cn(
      'h-12 bg-zinc-900 dark:bg-zinc-100',
      'hover:bg-zinc-800 dark:hover:bg-zinc-300',
      'text-white dark:text-zinc-900',
      'shadow-[0_1px_15px_rgba(0,0,0,0.1)]',
      'hover:shadow-[0_1px_20px_rgba(0,0,0,0.15)]',
      'font-semibold text-base'
    ),
  }

  const badgeStyles = cn(
    'px-4 py-1.5 text-sm font-medium',
    'bg-zinc-900 dark:bg-zinc-100',
    'text-white dark:text-zinc-900',
    'border-none shadow-lg'
  )

  return (
    <div
      className={cn(
        'group relative backdrop-blur-sm',
        'rounded-3xl transition-all duration-300',
        'flex flex-col',
        isHighlighted
          ? 'bg-gradient-to-b from-zinc-100/80 to-transparent dark:from-zinc-400/[0.15]'
          : 'bg-white dark:bg-zinc-800/50',
        'border',
        isHighlighted
          ? 'border-zinc-400/50 shadow-xl dark:border-zinc-400/20'
          : 'border-zinc-200 shadow-md dark:border-zinc-700',
        'hover:translate-y-0 hover:shadow-lg'
      )}
    >
      {/* Badge for highlighted plans */}
      {isHighlighted && discountPercentage > 0 && (
        <div className="absolute -top-4 left-6">
          <Badge className={badgeStyles}>{discountPercentage}% OFF</Badge>
        </div>
      )}

      <div className="flex-1 p-8">
        <div className="mb-4 flex items-center justify-between">
          <div
            className={cn(
              'rounded-xl p-2',
              isHighlighted
                ? 'bg-zinc-100 text-zinc-900 dark:bg-zinc-800 dark:text-zinc-100'
                : 'bg-zinc-100 text-zinc-600 dark:bg-zinc-800 dark:text-zinc-400'
            )}
          >
            {plan.plan_image ? (
              <Image
                src={plan.plan_image}
                alt={plan.name}
                width={24}
                height={24}
                className="h-8 w-8 object-contain lg:h-10 lg:w-10"
              />
            ) : (
              <div className="h-8 w-8  rounded-full bg-zinc-300 dark:bg-emerald-300 lg:h-10 lg:w-10" />
            )}
          </div>
          <h3 className="text-xl font-semibold text-zinc-900 dark:text-zinc-100">
            {plan.name}
          </h3>
        </div>

        <div className="mb-6">
          <div className="flex items-baseline gap-2">
            <span className="text-4xl font-bold text-zinc-900 dark:text-zinc-100">
              {plan.plan_type === 'free' ? (
                'Free'
              ) : (
                <>{moneyFormatter(discountedPrice, 'USD', false)}</>
              )}{' '}
            </span>
            {plan.plan_type !== 'free' && plan.plan_type !== 'lifetime' && (
              <span className="text-sm text-zinc-500 dark:text-zinc-400">
                /{plan.plan_type === 'pro-y' ? 'year' : 'month'}
              </span>
            )}
            {plan.plan_type === 'lifetime' && (
              <Badge className="ml-2 font-normal capitalize">
                for lifetime
              </Badge>
            )}
          </div>
          <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-400">
            {plan.description}
          </p>
          {discountPercentage > 0 && plan.plan_type !== 'free' && (
            <div className="mt-2 flex items-center justify-start gap-2">
              <p className="text-sm font-bold text-zinc-400 line-through dark:text-zinc-500">
                ${originalPrice.toFixed(2)}
                {plan.plan_type !== 'lifetime' && (
                  <span>
                    {plan.plan_type === 'pro-y' && '/year'}
                    {plan.plan_type === 'pro-m' && '/mo'}
                  </span>
                )}
              </p>
            </div>
          )}
        </div>

        <div className="space-y-4">
          {plan.metadata.features.map((feature: any, index: number) => (
            <div key={index} className="flex gap-4">
              <div
                className={cn(
                  'mt-1 rounded-full p-0.5 transition-colors duration-200',
                  feature.included
                    ? 'text-emerald-600 dark:text-emerald-400'
                    : 'text-zinc-400 dark:text-zinc-600'
                )}
              >
                <CheckIcon className="h-4 w-4" />
              </div>
              <div>
                <TooltipProvider>
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <div
                        className={cn(
                          'text-sm font-medium text-zinc-900 dark:text-zinc-100',
                          feature.tooltip &&
                            'cursor-pointer border-b !border-dashed border-zinc-300 dark:border-zinc-600'
                        )}
                      >
                        {feature.text}
                      </div>
                    </TooltipTrigger>
                    {feature.tooltip && (
                      <TooltipContent>
                        <p>{feature.tooltip}</p>
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-auto p-8 pt-0">
        {plan.plan_type === 'free' ? (
          <Link
            prefetch={true}
            href="/components"
            className={cn(
              'relative inline-flex w-full items-center justify-center transition-all duration-300',
              buttonStyles.default
            )}
          >
            <span className="relative z-10 flex items-center justify-center gap-2">
              {plan.metadata.btn.text}
              <ArrowRightIcon className="h-4 w-4" />
            </span>
          </Link>
        ) : (
          <Button
            onClick={() => {
              openCheckout(plan.paddle_price_id)
            }}
            className={cn(
              'relative w-full transition-all duration-300',
              isHighlighted ? buttonStyles.highlight : buttonStyles.default
            )}
          >
            <span className="relative z-10 flex items-center justify-center gap-2">
              {isHighlighted ? (
                <>
                  {plan.metadata.btn.text}
                  <ArrowRightIcon className="h-4 w-4" />
                </>
              ) : (
                <>
                  {plan.metadata.btn.text}
                  <ArrowRightIcon className="h-4 w-4" />
                </>
              )}
            </span>
          </Button>
        )}
      </div>
    </div>
  )
}

export default PricingCard
