'use client'

import { useThrottle } from '@/hooks/use-throttle'
import { copyToClipBoard } from '@/utils/copy-to-clipboard'

import { Button } from '../ui/button'
import { LikeButton } from '../ui/like-button'

type Props = {
  id: string
  tags: string
  is_pro: boolean
  likes: number
  isTemplate?: boolean
}

const MainCardLikeAndCopyButton = ({
  id,
  tags,
  is_pro,
  likes,
  isTemplate = false,
}: Props) => {
  // Throttle copy function to prevent spam clicking
  const throttledCopy = useThrottle(async () => {
    await copyToClipBoard(id, is_pro, isTemplate)
  }, 2000)

  return (
    <>
      {/* <div className="absolute bottom-2 right-2 hidden flex-col items-end space-x-2 group-hover:flex"> */}
      <div className="flex w-full flex-row justify-start space-x-2 pr-1">
        <LikeButton
          componentId={id}
          initialLikes={likes}
          variant="card"
          size="sm"
        />
        <div>
          <Button
            aria-label="Primary Button"
            type="button"
            onClick={throttledCopy}
            className="w-full"
            style={{ flexShrink: 0 }}
          >
            <p className="flex items-center justify-start space-x-1.5">
              <span>Copy to clipboard</span>
            </p>
          </Button>{' '}
          {/**/}
        </div>
      </div>

      {/* </div> */}
    </>
  )
}

export default MainCardLikeAndCopyButton
