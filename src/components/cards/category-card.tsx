import { EditIcon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Category } from '@/types/custom'

interface CategoryCardProps {
  category: Category
  isLoading?: boolean
}

export const CategoryCard = ({ category, isLoading }: CategoryCardProps) => {
  if (isLoading) {
    return (
      <div className="animate-pulse rounded-xl border bg-card p-6 shadow-sm">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 rounded-full bg-gray-200" />
          <div className="h-4 w-1/3 rounded bg-gray-200" />
        </div>
        <div className="mt-4 space-y-2">
          <div className="h-3 w-3/4 rounded bg-gray-200" />
          <div className="h-3 w-1/2 rounded bg-gray-200" />
        </div>
      </div>
    )
  }

  return (
    <div
      className={cn(
        'group relative overflow-hidden rounded-xl border bg-card transition-all hover:shadow-lg',
        category.is_active ? 'border-gray-800' : 'border-red-700'
      )}
    >
      <div className="p-6">
        <div className="flex flex-col gap-4">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className="relative h-12 w-12 overflow-hidden rounded-full border">
                <Image
                  src={category?.icon || ''}
                  alt={category?.name || 'Category Icon'}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
              </div>
              <div>
                <h3 className="font-semibold tracking-tight">
                  {category.name}
                </h3>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Badge variant={'secondary'}>#{category.sequence}</Badge>
                  <Badge
                    variant={category.is_active ? 'success' : 'destructive'}
                    className="capitalize"
                  >
                    {category.is_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <p className="line-clamp-3 text-sm text-muted-foreground">
            {category.description}
          </p>

          <div className="flex items-center justify-end">
            <Link href={`/dashboard/categories/${category.id}`}>
              <Button
                size={'sm'}
                variant={'default'}
                className="group/button transition-all"
              >
                <EditIcon
                  size={14}
                  className="mr-2 transition-transform duration-300 group-hover/button:scale-110"
                />
                Edit Category
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
