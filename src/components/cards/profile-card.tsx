'use client'

import { Crown, Heart, LogOutIcon, Mail, User } from 'lucide-react'
import { ReactNode } from 'react'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Profile } from '@/types/custom'

interface ProfileCardProps {
  profile: Profile
  onEditToggle?: () => void
  isEditing?: boolean
  isCurrentUser?: boolean
  children?: ReactNode
  className?: string
}

export function ProfileCard({
  profile,
  onEditToggle,
  isEditing = false,
  isCurrentUser = false,
  children,
  className,
}: ProfileCardProps) {
  return (
    <Card
      className={cn(
        'relative overflow-hidden border-0 bg-gradient-to-br from-background via-background to-accent/5',
        className
      )}
    >
      {/* Decorative background pattern */}
      <div className="absolute inset-0 bg-grid-black/[0.02] [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />

      <CardHeader className="relative">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Avatar className="h-20 w-20 border-4 border-primary/20 shadow-lg">
                <AvatarImage
                  src={profile.avatar_url || undefined}
                  alt={`${profile.first_name} ${profile.last_name}`}
                  className="object-cover"
                />
                <AvatarFallback className="bg-gradient-to-br from-primary/20 to-primary/40 text-xl font-semibold">
                  <User className="h-8 w-8 text-primary/80" />
                </AvatarFallback>
              </Avatar>
              {profile.is_pro && (
                <div className="absolute -bottom-1 -right-1 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 p-1.5 shadow-lg">
                  <Crown className="h-3 w-3 text-white" />
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold tracking-tight">
                  {profile.first_name} {profile.last_name}
                </h1>
              </div>

              <div className="flex items-center gap-2 text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span className="text-sm">{profile.email}</span>
              </div>

              <div className="flex flex-wrap gap-2">
                {profile.is_pro && (
                  <Badge variant={'pro'}>
                    <Crown className="h-3 w-3" />
                    PRO
                  </Badge>
                )}
                <Badge variant="outline" className="gap-1">
                  <Heart className="h-3 w-3" />
                  {profile.liked_components?.length || 0} likes
                </Badge>
              </div>
            </div>
          </div>

          {/* {isCurrentUser && onEditToggle && (
            <Button
              variant="outline"
              size="sm"
              onClick={onEditToggle}
              className="flex items-center gap-2"
            >
              <Edit className="h-4 w-4" />
              {isEditing ? 'Cancel' : 'Edit Profile'}
            </Button>
          )} */}

          <form className="h-full " action="/auth/signout" method="post">
            <Button
              variant={'destructive'}
              className="button flex  min-w-[180px] text-center"
              type="submit"
            >
              Log out
              <LogOutIcon className=" ml-2 h-4 w-4" />
            </Button>
          </form>
        </div>
      </CardHeader>

      {children && <CardContent className="pt-0">{children}</CardContent>}
    </Card>
  )
}
