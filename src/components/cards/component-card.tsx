'use client'
import { Edit, MoreH<PERSON>zontal, Trash } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { useState } from 'react'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { LikeButton } from '@/components/ui/like-button'
import Skeleton from '@/components/ui/skeleton'
import { Component } from '@/types/custom'

interface ComponentCardProps {
  component: Component
  isLoading?: boolean
  isAdmin?: boolean
}

export function ComponentCard({
  component,
  isLoading = false,
  isAdmin = false,
}: ComponentCardProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

  if (isLoading) {
    return (
      <Card className="overflow-hidden">
        <CardHeader className="p-0">
          <div className="relative h-48 w-full">
            <Skeleton className="h-full w-full" />
          </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-2">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between p-4 pt-0">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </CardFooter>
      </Card>
    )
  }

  const handleDelete = async () => {
    // Implement your delete logic here
    console.log('Deleting component:', component.id)
    setIsDeleteDialogOpen(false)
  }

  return (
    <>
      <Card className="overflow-hidden">
        <CardHeader className="p-0">
          <div className="relative h-48 w-full">
            {component.image_url ? (
              <Image
                src={component.image_url}
                alt={component.name || 'Component image'}
                fill
                className="object-cover"
              />
            ) : (
              <div className="flex h-full w-full items-center justify-center bg-muted">
                No image
              </div>
            )}
            {component.is_pro && (
              <Badge className="absolute right-2 top-2" variant="default">
                PRO
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-2">
            <h3 className="font-semibold">{component.name}</h3>
            <p className="line-clamp-2 text-sm text-muted-foreground">
              {component.description || 'No description available'}
            </p>
            {!isAdmin && (
              <div className="flex items-center justify-between pt-2">
                <LikeButton
                  componentId={component.id}
                  initialLikes={component.likes || 0}
                  variant="minimal"
                  size="sm"
                />
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between p-4 pt-0">
          {isAdmin ? (
            <>
              <Link href={`/dashboard/components/${component.id}`}>
                <Button variant="outline" size="sm">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              </Link>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuItem
                    onClick={() => navigator.clipboard.writeText(component.id)}
                  >
                    Copy ID
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/components/${component.slug}`}
                      target="_blank"
                    >
                      View Live
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() => setIsDeleteDialogOpen(true)}
                    className="text-destructive"
                  >
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <Link
              prefetch={true}
              href={`/components/${component.slug}`}
              className="w-full"
            >
              <Button variant="default" size="sm" className="w-full">
                View Component
              </Button>
            </Link>
          )}
        </CardFooter>
      </Card>

      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Are you sure?</DialogTitle>
            <DialogDescription>
              This action cannot be undone. This will permanently delete the
              component and remove it from our servers.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <Trash className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
