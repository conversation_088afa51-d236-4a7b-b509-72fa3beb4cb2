'use client'

import { motion } from 'framer-motion'
import {
  Code2,
  Crown,
  ExternalLink,
  Eye,
  Heart,
  Loader2,
  MoreHorizontal,
  Share,
  Trash2,
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { useState } from 'react'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useThrottle } from '@/hooks/use-throttle'
import { cn } from '@/lib/utils'
import { ComponentWithCategory } from '@/types/custom'

interface LikedComponentCardProps {
  component: ComponentWithCategory
  onRemove?: (componentId: string) => void
  isRemoving?: boolean
}

export function LikedComponentCard({
  component,
  onRemove,
  isRemoving = false,
}: LikedComponentCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  const url = `/components/${component.category.slug}/${component.slug}`

  // Throttle remove function to prevent spam clicking
  const throttledRemove = useThrottle(() => {
    if (onRemove) {
      onRemove(component.id)
    }
  }, 1000)

  const tags = component.tags?.split(',').filter(Boolean) || []

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: component.name,
          text: component.description || '',
          url: url,
        })
      } catch (err) {
        // User cancelled sharing
      }
    } else {
      // Fallback to copying URL
      await navigator.clipboard.writeText(`${window.location.origin}/${url}`)
    }
  }

  return (
    <motion.div
      whileHover={{ y: -2 }}
      transition={{ duration: 0.2 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="group"
    >
      <Card className="relative overflow-hidden rounded-xl border bg-card shadow-sm transition-all duration-300 hover:shadow-lg">
        {/* Component Preview */}
        <div className="relative aspect-video overflow-hidden rounded-t-xl bg-muted">
          {component.image_url ? (
            <Image
              src={component.image_url}
              alt={component.name}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            />
          ) : (
            <div className="flex h-full items-center justify-center">
              <Code2 className="h-8 w-8 text-muted-foreground/50" />
            </div>
          )}

          {/* Overlay with Actions */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ duration: 0.2 }}
            className="absolute inset-0 flex items-center justify-center gap-2 bg-black/50 backdrop-blur-sm"
          >
            <Link prefetch={true} href={url}>
              <Button
                size="sm"
                className="bg-card text-foreground hover:bg-card/90"
              >
                <Eye className="mr-1.5 h-3.5 w-3.5" />
                View
              </Button>
            </Link>
          </motion.div>

          {/* Premium Badge */}
          {component.is_pro && (
            <div className="absolute left-3 top-3">
              <Badge className="bg-gradient-to-r from-amber-500 to-orange-600 text-white shadow-sm">
                <Crown className="mr-1 h-3 w-3" />
                Pro
              </Badge>
            </div>
          )}

          {/* Actions Menu */}
          <div className="absolute right-3 top-3">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  size="sm"
                  variant="secondary"
                  className="h-7 w-7 bg-card/90 p-0 opacity-0 shadow-sm transition-opacity duration-200 hover:bg-card group-hover:opacity-100"
                >
                  <MoreHorizontal className="h-3.5 w-3.5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={handleShare}>
                  <Share className="mr-2 h-4 w-4" />
                  Share Component
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={url} target="_blank">
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Open in New Tab
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={throttledRemove}
                  disabled={isRemoving}
                  className="text-red-600 focus:text-red-600"
                >
                  {isRemoving ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="mr-2 h-4 w-4" />
                  )}
                  Remove from Favorites
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <CardContent className="p-4">
          <div className="space-y-3">
            {/* Header */}
            <div className="flex items-start justify-between">
              <div className="min-w-0 flex-1">
                <h3 className="truncate font-semibold text-foreground">
                  {component.name}
                </h3>
                {component.category && (
                  <p className="text-xs text-muted-foreground">
                    {component.category.name}
                  </p>
                )}
              </div>
              <Badge
                variant="outline"
                className="ml-2 shrink-0 border-destructive/20 text-destructive"
              >
                <Heart className="mr-1 h-2.5 w-2.5" fill="currentColor" />
                Liked
              </Badge>
            </div>

            {/* Description */}
            {component.description && (
              <p className="line-clamp-2 text-sm text-muted-foreground">
                {component.description}
              </p>
            )}

            {/* Tags */}
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {tags.slice(0, 2).map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="text-xs font-normal"
                  >
                    {tag.trim()}
                  </Badge>
                ))}
                {tags.length > 2 && (
                  <Badge variant="secondary" className="text-xs font-normal">
                    +{tags.length - 2}
                  </Badge>
                )}
              </div>
            )}

            {/* Footer Actions */}
            <div className="flex items-center justify-end pt-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={throttledRemove}
                disabled={isRemoving}
                className={cn(
                  'h-7 w-7 p-0 text-muted-foreground transition-colors hover:text-red-600',
                  isRemoving && 'cursor-not-allowed opacity-50'
                )}
              >
                {isRemoving ? (
                  <Loader2 className="h-3.5 w-3.5 animate-spin" />
                ) : (
                  <Trash2 className="h-3.5 w-3.5" />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
