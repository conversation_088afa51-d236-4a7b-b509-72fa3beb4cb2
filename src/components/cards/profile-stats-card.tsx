'use client'

import { <PERSON><PERSON><PERSON><PERSON>, Heart, Star, TrendingUp } from 'lucide-react'
import Link from 'next/link'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Profile } from '@/types/custom'

interface ProfileStatsCardProps {
  profile: Profile
  className?: string
}

interface StatItemProps {
  icon: React.ReactNode
  label: string
  value: string | number
  description?: string
  href?: string
  gradient?: string
}

function StatItem({
  icon,
  label,
  value,
  href,
  description,
  gradient,
}: StatItemProps) {
  return (
    <Link
      prefetch={true}
      href={href ?? ''}
      className="group relative overflow-hidden rounded-xl border bg-gradient-to-br from-background via-background to-accent/5 p-6 transition-all hover:border-primary/20 hover:shadow-lg hover:shadow-primary/10"
    >
      <div
        className={cn(
          'absolute inset-0 opacity-10 transition-opacity group-hover:opacity-20',
          gradient || 'bg-gradient-to-br from-primary to-primary/80'
        )}
      />

      <div className="relative space-y-3">
        <div className="flex items-center gap-3">
          <div
            className={cn(
              'rounded-lg p-3 transition-colors',
              gradient?.includes('red') &&
                'bg-red-100 text-red-600 dark:bg-red-900/50 dark:text-red-400',
              gradient?.includes('blue') &&
                'bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400',
              gradient?.includes('amber') &&
                'bg-amber-100 text-amber-600 dark:bg-amber-900/50 dark:text-amber-400',
              gradient?.includes('green') &&
                'bg-green-100 text-green-600 dark:bg-green-900/50 dark:text-green-400',
              !gradient && 'bg-primary/10 text-primary'
            )}
          >
            {icon}
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-muted-foreground">{label}</p>
            <p className="text-3xl font-bold tracking-tight">{value}</p>
          </div>
        </div>
        {description && (
          <p className="text-xs leading-relaxed text-muted-foreground">
            {description}
          </p>
        )}
      </div>
    </Link>
  )
}

export function ProfileStatsCard({
  profile,
  className,
}: ProfileStatsCardProps) {
  const stats = [
    {
      icon: <Heart className="h-4 w-4" />,
      label: 'Liked Components',
      value: profile.liked_components?.length || 0,
      description: "Components you've liked",
      gradient: 'bg-gradient-to-br from-red-400 to-pink-600',
      href: '/profile/wishlist',
    },
    {
      icon: <BarChart3 className="h-4 w-4" />,
      label: 'Submitted',
      value: profile.submitted_components_count || 0,
      description: 'Components contributed',
      gradient: 'bg-gradient-to-br from-blue-400 to-indigo-600',
    },
    {
      icon: <Star className="h-4 w-4" />,
      label: 'Status',
      value: profile.is_pro ? 'PRO' : 'Free',
      description: profile.is_pro ? 'PRO member' : 'Free tier user',
      gradient: 'bg-gradient-to-br from-amber-400 to-orange-600',
    },
    {
      icon: <TrendingUp className="h-4 w-4" />,
      label: 'Member Since',
      value: new Date(profile.updated_at || Date.now()).getFullYear(),
      description: 'Year joined',
      gradient: 'bg-gradient-to-br from-green-400 to-emerald-600',
    },
  ]

  return (
    <Card
      className={cn(
        'border-0 bg-gradient-to-br from-background via-background to-accent/5 backdrop-blur-sm',
        className
      )}
    >
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3">
          <div className="rounded-lg bg-primary/10 p-2">
            <BarChart3 className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h3 className="text-xl font-bold">Profile Statistics</h3>
            <p className="text-sm font-normal text-muted-foreground">
              Your activity overview
            </p>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 ">
          {stats.map((stat, index) => (
            <StatItem key={index} {...stat} />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
