'use client'

import { <PERSON>, Code2, Heart, TrendingUp } from 'lucide-react'

import { Badge } from '@/components/ui/badge'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface ActivityItem {
  id: string
  type: 'like' | 'submission' | 'update' | 'milestone'
  title: string
  description: string
  timestamp: string
  metadata?: {
    componentName?: string
    likes?: number
    category?: string
  }
}

interface ActivityCardProps {
  activities: ActivityItem[]
  className?: string
}

function ActivityItemComponent({ activity }: { activity: ActivityItem }) {
  const getIcon = () => {
    switch (activity.type) {
      case 'like':
        return <Heart className="h-4 w-4 text-red-500" />
      case 'submission':
        return <Code2 className="h-4 w-4 text-blue-500" />
      case 'update':
        return <Clock className="h-4 w-4 text-orange-500" />
      case 'milestone':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getTypeColor = () => {
    switch (activity.type) {
      case 'like':
        return 'bg-red-50 text-red-700 dark:bg-red-950 dark:text-red-300'
      case 'submission':
        return 'bg-blue-50 text-blue-700 dark:bg-blue-950 dark:text-blue-300'
      case 'update':
        return 'bg-orange-50 text-orange-700 dark:bg-orange-950 dark:text-orange-300'
      case 'milestone':
        return 'bg-green-50 text-green-700 dark:bg-green-950 dark:text-green-300'
      default:
        return 'bg-gray-50 text-gray-700 dark:bg-gray-950 dark:text-gray-300'
    }
  }

  return (
    <div className="group flex items-start gap-3 rounded-lg border bg-background/50 p-4 transition-all hover:bg-accent/5 hover:shadow-sm">
      <div className="mt-0.5 rounded-md bg-accent/10 p-2">{getIcon()}</div>

      <div className="flex-1 space-y-1">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium">{activity.title}</h4>
          <Badge variant="secondary" className={cn('text-xs', getTypeColor())}>
            {activity.type}
          </Badge>
        </div>

        <p className="text-sm text-muted-foreground">{activity.description}</p>

        {activity.metadata && (
          <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
            {activity.metadata.componentName && (
              <span>• {activity.metadata.componentName}</span>
            )}
            {activity.metadata.likes && (
              <span>• {activity.metadata.likes} likes</span>
            )}
            {activity.metadata.category && (
              <span>• {activity.metadata.category}</span>
            )}
          </div>
        )}

        <p className="text-xs text-muted-foreground">
          {new Date(activity.timestamp).toLocaleDateString()} at{' '}
          {new Date(activity.timestamp).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
          })}
        </p>
      </div>
    </div>
  )
}

export function ActivityCard({ activities, className }: ActivityCardProps) {
  // Mock data for demonstration
  const mockActivities: ActivityItem[] = [
    {
      id: '1',
      type: 'like',
      title: 'Liked a component',
      description: 'You liked the "Modern Card Component"',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      metadata: {
        componentName: 'Modern Card Component',
        category: 'UI Components',
      },
    },
    {
      id: '2',
      type: 'submission',
      title: 'Submitted new component',
      description: 'Your component "Profile Header" was submitted for review',
      timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      metadata: {
        componentName: 'Profile Header',
        category: 'Headers',
      },
    },
    {
      id: '3',
      type: 'milestone',
      title: 'Achievement unlocked',
      description: "You've reached 10 liked components!",
      timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: '4',
      type: 'update',
      title: 'Profile updated',
      description: 'You updated your profile information',
      timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ]

  const displayActivities = activities.length > 0 ? activities : mockActivities

  return (
    <Card
      className={cn(
        'border-0 bg-gradient-to-br from-background via-background to-accent/5',
        className
      )}
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-primary" />
          Recent Activity <small>(Coming Soon!)</small>
        </CardTitle>
      </CardHeader>
      <CardContent className="opacity-50">
        <div className="space-y-4">
          {displayActivities.slice(0, 5).map((activity) => (
            <ActivityItemComponent key={activity.id} activity={activity} />
          ))}

          {displayActivities.length === 0 && (
            <div className="rounded-lg border-2 border-dashed border-muted p-8 text-center">
              <Clock className="mx-auto h-8 w-8 text-muted-foreground/50" />
              <p className="mt-2 text-sm text-muted-foreground">
                No recent activity to display
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
