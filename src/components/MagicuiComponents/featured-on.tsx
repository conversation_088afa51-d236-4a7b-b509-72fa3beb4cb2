import Image from 'next/image'

import Marquee from '@/components/magicui/marquee'
import {
  featuredCompanies,
  FeaturedCompany,
} from '@/constants/featured-companies'

import LinkItem from '../LInkItem/LinkItem'

export default function FeaturedOn() {
  const firstRow = featuredCompanies.slice(0, featuredCompanies.length / 2)
  const secondRow = featuredCompanies.slice(featuredCompanies.length / 2)

  return (
    <>
      <div className="relative flex w-full flex-col  items-center justify-center overflow-hidden border-y bg-background  py-10 md:py-20 md:shadow-xl">
        <h2 className="bg-gradient-to-tr from-zinc-400/50 via-white to-white/60 bg-clip-text pb-4 text-center  font-heading text-4xl font-medium text-transparent  md:pb-10 md:text-5xl lg:text-7xl ">
          Featured By World
          <br />
          Top Companies
        </h2>
        <Marquee pauseOnHover className="[--duration:30s]">
          {firstRow.map((feature) => (
            <FeaturedOnCard key={feature.name} feature={feature} />
          ))}
        </Marquee>
        <Marquee reverse pauseOnHover className="[--duration:30s]">
          {secondRow.map((feature) => (
            <FeaturedOnCard key={feature.name} feature={feature} />
          ))}
        </Marquee>
        <div className="pointer-events-none absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-white dark:from-background" />
        <div className="pointer-events-none absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-white dark:from-background" />
      </div>
    </>
  )
}

const FeaturedOnCard = ({ feature }: { feature: FeaturedCompany }) => {
  return (
    <LinkItem
      href={feature.url}
      target="_blank"
      rel="noreferrer"
      className="flex w-full cursor-pointer flex-col items-center justify-center gap-4 rounded-md px-4 py-2 md:flex-row md:gap-8 md:px-8 md:py-4"
    >
      <div className="relative w-full max-w-[300px]">
        <Image
          src={feature.image}
          alt={feature.name}
          width={600}
          height={300}
          className="h-full w-32 object-cover md:w-44"
        />
      </div>
      <div className=" hidden flex-col gap-4">
        <h3 className="font heading  text-2xl font-medium text-zinc-900 dark:text-zinc-100">
          {feature.name}
        </h3>
      </div>
    </LinkItem>
  )
}
