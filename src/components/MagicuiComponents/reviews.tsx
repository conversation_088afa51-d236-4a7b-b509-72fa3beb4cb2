import Image from 'next/image'

import Marquee from '@/components/magicui/marquee'
import { cn } from '@/lib/utils'

const reviews = [
  {
    name: '<PERSON>',
    username: '@alex_designs',
    body: 'CopyElement has completely transformed how I build websites with Elementor. The free components are a game-changer!',
    img: 'https://avatar.vercel.sh/alex',
  },
  {
    name: '<PERSON>',
    username: '@sophia_creates',
    body: "I can't believe the quality of components available on CopyElement, and many of them are free! It's a must-have for any designer.",
    img: 'https://avatar.vercel.sh/sophia',
  },
  {
    name: '<PERSON>',
    username: '@ethan_builds',
    body: 'I upgraded to the Pro Plan, and it’s worth every cent. The exclusive components and priority support are fantastic!',
    img: 'https://avatar.vercel.sh/ethan',
  },
  {
    name: '<PERSON>',
    username: '@liam_dev',
    body: 'The Pro Plan on CopyElement has helped me deliver projects faster. The variety of premium components is unmatched.',
    img: 'https://avatar.vercel.sh/liam',
  },
  {
    name: '<PERSON>',
    username: '@emma_design',
    body: 'As a freelancer, CopyElement Pro has been a lifesaver. The premium components have helped me land bigger clients.',
    img: 'https://avatar.vercel.sh/emma',
  },
  {
    name: 'Olivia',
    username: '@olivia_web',
    body: 'CopyElement Pro takes it to the next level. The advanced components and regular updates keep my designs fresh and unique.',
    img: 'https://avatar.vercel.sh/olivia',
  },
  {
    name: 'Nathan',
    username: '@nathan_creative',
    body: 'The free version is amazing, but the Pro Plan is where the magic happens. Access to exclusive components is a huge plus!',
    img: 'https://avatar.vercel.sh/nathan',
  },
  {
    name: 'Mia',
    username: '@mia_digital',
    body: 'The Pro Plan has become an essential tool in my workflow. The pre-built templates are so customizable and save me hours of work.',
    img: 'https://avatar.vercel.sh/mia',
  },
  {
    name: 'Noah',
    username: '@noah_webpro',
    body: 'I love the Pro Plan’s value. Priority support has been incredibly helpful, and the components always exceed expectations.',
    img: 'https://avatar.vercel.sh/noah',
  },
  {
    name: 'Ava',
    username: '@ava_uiux',
    body: 'CopyElement Pro is the secret behind my polished Elementor designs. The new components released every month keep me ahead of the curve.',
    img: 'https://avatar.vercel.sh/ava',
  },
]

const firstRow = reviews.slice(0, reviews.length / 2)
const secondRow = reviews.slice(reviews.length / 2)

const ReviewCard = ({
  img,
  name,
  username,
  body,
}: {
  img: string
  name: string
  username: string
  body: string
}) => {
  return (
    <figure
      className={cn(
        'relative w-64 cursor-pointer overflow-hidden rounded-lg border p-4 md:w-72 xl:w-80',
        // light styles
        'border-gray-950/[.1] bg-gray-950/[.01] hover:bg-gray-950/[.05]',
        // dark styles
        'dark:border-gray-50/[.1] dark:bg-gray-50/[.10] dark:hover:bg-gray-50/[.15]'
      )}
    >
      <div className="flex flex-row items-center gap-2">
        <Image
          className="rounded-full"
          width={32}
          height={32}
          alt=""
          src={img}
        />
        {/* <img className="rounded-full" width="32" height="32" alt="" src={img} /> */}
        <div className="flex flex-col">
          <figcaption className="text-sm font-medium dark:text-white">
            {name}
          </figcaption>
          <p className="text-xs font-medium dark:text-white/40">{username}</p>
        </div>
      </div>
      <blockquote className="mt-2 text-sm">{body}</blockquote>
    </figure>
  )
}

export function Reviews() {
  return (
    <>
      <div className="relative flex w-full flex-col  items-center justify-center overflow-hidden border-y bg-background  py-10 md:py-20 md:shadow-xl">
        <h2 className="bg-gradient-to-tr from-zinc-400/50 via-white to-white/60 bg-clip-text pb-4 text-center  font-heading text-4xl font-medium text-transparent  md:pb-10 md:text-5xl lg:text-7xl ">
          {/* Customer Reviews <br /> for CopyElement */}
          What Our Users Are Saying <br /> About CopyElement
        </h2>
        <Marquee pauseOnHover className="[--duration:20s]">
          {firstRow.map((review) => (
            <ReviewCard key={review.username} {...review} />
          ))}
        </Marquee>
        <Marquee reverse pauseOnHover className="[--duration:20s]">
          {secondRow.map((review) => (
            <ReviewCard key={review.username} {...review} />
          ))}
        </Marquee>
        <div className="pointer-events-none absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-white dark:from-background" />
        <div className="pointer-events-none absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-white dark:from-background" />
      </div>
    </>
  )
}
