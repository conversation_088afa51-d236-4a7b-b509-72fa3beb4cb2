import Image from 'next/image'
import React from 'react'

type Props = {
  image?: string
  text?: string
}

const NotFound = ({ image, text }: Props) => {
  return (
    <div className="flex w-full items-center justify-center overflow-hidden">
      <div>
        <Image
          src={
            image
              ? image
              : 'https://utfs.io/f/LhUzHyAuqPAKLxzgEeAuqPAKXBhwfzrNUm4sYecxMvIWR90t'
          }
          width="500"
          height="500"
          alt="Search results not found."
        />
        <h3 className="w-full -translate-y-10 text-center text-xl font-bold tracking-widest md:text-3xl">
          {text ? text : 'No Results Found'}
        </h3>
      </div>
    </div>
  )
}

export default NotFound
