'use client'
import { initializePaddle, Paddle } from '@paddle/paddle-js'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'

import { useProfileStore } from '@/stores/profile-store'

import { Button } from '../ui/button'

const PaddlePayment = () => {
  // Create a local state to store Paddle instance
  const [paddle, setPaddle] = useState<Paddle>()

  const { profile } = useProfileStore()

  // Download and initialize Paddle instance from CDN
  useEffect(() => {
    initializePaddle({
      environment: 'production' || process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT!,
      token: process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN!,
    }).then((paddleInstance: Paddle | undefined) => {
      if (paddleInstance) {
        setPaddle(paddleInstance)
      }
    })
  }, [])

  // Callback to open a checkout
  const openCheckout = () => {
    if (!paddle) {
      // console.error('Paddle instance is not initialized')
      toast.error('Paddle is not initialized yet. Please try again later.')
      return
    }
    paddle?.Checkout.open({
      items: [{ priceId: 'pri_01jyjkdbjyhp3gkw8zq7tn3jec' }],
      settings: {
        displayMode: 'overlay',
        theme: 'light',
        successUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/payment-success`,
      },
      customData: {
        userId: profile?.id,
        email: profile?.email,
      },
    })
  }
  return (
    <>
      <Button onClick={openCheckout} className="w-full" disabled={!paddle}>
        {paddle ? 'Pay with Paddle' : 'Loading Paddle...'}
      </Button>
    </>
  )
}

export default PaddlePayment
