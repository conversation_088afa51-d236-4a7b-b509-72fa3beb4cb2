'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { useState, useEffect } from 'react'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

interface TimeLeft {
  days: number
  hours: number
  minutes: number
  seconds: number
}

interface DiscountTimerProps {
  endDate: Date
  discountPercentage: number
}

interface TimeUnit {
  label: string
  value: number
}

// Utility function moved outside the component
const getTimeLeft = (end: Date): TimeLeft => {
  const difference = +end - +new Date()
  if (difference > 0) {
    return {
      days: Math.floor(difference / (1000 * 60 * 60 * 24)),
      hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
      minutes: Math.floor((difference / 1000 / 60) % 60),
      seconds: Math.floor((difference / 1000) % 60),
    }
  }
  return { days: 0, hours: 0, minutes: 0, seconds: 0 }
}

export function DiscountTimer({
  endDate,
  discountPercentage,
}: DiscountTimerProps): JSX.Element {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>(() => getTimeLeft(endDate))

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(getTimeLeft(endDate))
    }, 1000)

    return () => clearInterval(timer)
  }, [endDate])

  const timeUnits: TimeUnit[] = [
    { label: 'Days', value: timeLeft.days },
    { label: 'Hours', value: timeLeft.hours },
    { label: 'Minutes', value: timeLeft.minutes },
    { label: 'Seconds', value: timeLeft.seconds },
  ]

  return (
    <Card className="mx-auto w-full max-w-md bg-gradient-to-br  text-white shadow-lg">
      {/* <CardHeader className="pb-2">
        <CardTitle className="bg-gradient-to-r from-yellow-300 to-red-300 bg-clip-text text-center text-3xl font-extrabold text-transparent">
          Flash Sale!
        </CardTitle>
      </CardHeader> */}
      <CardContent className="p-4 px-6">
        {/* <motion.div
          initial={{ scale: 0.5, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <Badge className="mx-auto mb-6 block w-fit bg-white px-4 py-2 text-2xl font-bold text-purple-600">
            {discountPercentage}% OFF
          </Badge>
        </motion.div> */}
        <div className="grid grid-cols-4 gap-2">
          {timeUnits.map(({ label, value }) => (
            <div
              key={label}
              className="rounded-lg border border-gray-600 border-input bg-black/50 p-2 text-center"
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={value}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                  className="mb-1 text-4xl font-bold"
                >
                  {value}
                </motion.div>
              </AnimatePresence>
              <div className="text-sm text-gray-200">{label}</div>
            </div>
          ))}
        </div>
        <motion.p
          className="mt-2 text-center text-lg font-semibold"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          Hurry! Limited Time Offer
        </motion.p>
      </CardContent>
    </Card>
  )
}
