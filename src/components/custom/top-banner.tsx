'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Rocket } from 'lucide-react'
import Link from 'next/link'
import { useState, useEffect, useRef } from 'react'
import ReactConfetti from 'react-confetti'

import { But<PERSON> } from '@/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

export function TopBanner() {
  const [isVisible, setIsVisible] = useState(true)
  const [isMounted, setIsMounted] = useState(false)
  const [isCopied, setIsCopied] = useState(false)
  const [isConfettiActive, setIsConfettiActive] = useState(false)
  const bannerRef = useRef<HTMLDivElement>(null)
  const couponCode = 'EARLYBIRD'

  useEffect(() => {
    // Check localStorage to see if the user has previously closed the banner
    const isBannerClosed = localStorage.getItem('isBannerClosed')
    if (isBannerClosed === 'true') {
      setIsVisible(false)
    }
    setIsMounted(true)
    // Trigger confetti after a short delay
    const timer = setTimeout(() => setIsConfettiActive(true), 500)
    return () => clearTimeout(timer)
  }, [])

  const copyToClipboard = () => {
    navigator.clipboard.writeText(couponCode).then(() => {
      setIsCopied(true)
      setTimeout(() => setIsCopied(false), 2000)
    })
  }

  const handleCloseBanner = () => {
    setIsVisible(false)
    // Store the user's preference in localStorage
    localStorage.setItem('isBannerClosed', 'true')
  }

  if (!isMounted) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          ref={bannerRef}
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.5 }}
          className="absolute left-0 right-0 top-0 z-[999] overflow-hidden"
        >
          {isConfettiActive && (
            <ReactConfetti
              width={bannerRef.current?.offsetWidth || 300}
              height={bannerRef.current?.offsetHeight || 100}
              recycle={false}
              numberOfPieces={100}
              gravity={0.1}
              initialVelocityY={20}
              confettiSource={{
                x: 0,
                y: 0,
                w: bannerRef.current?.offsetWidth || 300,
                h: 0,
              }}
            />
          )}
          <div className="relative z-[999] bg-gradient-to-r from-orange-500 via-red-600 to-purple-700 py-3 text-white shadow-lg">
            <div className="container flex w-full flex-col items-center justify-between space-y-2 px-4 sm:flex-row sm:space-y-0 md:px-6">
              {/* Left */}
              <div className="flex items-center gap-2 md:flex-1">
                <Rocket className="h-6 w-6 animate-pulse text-yellow-300" />
                <p className="text-lg font-bold sm:text-xl md:text-2xl">
                  Early Access Special
                </p>
              </div>

              {/* Middle */}
              <div className="flex flex-col items-center justify-center md:flex-1">
                <p className="text-center text-sm font-medium md:text-base">
                  🎉 Early Access Special: Celebrate Our Launch with 40% OFF!
                </p>
                {/* <span className="mt-1 rounded-full bg-yellow-400 px-3 py-1 text-xs font-bold uppercase tracking-wider text-black md:text-sm">
                  40% OFF All Plans
                </span> */}
              </div>

              {/* Right */}
              <div className="flex items-center justify-end space-x-2 md:flex-1">
                <TooltipProvider>
                  <Tooltip open={isCopied}>
                    <TooltipTrigger asChild>
                      <button
                        onClick={copyToClipboard}
                        className="group flex items-center justify-center gap-2 rounded-lg bg-white/10 px-4 py-2 text-sm font-medium transition-colors duration-300 hover:bg-white/20"
                      >
                        <span className="font-mono">{couponCode}</span>
                        <Copy className="h-4 w-4 transition-colors duration-300 group-hover:text-yellow-300" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom">
                      <p>{isCopied ? 'Copied!' : 'Click to copy'}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <Button
                  asChild
                  className="hidden bg-yellow-400 text-black hover:bg-yellow-500 sm:flex"
                >
                  <Link href="/pricing">GET NOW</Link>
                </Button>
                <Button
                  onClick={handleCloseBanner}
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20"
                  aria-label="Close Year-End sale banner"
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
          <div className="absolute inset-0 bg-gradient-to-r from-orange-500/50 via-red-600/50 to-purple-700/50 blur-2xl" />
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// export function TopBanner1() {
//   const [isVisible, setIsVisible] = useState(true)
//   const [isMounted, setIsMounted] = useState(false)
//   const [isCopied, setIsCopied] = useState(false)
//   const [isConfettiActive, setIsConfettiActive] = useState(false)

//   useEffect(() => {
//     setIsMounted(true)
//     // Trigger confetti after a short delay
//     const timer = setTimeout(() => setIsConfettiActive(true), 500)
//     return () => clearTimeout(timer)
//   }, [])

//   const copyToClipboard = () => {
//     navigator.clipboard.writeText('NEWYEAR2024').then(() => {
//       setIsCopied(true)
//       setTimeout(() => setIsCopied(false), 2000)
//     })
//   }

//   if (!isMounted) return null

//   return (
//     <AnimatePresence>
//       {isVisible && (
//         <motion.div
//           initial={{ opacity: 0, y: -50 }}
//           animate={{ opacity: 1, y: 0 }}
//           exit={{ opacity: 0, y: -50 }}
//           transition={{ duration: 0.5 }}
//           className="absolute left-0 right-0 top-0 z-[999]"
//         >
//           {isConfettiActive && (
//             <ReactConfetti
//               width={typeof window !== 'undefined' ? window.innerWidth : 300}
//               height={typeof window !== 'undefined' ? window.innerHeight : 200}
//               recycle={false}
//               numberOfPieces={200}
//               gravity={0.2}
//             />
//           )}
//           <div className="relative z-[999] bg-gradient-to-r from-blue-600 via-purple-600 to-pink-500 py-3 text-white shadow-lg">
//             <div className="container flex w-full flex-col items-center justify-between space-y-2 px-4 sm:flex-row sm:space-y-0 md:px-6">
//               {/* Left */}
//               <div className="flex items-center gap-2 md:flex-1">
//                 <Gift className="h-6 w-6 animate-bounce text-yellow-300" />
//                 <p className="text-lg font-bold sm:text-xl md:text-2xl">
//                   New Year, New You!
//                 </p>
//               </div>

//               {/* Middle */}
//               <div className="flex flex-col items-center justify-center md:flex-1">
//                 <p className="text-center text-sm font-medium md:text-base">
//                   Unlock Your Potential in 2024
//                 </p>
//                 <span className="mt-1 rounded-full bg-yellow-400 px-3 py-1 text-xs font-bold uppercase tracking-wider text-black md:text-sm">
//                   60% OFF All Plans
//                 </span>
//               </div>

//               {/* Right */}
//               <div className="flex items-center justify-end space-x-2 md:flex-1">
//                 <TooltipProvider>
//                   <Tooltip open={isCopied}>
//                     <TooltipTrigger asChild>
//                       <button
//                         onClick={copyToClipboard}
//                         className="group flex items-center justify-center gap-2 rounded-lg bg-white/10 px-4 py-2 text-sm font-medium transition-colors duration-300 hover:bg-white/20"
//                       >
//                         <span className="font-mono">NEWYEAR2024</span>
//                         <Copy className="h-4 w-4 transition-colors duration-300 group-hover:text-yellow-300" />
//                       </button>
//                     </TooltipTrigger>
//                     <TooltipContent side="bottom">
//                       <p>{isCopied ? 'Copied!' : 'Click to copy'}</p>
//                     </TooltipContent>
//                   </Tooltip>
//                 </TooltipProvider>
//                 <Button
//                   asChild
//                   className="hidden bg-yellow-400 text-black hover:bg-yellow-500 sm:flex"
//                 >
//                   <Link href="/pricing">Get Started</Link>
//                 </Button>
//                 <Button
//                   onClick={() => setIsVisible(false)}
//                   variant="ghost"
//                   size="icon"
//                   className="text-white hover:bg-white/20"
//                   aria-label="Close New Year sale banner"
//                 >
//                   <X className="h-5 w-5" />
//                 </Button>
//               </div>
//             </div>
//           </div>
//           <div className="absolute inset-0 bg-gradient-to-r from-blue-600/50 via-purple-600/50 to-pink-500/50 blur-2xl"></div>
//         </motion.div>
//       )}
//     </AnimatePresence>
//   )
// }
