'use client'

import { initializePaddle, Paddle } from '@paddle/paddle-js'
import { AnimatePresence, motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'

import { useProfileStore } from '@/stores/profile-store'

import PricingCard from '../cards/pricing-card'

type Tab = 'monthly' | 'yearly'

type Props = {
  data: any
  discountPercentage?: number
}
const PaddleEvironment = process.env.NEXT_PUBLIC_PADDLE_ENVIRONMENT as
  | 'production'
  | 'sandbox'

const PricingCards = ({ data, discountPercentage = 0 }: Props) => {
  const [activeTab, setActiveTab] = useState<Tab>('monthly')

  // Create a local state to store Paddle instance
  const [paddle, setPaddle] = useState<Paddle>()

  const { profile } = useProfileStore()
  const router = useRouter()

  // Download and initialize Paddle instance from CDN
  useEffect(() => {
    initializePaddle({
      environment: PaddleEvironment,
      token: process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN!,
    }).then((paddleInstance: Paddle | undefined) => {
      if (paddleInstance) {
        setPaddle(paddleInstance)
      }
    })
  }, [])

  // Callback to open a checkout
  const openCheckout = (priceId: string) => {
    if (!paddle) {
      // console.error('Paddle instance is not initialized')
      console.error('Paddle instance is not initialized')
      return
    }

    if (!profile) {
      // console.error('User profile is not available')
      toast.error('Please log in to continue with the purchase.')
      router.push('/login')
      return
    }

    paddle?.Checkout.open({
      items: [
        {
          priceId: priceId,
          // priceId: 'pri_01jyjkdbjyhp3gkw8zq7tn3jec',
        },
      ],
      settings: {
        displayMode: 'overlay',
        theme: 'light',
        successUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/payment-success`,
      },
      customData: {
        userId: profile?.id,
        email: profile?.email,
        name: profile?.first_name + ' ' + profile?.last_name,
      },
    })
  }

  // Filter plans based on active tab
  const filteredPlans = data?.filter((plan: { plan_type: string }) => {
    if (activeTab === 'yearly') return plan.plan_type !== 'pro-m'
    if (activeTab === 'monthly') return plan.plan_type !== 'pro-y'
    return true
  })

  return (
    <div className="mx-auto w-full max-w-7xl">
      <div className="mb-12 flex flex-col items-center gap-4">
        <div className="inline-flex items-center rounded-full border border-zinc-200 bg-white p-1.5 shadow-sm dark:border-zinc-700 dark:bg-zinc-800/50">
          {['monthly', 'yearly'].map((period) => (
            <button
              key={period}
              onClick={() => setActiveTab(period.toLowerCase() as Tab)}
              className={`rounded-full px-8 py-2.5 text-sm font-medium transition-all duration-300 ${
                period.toLowerCase() === activeTab
                  ? 'bg-zinc-900 text-white shadow-lg dark:bg-zinc-100 dark:text-zinc-900'
                  : 'text-zinc-600 hover:text-zinc-900 dark:text-zinc-400 dark:hover:text-zinc-100'
              }`}
            >
              {period}
            </button>
          ))}
        </div>
      </div>

      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.5 }}
        className="w-full"
      >
        <motion.div
          layout
          className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3"
        >
          <AnimatePresence mode="wait">
            {filteredPlans?.map((plan: any, index: number) => (
              <motion.div
                key={plan?.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{
                  delay: index * 0.1,
                  duration: 0.4,
                }}
              >
                <PricingCard
                  plan={plan}
                  activeTab={activeTab}
                  discountPercentage={discountPercentage}
                  openCheckout={openCheckout}
                />
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      </motion.div>
    </div>
  )
}

export default PricingCards
