import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
export interface FAQItem {
  question: string
  answer: string
}

interface FAQProps {
  items: FAQItem[]
}

export function FAQ({ items }: FAQProps) {
  return (
    <Accordion
      type="single"
      collapsible
      className="w-full space-y-3 text-start"
    >
      {/* <Accordion type="multiple"  className="w-full"> */}

      {items.map((item, index) => (
        <AccordionItem
          key={index}
          value={`item-${index}`}
          className="rounded-lg bg-card px-3 md:px-5"
        >
          <AccordionTrigger className=" text-base md:text-lg">
            {item.question}
          </AccordionTrigger>
          <AccordionContent className="text-sm tracking-wide md:text-base">
            {item.answer}
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  )
}
