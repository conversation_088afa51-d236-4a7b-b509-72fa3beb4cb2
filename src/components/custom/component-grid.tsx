'use client'
import Masonry from 'react-layout-masonry'

import { MainCard } from '@/components/cards/main-card'
import { ComponentWithCategory } from '@/types/custom'

type Props = {
  components: ComponentWithCategory[] | null
  isTemplate?: boolean
}

const ComponentGrid = ({ components, isTemplate = false }: Props) => {
  return (
    <Masonry
      // columns={3}
      columns={{ 640: 1, 768: 2, 1024: 3, 1480: 4 }}
      gap={16}
    >
      {components?.map((item) => {
        return (
          <MainCard key={item.id} component={item} isTemplate={isTemplate} />
        )
      })}
    </Masonry>
  )
}

export default ComponentGrid
