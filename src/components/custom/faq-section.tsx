'use client'
import { motion } from 'framer-motion'
import { ArrowRightIcon } from 'lucide-react'
import Link from 'next/link'

import { FAQ, FAQItem } from '@/components/custom/faq-component'
import Heading from '@/components/custom/heading'
import { Button } from '@/components/ui/button'

interface FAQSectionProps {
  items: FAQItem[]
  showAllFAQsButton?: boolean
  showContactSupport?: boolean
  title?: string
  description?: string
  className?: string
  maxItems?: number
}

export function FAQSection({
  items,
  showAllFAQsButton = false,
  showContactSupport = true,
  title = 'Frequently Asked Questions',
  description = 'Find answers to common questions about CopyElement.',
  className = '',
  maxItems,
}: FAQSectionProps) {
  const displayItems = maxItems ? items.slice(0, maxItems) : items

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`mt-28 min-h-fit bg-gradient-to-b ${className}`}
    >
      <div className="mb-3 w-full text-center">
        <Heading className="pb-0">
          {title.split(' ').map((word, i, arr) => (
            <span key={i}>
              {word}
              {i < arr.length - 1 && ' '}
              {i === arr.length - 2 && <br />}
            </span>
          ))}
        </Heading>
        <p className="py-2 text-sm text-muted-foreground md:text-lg">
          {description}
        </p>
      </div>

      <section className="container mx-auto mt-12 px-4 md:px-0">
        <div className="mx-auto max-w-3xl">
          <FAQ items={displayItems} />
        </div>

        <div className="mt-12 flex flex-col items-center justify-center gap-4">
          {showAllFAQsButton && items.length > (maxItems || 0) && (
            <Link prefetch={true} href="/faq">
              <Button variant="outline-primary" size="lg">
                View All FAQs
                <ArrowRightIcon className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          )}

          {showContactSupport && (
            <div className="text-center">
              <h2 className="mb-4 text-2xl font-semibold">
                Still have questions?
              </h2>
              <p className="mb-6">
                If you couldn&apos;t find the answer you were looking for,
                please contact our support team.
              </p>
              <Link prefetch={true} href="/contact-us">
                <Button>Contact Support</Button>
              </Link>
            </div>
          )}
        </div>
      </section>
    </motion.div>
  )
}
