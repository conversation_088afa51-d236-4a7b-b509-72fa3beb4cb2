import { ArrowRightIcon } from 'lucide-react'
import Link from 'next/link'

import <PERSON><PERSON><PERSON><PERSON> from '../global/animation-container'
import MaxWidthWrapper from '../global/max-width-wrapper'
import { But<PERSON> } from '../ui/button'
import { LampContainer } from '../ui/lamp'

const CTASection = () => {
  return (
    <MaxWidthWrapper className="max-w-[100vw] overflow-x-hidden pt-10 scrollbar-hide">
      <AnimationContainer delay={0.1}>
        <LampContainer>
          <div className="relative flex w-full flex-col items-center justify-center text-center">
            <h2 className="mt-8 bg-gradient-to-b from-neutral-200 to-neutral-400 bg-clip-text py-4 text-center font-heading text-4xl font-medium !leading-[1.15] tracking-tight text-transparent md:text-7xl">
              Build Website in hours, <br />
              not weeks
            </h2>
            <p className="mx-auto mt-6 max-w-md text-muted-foreground">
              Experience the cutting-edge solution that transforms how you
              handle your links. Elevate your online presence with our next-gen
              platform.
            </p>
            <div className="mt-6">
              <Button asChild>
                <Link prefetch={true} href={'/components'}>
                  Get started for free
                  <ArrowRightIcon className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </LampContainer>
      </AnimationContainer>
    </MaxWidthWrapper>
  )
}

export default CTASection
