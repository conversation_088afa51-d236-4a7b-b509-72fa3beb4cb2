'use client'

import { AnimatePresence, motion, useAnimation, useInView } from 'framer-motion'
import { ChevronRightIcon } from 'lucide-react'
import Link from 'next/link'
import { ReactNode, useEffect, useMemo, useRef, useState } from 'react'

import { Button } from '@/components/ui/button'
import { cn } from '@/utils'

import Heading from './heading'

const ANIMATION_INTERVAL = 2000

export default function HowItWorks() {
  const [isCmd, setIsCmd] = useState(false)
  const ref = useRef(null)
  const isInView = useInView(ref, { once: false, amount: 0.5 })
  const controls = useAnimation()

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null
    if (isInView) {
      controls.start('visible')
      interval = setInterval(() => {
        setIsCmd((prev) => !prev)
      }, ANIMATION_INTERVAL)
    } else {
      controls.start('hidden')
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isInView, controls])

  const animatedButton = useMemo(
    () => (
      <AnimatePresence mode="wait">
        <motion.span
          key={isCmd ? 'cmd' : 'ctrl'}
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -20, opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          {isCmd ? 'Cmd' : 'Ctrl'}
        </motion.span>
      </AnimatePresence>
    ),
    [isCmd]
  )

  return (
    <motion.section
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        visible: { opacity: 1, y: 0 },
        hidden: { opacity: 0, y: 50 },
      }}
      transition={{ duration: 0.5 }}
      className="relative w-full overflow-hidden bg-gradient-to-b from-background to-secondary/10 px-4 py-16 sm:px-6 lg:px-8"
    >
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.9))] dark:bg-grid-slate-700/25 dark:[mask-image:linear-gradient(0deg,rgba(255,255,255,0.1),rgba(255,255,255,0.7))]" />
      <div className="relative mx-auto max-w-4xl text-center">
        <Heading>How It Works!</Heading>
        <p className="mx-auto mt-3 max-w-2xl font-heading text-lg font-medium text-primary-foreground md:text-xl lg:text-2xl xl:text-3xl">
          Just Copy and Paste the code of the component you want to use in your
          project. It&apos;s that simple!
        </p>

        <motion.div
          className="mx-auto mt-12 flex max-w-fit flex-col items-center justify-center gap-4 rounded-2xl bg-card p-8 shadow-xl md:flex-row"
          role="group"
          aria-label="Keyboard shortcut demonstration"
        >
          <div className="flex items-center gap-4">
            <RetroButton
              wide
              aria-label={`${isCmd ? 'Command' : 'Control'} key`}
            >
              {animatedButton}
            </RetroButton>
            <RetroButton aria-label="C key">C</RetroButton>
          </div>

          <span
            className="text-3xl font-bold text-muted-foreground"
            aria-hidden="true"
          >
            +
          </span>

          <div className="flex items-center gap-4">
            <RetroButton
              wide
              aria-label={`${isCmd ? 'Command' : 'Control'} key`}
            >
              {animatedButton}
            </RetroButton>
            <RetroButton aria-label="V key">V</RetroButton>
          </div>
        </motion.div>

        <div className="mt-8 flex justify-center gap-4">
          <Button
            variant={'gradient-primary'}
            className="font-semibold"
            size={'hero'}
            asChild
          >
            <Link prefetch={true} href="/components/free">
              Try Now
              <ChevronRightIcon className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </motion.section>
  )
}

interface RetroButtonProps {
  children: ReactNode
  wide?: boolean
  'aria-label': string
}

export const RetroButton = ({
  children,
  wide = false,
  'aria-label': ariaLabel,
}: RetroButtonProps) => {
  return (
    <motion.button
      aria-label={ariaLabel}
      whileHover={{ translateY: -2, translateX: -2 }}
      whileTap={{ translateY: 0, translateX: 0 }}
      className={cn(
        'relative overflow-hidden rounded-xl border-2 border-border bg-card px-4 py-2 font-heading text-2xl font-bold text-foreground shadow-[4px_4px_0_0_rgba(15,23,42,0.2)] transition-all hover:shadow-[2px_2px_0_0_rgba(15,23,42,0.2)] active:shadow-none',
        wide ? 'h-14 w-32 md:h-16 md:w-40' : 'h-12 w-20 md:h-14 md:w-24',
        'after:absolute after:inset-0 after:z-[-1] after:translate-y-[200%] after:bg-gradient-to-t after:from-violet-500 after:to-violet-300 after:transition-transform hover:text-white hover:after:translate-y-0'
      )}
    >
      {children}
    </motion.button>
  )
}
