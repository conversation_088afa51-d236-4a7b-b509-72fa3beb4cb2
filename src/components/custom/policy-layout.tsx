import Link from 'next/link'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'

import AnimationContainer from '../global/animation-container'

import type { ReactNode } from 'react'

interface PolicyLayoutProps {
  children: ReactNode
  title: string
}

export default function PolicyLayout({ children, title }: PolicyLayoutProps) {
  return (
    <AnimationContainer className="container px-4 py-24 sm:px-6 lg:px-8">
      <Card className="mx-auto max-w-5xl">
        <CardHeader>
          <CardTitle className="text-center text-3xl font-bold">
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>{children}</CardContent>
      </Card>
    </AnimationContainer>
  )
}
