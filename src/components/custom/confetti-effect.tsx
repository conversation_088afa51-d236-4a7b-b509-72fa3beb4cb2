'use client'

import { useEffect, useState } from 'react'
import Confetti from 'react-confetti'

interface ConfettiEffectProps {
  duration?: number
  colors?: string[]
  recycle?: boolean
  numberOfPieces?: number
}

export function ConfettiEffect({
  duration = 8000,
  colors = [
    '#10B981',
    '#059669',
    '#047857',
    '#065F46',
    '#FFD700',
    '#FFA500',
    '#FF6B6B',
    '#4ECDC4',
  ],
  recycle = false,
  numberOfPieces = 200,
}: ConfettiEffectProps) {
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 })
  const [isVisible, setIsVisible] = useState(true)
  const [opacity, setOpacity] = useState(1)

  useEffect(() => {
    const { innerWidth: width, innerHeight: height } = window
    setDimensions({ width, height })

    // Start fading out confetti after 75% of duration
    const fadeTimer = setTimeout(() => {
      setOpacity(0.5)
    }, duration * 0.75)

    // Hide confetti completely after full duration
    const hideTimer = setTimeout(() => {
      setIsVisible(false)
    }, duration)

    const handleResize = () => {
      setDimensions({ width: window.innerWidth, height: window.innerHeight })
    }

    window.addEventListener('resize', handleResize)

    return () => {
      clearTimeout(fadeTimer)
      clearTimeout(hideTimer)
      window.removeEventListener('resize', handleResize)
    }
  }, [duration])

  if (!isVisible) return null

  return (
    <div
      style={{
        opacity,
        transition: 'opacity 2s ease-out',
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 9999,
      }}
    >
      <Confetti
        width={dimensions.width}
        height={dimensions.height}
        recycle={recycle}
        numberOfPieces={numberOfPieces}
        colors={colors}
        gravity={0.1}
        wind={0.05}
        initialVelocityX={5}
        initialVelocityY={20}
        confettiSource={{
          x: 0,
          y: 0,
          w: dimensions.width,
          h: 0,
        }}
      />
    </div>
  )
}
