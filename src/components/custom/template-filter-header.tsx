import { <PERSON><PERSON><PERSON>, Star, Zap } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface FilterOption {
  label: string
  value: string
  href: string
}

interface TemplateFilterHeaderProps {
  title?: string
  description?: string
  image?: string
  currentFilter?: string
  filters?: FilterOption[]
  basePath?: string
  className?: string
  hideFilters?: boolean
}

export default function TemplateFilterHeader({
  title = 'Templates',
  description = "A template section highlights the unique and significant aspects of your product or service. Effectively communicate what makes your product valuable, the benefits of what you offer and why it's worth considering.",
  image,
  currentFilter = 'all',
  filters,
  basePath = '/templates',
  className,
  hideFilters = false,
}: TemplateFilterHeaderProps) {
  // Default filters if none provided
  const defaultFilters: FilterOption[] = [
    { label: 'All', value: 'all', href: basePath },
    { label: 'Pro', value: 'pro', href: `${basePath}?filter=pro` },
    { label: 'Free', value: 'free', href: `${basePath}?filter=free` },
  ]

  const filterOptions = filters || defaultFilters

  // Get appropriate icon based on title
  const getHeaderIcon = () => {
    if (
      title.toLowerCase().includes('premium') ||
      title.toLowerCase().includes('pro')
    ) {
      return <Star className="h-5 w-5 text-amber-500" />
    }
    if (title.toLowerCase().includes('free')) {
      return <Zap className="h-5 w-5 text-green-500" />
    }
    if (title.toLowerCase().includes('popular')) {
      return <Sparkles className="h-5 w-5 text-purple-500" />
    }
    return <Sparkles className="h-5 w-5 text-purple-500" />
  }

  return (
    <div
      className={cn(
        'relative mb-4 w-full overflow-hidden rounded-sm  bg-gradient-to-br from-primary/50 to-card/40 shadow-sm backdrop-blur-sm md:rounded-lg',
        className
      )}
    >
      {/* Background Image */}
      <Image
        // src="https://gallery.theportfolio.in/background-images/1743147142349-background-2.webp"
        src="https://gallery.theportfolio.in/background-images/1750953291767-section-bg-2.webp"
        alt="Background"
        fill
        className="absolute inset-0 object-cover opacity-20"
      />

      {/* Header Content */}
      <div className="relative flex flex-col items-center gap-6 p-6 md:flex-row md:gap-8 md:p-8">
        {/* Left Side: Text and Filters */}
        <div className="flex w-full flex-col justify-center text-center md:w-auto md:text-left">
          <div className="flex items-center gap-3">
            {/* Icon */}
            {getHeaderIcon() && (
              <div className="mb-3 flex justify-center md:justify-start">
                <div className="flex h-10 w-10 items-center justify-center rounded-lg border border-border/30 bg-background/50 shadow-md backdrop-blur-sm">
                  {getHeaderIcon()}
                </div>
              </div>
            )}

            {/* Title with gradient */}
            <h1 className="mb-3 bg-gradient-to-r from-foreground via-foreground/90 to-foreground/80 bg-clip-text text-2xl font-bold tracking-tight text-transparent md:text-4xl">
              {title}
            </h1>
          </div>

          {/* Description */}
          <p className="mb-5 text-sm leading-relaxed text-accent-foreground md:max-w-[90%] md:text-base">
            {description}
          </p>

          {/* Filter Buttons */}
          {!hideFilters && (
            <div className="flex flex-wrap justify-center gap-2 md:justify-start">
              {filterOptions.map((filter) => {
                const isActive = currentFilter === filter.value
                return (
                  <Button
                    asChild
                    key={filter.value}
                    variant={isActive ? 'gradient-primary' : 'outline-primary'}
                    className="min-w-[60px] bg-primary/30"
                  >
                    <Link prefetch={true} href={filter.href}>
                      <span className="relative z-10">{filter.label}</span>
                    </Link>
                  </Button>
                )
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
