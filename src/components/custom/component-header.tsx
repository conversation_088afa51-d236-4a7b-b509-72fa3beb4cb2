'use client'

import { useQuery } from '@tanstack/react-query'
import Image from 'next/image'
import Link from 'next/link'

import ComponentSearch from '@/components/accertinity/component-search'
import { WatchTutorialDialog } from '@/components/dialogs/watch-tutorial'
import { UserNav } from '@/components/layout/user-nav'
import { Button } from '@/components/ui/button'
import MagicBadge from '@/components/ui/magic-badge'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu'
import { useProfileStore } from '@/stores/profile-store'
import { createClient } from '@/utils/supabase/client'

interface RouteProps {
  href: string
  label: string
}

interface FeatureProps {
  title: string
  description: string
}

const routeList: RouteProps[] = [
  {
    href: '/components',
    label: 'Components',
  },
  {
    href: '/pricing',
    label: 'Pricing',
  },
  {
    href: '/contact',
    label: 'Contact',
  },
  {
    href: '/faq',
    label: 'FAQ',
  },
]

const featureList: FeatureProps[] = [
  {
    title: 'Showcase Your Value ',
    description: 'Highlight how your product solves user problems.',
  },
  {
    title: 'Build Trust',
    description:
      'Leverages social proof elements to establish trust and credibility.',
  },
  {
    title: 'Capture Leads',
    description:
      'Make your lead capture form visually appealing and strategically.',
  },
]

interface ComponentHeaderProps {
  isTemplate?: boolean
}

export const ComponentHeader = ({
  isTemplate = false,
}: ComponentHeaderProps) => {
  const { profile } = useProfileStore()
  const supabase = createClient()

  const { isLoading, error, data } = useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const { data } = await supabase.auth.getUser()
      return data
    },
  })

  const user = data?.user

  return (
    <header className="bg-opacity-15 sticky left-0 right-0 top-0 z-40 mx-auto flex w-full items-center justify-between gap-3 rounded-lg border border-secondary bg-card p-2 shadow-inner md:gap-5">
      <div className="w-full md:max-w-lg">
        <ComponentSearch isTemplate={isTemplate} />
      </div>

      <div className="hidden items-center justify-center gap-3 md:flex">
        {/* show components or templates button based on condition */}
        {isTemplate ? (
          <Button
            asChild
            aria-label="Components"
            size={'sm'}
            variant={'gradient-primary'}
            className="flex items-center gap-2"
          >
            <Link prefetch={true} aria-label="Components" href="/components">
              ✨ Components
            </Link>
          </Button>
        ) : (
          <Button
            asChild
            aria-label="Templates"
            size={'sm'}
            variant={'gradient-primary'}
            className="flex items-center gap-2"
          >
            <Link prefetch={true} aria-label="Templates" href="/templates">
              ✨ Templates
            </Link>
          </Button>
        )}
        <WatchTutorialDialog />
        {!profile?.is_pro && (
          <Link
            aria-label="Login button"
            href="/pricing"
            className="flex items-center gap-2"
          >
            <MagicBadge title="✨ Get PRO" />
          </Link>
        )}
        {user ? (
          <UserNav />
        ) : (
          <Button asChild aria-label="Login" size={'sm'}>
            <Link prefetch={true} aria-label="Login button" href="/login">
              Login
            </Link>
          </Button>
        )}
      </div>
    </header>
  )
}

export const NavigatinoMenuDesktop = () => {
  return (
    <NavigationMenu className="mx-auto hidden lg:block">
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger className="bg-card text-sm text-primary dark:text-purple-200">
            Features
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="grid w-[600px] grid-cols-2 gap-5 p-4">
              <Image
                src="https://avatars.githubusercontent.com/u/75042455?v=4"
                alt="RadixLogo"
                className="h-full w-full rounded-md object-cover"
                width={600}
                height={600}
              />
              <ul className="flex flex-col gap-2">
                {featureList.map(({ title, description }) => (
                  <li
                    key={title}
                    className="rounded-md p-3 text-sm hover:bg-muted"
                  >
                    <p className="mb-1 font-semibold leading-none text-foreground">
                      {title}
                    </p>
                    <p className="line-clamp-2 text-muted-foreground">
                      {description}
                    </p>
                  </li>
                ))}
              </ul>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>

        <NavigationMenuItem>
          {routeList.map(({ href, label }) => (
            <NavigationMenuLink key={href} asChild>
              <Link
                prefetch={true}
                href={href}
                className="px-2 text-sm text-primary transition-all hover:opacity-70 dark:text-purple-200 "
              >
                {label}
              </Link>
            </NavigationMenuLink>
          ))}
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  )
}
