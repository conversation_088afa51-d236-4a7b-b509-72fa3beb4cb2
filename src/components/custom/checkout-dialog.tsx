/* eslint-disable import/order */
//@ts-nocheck

'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useQuery } from '@tanstack/react-query'
import {
  BadgeDollarSign,
  BadgeIndianRupee,
  CheckCircleIcon,
  X,
} from 'lucide-react'
import Image from 'next/image'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import * as z from 'zod'

import { fetchLocation } from '@/actions/locastions'
import { fetchSubscriptionPlan } from '@/actions/subscriptions'
import { CountrySelector } from '@/components/custom/country-selector'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader } from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Separator } from '@/components/ui/separator'
import { moneyFormatter } from '@/utils/functions/money-formatter'
import { EXCHANGE_RATE } from '@/utils/payment/phonepe'
import { createClient } from '@/utils/supabase/client'

import { Badge } from '../ui/badge'
import { InputForm } from '../ui/input/input-form'
import Heading from './heading'

const formSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Enter a valid email address' }),
  country: z.string().min(1, { message: 'Please select a country' }),
  countryCode: z.string().optional(),
  phoneNumber: z.string().optional(),
  // phoneNumber: z.string().min(10, { message: 'Enter a valid phone number' }),
  paymentMethod: z.enum(['paypal', 'phonepe']),
})

type CheckoutFormValues = z.infer<typeof formSchema>

export default function CheckoutDialog() {
  const [loading, setLoading] = useState(false)
  const [showCouponInput, setShowCouponInput] = useState(true)
  const [couponCode, setCouponCode] = useState('')
  const [appliedCoupon, setAppliedCoupon] = useState('')
  // const [discount, setDiscount] = useState(0)
  const [discountPercentage, setDiscountPercentage] = useState(0)
  const searchParams = useSearchParams()
  const planId = searchParams.get('plan') || ''
  const [isOpen, setIsOpen] = useState(false)
  const router = useRouter()

  useEffect(() => {
    setIsOpen(searchParams.get('checkout') === 'true')
  }, [searchParams])

  const supabase = createClient()

  const {
    isLoading: planLoading,
    error: planError,
    data: plan,
  } = useQuery({
    queryKey: ['plan', planId],
    queryFn: () => fetchSubscriptionPlan(planId),
    enabled: !!planId,
  })

  // console.log('plan to buy', plan)

  const {
    isLoading: userLoading,
    error: userError,
    data: userResult,
  } = useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      // const { data } = await supabase.from('categories').select('*').range(0, 9)
      const { data } = await supabase.auth.getUser()
      form.setValue('email', data?.user?.email ?? '')
      form.setValue(
        'name',
        data?.user?.user_metadata?.first_name &&
          data?.user?.user_metadata?.last_name
          ? `${data.user.user_metadata.first_name} ${data.user.user_metadata.last_name}`
          : data?.user?.user_metadata?.display_name ?? ''
      )
      return data
    },
  })
  const {
    isLoading: locationLoading,
    error: locationError,
    data: locationResult,
  } = useQuery({
    queryKey: ['location'],
    queryFn: async () => fetchLocation(),
  })

  const user = userResult?.user

  // console.log('data', plan)
  // console.log('locatoin Result', locationResult)

  const handleClose = () => {
    setIsOpen(false)
    router.push('/pricing') // Adjust this to your pricing page route
  }

  const form = useForm<CheckoutFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: user ? user.email : '',
      country: 'US',
      // countryCode: '+91',
      countryCode: '+1',
      phoneNumber: '',
      paymentMethod: 'paypal',
    },
  })

  const paymentMethod = form.getValues('paymentMethod')

  const exchangeRate = paymentMethod === 'paypal' ? 1 : EXCHANGE_RATE // Default to 1 for PayPal
  const currency = paymentMethod === 'paypal' ? 'USD' : 'INR'

  // const ppp = locationResult?.ppp || 1 //changing it for now as the pricing page is static build now
  const ppp = 1

  //ignore type checking for now
  const baseSubtotal = plan ? (plan?.unit_amount * ppp) / 100 : 99 // Base subtotal without exchange rate
  let subtotal = baseSubtotal * exchangeRate // Adjust subtotal with exchange rate

  const discount =
    discountPercentage > 0
      ? Math.round(((baseSubtotal * discountPercentage) / 100) * exchangeRate)
      : 0 // Adjust discount with exchange rate

  const subTotalAfterDiscount = subtotal - discount

  const taxPercentage = locationResult?.taxRate || 10
  // const tax = Math.round((subTotalAfterDiscount * taxPercentage) / 100) // Adjust tax with converted subtotal
  const tax = 0 // Adjust tax with converted subtotal

  const total =
    paymentMethod === 'paypal'
      ? (subtotal - discount + tax).toFixed(2)
      : Math.round(subtotal - discount + tax) // Final total with all adjustments

  const handleApplyCoupon = async (e) => {
    e.preventDefault()
    if (!couponCode) {
      toast.error('Please enter a valid coupon code.')
      return
    }
    if (appliedCoupon === couponCode) {
      toast.error('Coupon already applied.')
      return
    }
    try {
      const { data: coupon, error } = await supabase
        .from('coupons')
        .select('code , discount_amount, discount_percentage,limit')
        .eq('code', couponCode)
        .gte('valid_to', new Date().toISOString())
        .lte('valid_from', new Date().toISOString())
        .single()

      // console.log(error)
      console.log('coupons', coupon)

      if (error || !coupon) throw new Error('Invalid or expired coupon code.')

      if (coupon.limit !== null && coupon.limit <= 0) {
        throw new Error('Coupon limit exceeded.')
      }

      // console.log('Discount', discount)
      if (coupon.discount_percentage) {
        setDiscountPercentage(coupon.discount_percentage)
        subtotal -= (subtotal * coupon.discount_percentage) / 100
      } else if (coupon.discount_amount) {
        subtotal -= coupon.discount_amount
      }

      setAppliedCoupon(coupon.code)
      toast.success('Coupon applied successfully!')
    } catch (error) {
      console.log(error)
      toast.error(error.message ?? 'Error applying coupon.')
    }
  }

  console.log(total)

  const onSubmit = async (data: CheckoutFormValues) => {
    setLoading(true)
    try {
      if (!plan) {
        toast.error('No valid subscription plan selected.')
        return
      }

      const response = await fetch('/api/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription_plan_id: plan.id,
          currency: currency,
          coupon_code: couponCode,
          user_id: user?.id, // Replace with actual user ID
          payment_option: data.paymentMethod,
          total_amount: total,
          ppp,
        }),
      })

      if (!response.ok) {
        toast.error('Something Went Wrong !')
        throw new Error('Failed to create PhonePe order')
      }

      const { phonepe_transaction_id, redirectUrl, message } =
        await response.json()

      if (redirectUrl) {
        router.push(redirectUrl)
      } else {
        toast.success(message)
        router.replace('/components')
      }
    } catch (error) {
      console.error(error)
      toast.error('Checkout Failed', {
        description: 'There was a problem with your checkout.',
      })
    } finally {
      setLoading(false)
    }
  }

  const isSubmitting = form.formState.isSubmitting
  const isFree = Math.floor(total) === 0

  const getButtonLabel = () => {
    if (isSubmitting) return 'Processing...'
    if (isFree) return 'Complete Purchase'

    return paymentMethod === 'paypal' ? 'Pay with PayPal' : 'Pay with PhonePe'
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className=" max-h-screen w-full overflow-auto font-heading text-white md:max-w-[1000px]">
        <DialogHeader className="relative mx-auto w-max">
          <Heading className="text-3xl font-bold md:text-4xl lg:text-5xl">
            Checkout
          </Heading>
          {/* <DialogTitle className="bg-gradient-to-tr from-zinc-400/50 via-white  to-white/60 bg-clip-text font-heading text-xl font-semibold md:text-4xl 2xl:text-5xl ">
            Checkout
          </DialogTitle> */}
          <Image
            src={
              // 'https://utfs.io/f/qPlpyBmwd8UN5MteVCsb4Ome6ESHCaIB1fPvzJGUgtDL9K8N'
              'https://utfs.io/f/qPlpyBmwd8UNWkdj7RMXnlKSkcJz4joqaV9QTCDiPdfxmA6I'
            }
            width={200}
            height={100}
            alt="Checkout"
            className="absolute bottom-0 right-0"
          />
        </DialogHeader>
        <div className="grid gap-6 rounded-lg bg-card p-3 md:grid-cols-2 md:p-6">
          {/* Left Column - User Information */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium">Your Information</h3>
              <p className="text-sm text-gray-400">
                Please provide your details for the order.
              </p>
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <InputForm
                  label="Full Name"
                  name="name"
                  placeholder="John Doe"
                  description=""
                  required
                />

                <InputForm
                  label="Email"
                  name="email"
                  placeholder="<EMAIL>"
                  description=""
                  required
                />

                <FormField
                  control={form.control}
                  name="country"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Country</FormLabel>
                      <FormControl>
                        <CountrySelector
                          value={field.value || ''} // Ensure value is never undefined
                          onValueChange={(country) => {
                            if (country && typeof country === 'object') {
                              console.log('country selected')
                              form.setValue(
                                'countryCode',
                                country.countryCallingCodes[0]
                              )
                              field.onChange(country.alpha3)
                            }
                            // field.onChange(country);
                            console.log(country)
                          }}
                          placeholder="Select your country"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <div className="flex">
                          <div className="flex items-center rounded-l-sm border border-r-0 border-input bg-muted px-4  py-2 text-sm text-muted-foreground">
                            {form.watch('countryCode') || '+1'}
                          </div>
                          <Input
                            {...field}
                            className="rounded-l-none"
                            placeholder="1234567890"
                            type="tel"
                            onChange={(e) => {
                              // Only allow numbers
                              const value = e.target.value.replace(
                                /[^0-9]/g,
                                ''
                              )
                              field.onChange(value)
                            }}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Payment Method</h3>
                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="grid grid-cols-2 gap-4"
                          >
                            <div>
                              <RadioGroupItem
                                value="paypal"
                                id="paypal"
                                className="peer sr-only"
                              />
                              <label
                                htmlFor="paypal"
                                // className="bg-backgroud bg-pr flex flex-col items-center justify-between rounded-md border border-input px-3 py-5 hover:bg-secondary peer-data-[state=checked]:border-blue-600 peer-data-[state=checked]:bg-gradient-to-r from-[#003087] via-[#0070BA] to-[#009CDE]"
                                className="flex flex-col items-center justify-between rounded-md border-4 border-input  bg-gradient-to-r from-[#003087] via-[#0070BA] to-[#009CDE] px-3 py-5 font-bold text-white peer-data-[state=checked]:animate-pulse peer-data-[state=checked]:border-yellow-500   "
                              >
                                <BadgeDollarSign className="mb-3 h-6 w-6" />
                                PayPal
                              </label>
                            </div>
                            <div>
                              <RadioGroupItem
                                value="phonepe"
                                id="phonepe"
                                className="peer sr-only"
                              />
                              <label
                                htmlFor="phonepe"
                                // className="flex flex-col items-center justify-between rounded-md  border border-input  bg-background px-3 py-5  hover:bg-secondary peer-data-[state=checked]:border-purple-600"
                                className="flex flex-col items-center justify-between rounded-md border-4 border-input bg-gradient-to-r from-[#5C00D3] via-[#9C27B0] to-[#D500F9] px-3 py-5 font-bold text-white hover:bg-purple-700  peer-data-[state=checked]:animate-pulse peer-data-[state=checked]:border-yellow-500  "
                              >
                                <BadgeIndianRupee className="mb-3 h-6 w-6" />
                                PhonePe
                              </label>
                            </div>
                          </RadioGroup>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </form>
            </Form>

            {/* <p className="text-center text-sm text-gray-400">
              By completing this purchase you agree to our{' '}
              <a href="#" className="text-white underline hover:text-gray-300">
                terms and conditions
              </a>
            </p> */}
          </div>

          {/* Right Column - Order Summary */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium">Order Summary</h3>
              <p className="text-sm text-gray-400">Review your order details</p>
            </div>

            <div className="rounded-lg  border border-input  bg-background p-4">
              <div className="flex items-center gap-4">
                <div className="relative  rounded-lg bg-card">
                  <Image
                    src={plan?.plan_image ?? ''}
                    alt={plan?.name}
                    width={100}
                    height={100}
                    className="h-16 w-16 rounded-lg"
                  />
                </div>
                <div className="flex-1 space-y-1">
                  <h4 className="font-medium"> {plan?.name} </h4>
                  <p className="text-sm text-gray-400"> {plan?.description} </p>
                </div>
                <div className="font-medium">
                  {moneyFormatter(subtotal, currency)}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-400">Subtotal</span>
                <span>{moneyFormatter(subtotal, currency)}</span>
              </div>
              {discount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-400">Discount</span>
                  <span className="text-green-500">
                    -{moneyFormatter(discount, currency)}
                  </span>
                </div>
              )}
              {tax > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-400">
                    Tax
                    <Badge variant={'secondary'} className="ml-2">
                      {taxPercentage}%{' '}
                    </Badge>
                  </span>
                  <span>
                    {moneyFormatter(tax, currency)}
                    {/* <Badge>{taxPercentage} </Badge> */}
                  </span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between font-medium">
                <span>Total</span>
                <span>
                  {moneyFormatter(total, currency)}
                  {/* {moneyFormatter(total, currency)} */}
                </span>
              </div>
            </div>

            <div className="w-full ">
              <div className="flex flex-wrap items-center justify-between gap-2">
                <Button
                  variant={'link'}
                  className="p-0 py-0 text-sm !text-green-500 hover:text-green-400"
                  onClick={() => setShowCouponInput(true)}
                >
                  Apply Coupon
                </Button>
                {showCouponInput && (
                  <Button
                    onClick={() => setShowCouponInput(false)}
                    variant={'link'}
                    className="text-muted-foreground"
                  >
                    <X className="w-4" />
                  </Button>
                )}
              </div>

              {showCouponInput && (
                <div className="flex gap-2">
                  <Input
                    value={couponCode}
                    // onChange={(e) => setCouponCode(e.target.value)}
                    onChange={(e) => {
                      //set coupn code in upper case with error check
                      const value = e.target.value
                      //check string
                      // if (value.match(/^[a-zA-Z0-9]*$/)) {
                      //   setCouponCode(value.toUpperCase())
                      // }

                      // allow dash
                      if (value.match(/^[a-zA-Z0-9-]*$/)) {
                        setCouponCode(value.toUpperCase())
                      }
                    }}
                    placeholder="Enter coupon code"
                  />
                  <Button
                    onClick={handleApplyCoupon}
                    variant="secondary"
                    // className="border-background hover:bg-background"
                  >
                    Apply
                  </Button>
                </div>
              )}
            </div>

            {/* {showCouponInput ? (
              <div className="flex gap-2">
                <Input
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  placeholder="Enter coupon code"
                />
                <Button
                  onClick={handleApplyCoupon}
                  // variant="outline"
                  // className="border-background hover:bg-background"
                >
                  Apply
                </Button>
              </div>
            ) : (
              <Button
                variant={'ghost'}
                className="text-sm text-blue-500 hover:text-blue-400"
                onClick={() => setShowCouponInput(true)}
              >
                Apply Coupon
              </Button>
            )} */}

            <Button
              className="w-full bg-purple-600 hover:bg-purple-700"
              size="lg"
              onClick={form.handleSubmit(onSubmit)}
              disabled={loading}
            >
              {getButtonLabel()}
            </Button>

            <p className="text-center text-sm text-gray-400">
              By completing this purchase you agree to our{' '}
              <a href="#" className="text-white underline hover:text-gray-300">
                terms and conditions
              </a>
            </p>
          </div>
        </div>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="flex items-center justify-center gap-1 bg-background px-2 text-green-400 ">
              <CheckCircleIcon className=" h-5 w-5 " />
              Secure Checkout
            </span>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
