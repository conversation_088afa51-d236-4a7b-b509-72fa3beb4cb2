import React, { ReactNode } from 'react'

import { cn } from '@/utils'

interface HeadingProps {
  children: ReactNode
  className?: string
}

const Heading: React.FC<HeadingProps> = ({ children, className }) => {
  return (
    <h2
      className={cn(
        'bg-gradient-to-tr from-zinc-400/50 via-white to-white/60 bg-clip-text  pb-5  text-center font-heading text-4xl font-medium  text-transparent md:text-5xl lg:text-7xl ',
        className
      )}
    >
      {children}
    </h2>
  )
}

export default Heading
