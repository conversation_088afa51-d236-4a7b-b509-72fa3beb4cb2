// import { Link } from 'react-router-dom'

// import { But<PERSON>, buttonVariants } from './custom/button'

import { ChevronDownIcon } from 'lucide-react'
import Link from 'next/link'

import SidebarSearch from '@/components/component-sidebar/sidebar-search'
import { Badge } from '@/components/ui/badge'
import { Button, buttonVariants } from '@/components/ui/button'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import useCheckActiveNav from '@/hooks/use-check-active-nav'
import { cn } from '@/lib/utils'
import { useProfileStore } from '@/stores/profile-store'
import { SideLink } from '@/types'

import { Icons } from '../icons'
import Skeleton from '../ui/skeleton'

interface NavProps extends React.HTMLAttributes<HTMLDivElement> {
  isCollapsed: boolean
  isLoading?: boolean
  links: SideLink[] | null | any
  closeNav: () => void
  onSearch?: (query: string) => void
}

export default function Nav({
  links,
  isCollapsed,
  isLoading = true,
  className,
  closeNav,
  onSearch,
}: NavProps) {
  const { profile } = useProfileStore()

  const renderLink = ({ sub, isAdmin, ...rest }: SideLink) => {
    const key = `${rest.title}-${rest.href}`

    // dont't show admin links if not admin
    if (isAdmin && profile?.role !== 'admin') return null

    if (!links && isLoading) {
      // items?.length === 0 &&
      return <Skeleton key={'22'} className="h-7 w-full bg-accent md:h-10" />
    }

    if (isCollapsed && sub)
      return (
        <NavLinkIconDropdown
          {...rest}
          sub={sub}
          key={key}
          closeNav={closeNav}
        />
      )

    if (isCollapsed)
      return <NavLinkIcon {...rest} key={key} closeNav={closeNav} />

    if (sub)
      return (
        <NavLinkDropdown {...rest} sub={sub} key={key} closeNav={closeNav} />
      )

    return <NavLink {...rest} key={key} closeNav={closeNav} />
  }
  return (
    <div
      data-collapsed={isCollapsed}
      className={cn(
        'group border-b bg-background transition-[max-height,padding] duration-500 data-[collapsed=true]:py-2 md:border-none',
        className
      )}
    >
      {onSearch && (
        <div className="mb-2  px-2 py-2">
          <SidebarSearch onSearch={onSearch} />
        </div>
      )}
      <TooltipProvider delayDuration={0}>
        <nav className="grid gap-1 pb-8 group-[[data-collapsed=true]]:justify-center group-[[data-collapsed=true]]:px-2">
          {!links &&
            isLoading &&
            new Array(15)
              .fill('')
              .map((item, index) => (
                <Skeleton
                  key={index}
                  className="h-12 w-full rounded-none bg-accent"
                />
              ))}
          {links?.map(renderLink)}
        </nav>
      </TooltipProvider>
    </div>
  )
}

interface NavLinkProps extends SideLink {
  subLink?: boolean
  closeNav: () => void
  labelVariant?: 'new' | 'hot' | 'beta' | 'pro' | 'default'
}

function NavLink({
  title,
  icon,
  label,
  labelVariant = 'default',
  href,
  closeNav,
  subLink = false,
}: NavLinkProps) {
  const { checkActiveNav } = useCheckActiveNav()
  const Icon = Icons[icon || 'component']

  return (
    <Link
      prefetch={true}
      href={href}
      onClick={closeNav}
      className={cn(
        buttonVariants({
          variant: checkActiveNav(href) ? 'gradient-primary' : 'ghost',
        }),
        'text-wrap h-12 justify-start rounded-none px-6 font-heading text-xs tracking-normal hover:scale-100 hover:bg-primary/20 md:text-sm',
        subLink && 'h-10 w-full border-l border-l-slate-500 px-2'
      )}
      aria-current={checkActiveNav(href) ? 'page' : undefined}
    >
      <div className="mr-2">
        <Icon size={18} />
      </div>

      <span className="mr-2">{title}</span>
      {label && (
        <Badge
          variant={labelVariant}
          className="ml-2 rounded-3xl px-3 py-0.5 text-[10px]"
        >
          {label}
        </Badge>
      )}
    </Link>
  )
}

function NavLinkDropdown({ title, icon, label, sub, closeNav }: NavLinkProps) {
  const { checkActiveNav } = useCheckActiveNav()

  /* Open collapsible by default
   * if one of child element is active */
  const isChildActive = !!sub?.find((s) => checkActiveNav(s.href))

  return (
    <Collapsible defaultOpen={isChildActive}>
      <CollapsibleTrigger
        className={cn(
          buttonVariants({ variant: 'ghost', size: 'sm' }),
          'group h-12 w-full justify-start rounded-none px-6'
        )}
      >
        {/* <div className="mr-2">{icon}</div> */}

        {title}
        {label && (
          <div className="ml-2 rounded-lg bg-primary px-1 text-[0.625rem] text-primary-foreground">
            {label}
          </div>
        )}
        <span
          className={cn(
            'ml-auto transition-all group-data-[state="open"]:-rotate-180'
          )}
        >
          <ChevronDownIcon stroke={'1'} className="h-full w-full" />
        </span>
      </CollapsibleTrigger>
      <CollapsibleContent className="collapsibleDropdown" asChild>
        <ul>
          {sub!.map((sublink) => (
            <li key={sublink.title} className="my-1 ml-8">
              <NavLink {...sublink} subLink closeNav={closeNav} />
            </li>
          ))}
        </ul>
      </CollapsibleContent>
    </Collapsible>
  )
}

function NavLinkIcon({
  title,
  icon,
  label,
  labelVariant = 'default',
  href,
}: NavLinkProps) {
  const { checkActiveNav } = useCheckActiveNav()
  const Icon = Icons[icon || 'component']

  return (
    <Tooltip delayDuration={0}>
      <TooltipTrigger asChild>
        <Link
          prefetch={true}
          href={href}
          className={cn(
            buttonVariants({
              variant: checkActiveNav(href) ? 'secondary' : 'ghost',
              size: 'icon',
            }),
            'h-12 w-12'
          )}
        >
          <Icon size={18} />
          <span className="sr-only">{title}</span>
        </Link>
      </TooltipTrigger>
      <TooltipContent side="right" className="flex items-center gap-4">
        {title}
        {label && (
          <Badge variant={labelVariant} className="text-[10px]">
            {label}
          </Badge>
        )}
      </TooltipContent>
    </Tooltip>
  )
}

function NavLinkIconDropdown({ title, icon, label, sub }: NavLinkProps) {
  const { checkActiveNav } = useCheckActiveNav()

  /* Open collapsible by default
   * if one of child element is active */
  const isChildActive = !!sub?.find((s) => checkActiveNav(s.href))
  const Icon = Icons[icon || 'component']

  return (
    <DropdownMenu>
      <Tooltip delayDuration={0}>
        <TooltipTrigger asChild>
          <DropdownMenuTrigger asChild>
            <Button
              variant={isChildActive ? 'secondary' : 'ghost'}
              size="icon"
              className="h-12 w-12"
            >
              {/* {icon} */}
              <Icon size={18} />
            </Button>
          </DropdownMenuTrigger>
        </TooltipTrigger>
        <TooltipContent side="right" className="flex items-center gap-4">
          {title}{' '}
          {label && (
            <span className="ml-auto text-muted-foreground">{label}</span>
          )}
          <ChevronDownIcon
            size={18}
            className="-rotate-90 text-muted-foreground"
          />
        </TooltipContent>
      </Tooltip>
      <DropdownMenuContent side="right" align="start" sideOffset={4}>
        <DropdownMenuLabel>
          {title} {label ? `(${label})` : ''}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {sub!.map(({ title, icon, label, href }) => {
          const Icon = Icons[icon || 'component']
          return (
            <DropdownMenuItem key={`${title}-${href}`} asChild>
              <Link
                prefetch={true}
                href={href}
                className={`${checkActiveNav(href) ? 'bg-secondary' : ''}`}
              >
                <Icon size={18} />
                <span className="max-w-52 text-wrap ml-2">{title}</span>
                {label && <span className="ml-auto text-xs">{label}</span>}
              </Link>
            </DropdownMenuItem>
          )
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
