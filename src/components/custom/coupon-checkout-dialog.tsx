'use client'

import { useMutation } from '@tanstack/react-query'
import { CheckCircle, Gift, Loader2, X } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useProfileStore } from '@/stores/profile-store'
import { createClient } from '@/utils/supabase/client'

interface CouponCheckoutDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

interface CouponValidationResponse {
  valid: boolean
  coupon?: {
    id: string
    code: string
    description: string
    discount_percentage: number
    discount_amount: number
  }
  error?: string
}

interface CouponCheckoutResponse {
  success: boolean
  transaction_id?: string
  message?: string
  error?: string
}

export function CouponCheckoutDialog({
  open,
  onOpenChange,
}: CouponCheckoutDialogProps) {
  const [couponCode, setCouponCode] = useState('')
  const [validatedCoupon, setValidatedCoupon] =
    useState<CouponValidationResponse | null>(null)
  const [isValidating, setIsValidating] = useState(false)
  const { user, fetchUser } = useProfileStore()
  const supabase = createClient()

  // Reset state when dialog closes
  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setCouponCode('')
      setValidatedCoupon(null)
      setIsValidating(false)
    }
    onOpenChange(newOpen)
  }

  // Validate coupon code
  const validateCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error('Please enter a coupon code')
      return
    }

    setIsValidating(true)
    try {
      const { data: coupon, error } = await supabase
        .from('coupons')
        .select(
          'id, code, description, discount_percentage, discount_amount, limit, valid_from, valid_to'
        )
        .eq('code', couponCode.toUpperCase())
        .gte('valid_to', new Date().toISOString())
        .lte('valid_from', new Date().toISOString())
        .single()

      if (error || !coupon) {
        setValidatedCoupon({
          valid: false,
          error: 'Invalid or expired coupon code',
        })
        toast.error('Invalid or expired coupon code')
        return
      }

      if (coupon.limit !== null && coupon.limit <= 0) {
        setValidatedCoupon({ valid: false, error: 'Coupon limit exceeded' })
        toast.error('Coupon limit exceeded')
        return
      }

      // Check if it's a 100% off coupon (lifetime deal requirement)
      const isLifetimeDeal = coupon.discount_percentage === 100
      if (!isLifetimeDeal) {
        setValidatedCoupon({
          valid: false,
          error: 'This coupon is not valid for lifetime deals',
        })
        toast.error('This coupon is not valid for lifetime deals')
        return
      }

      setValidatedCoupon({ valid: true, coupon })
      toast.success('Coupon validated successfully!')
    } catch (error) {
      console.error('Coupon validation error:', error)
      setValidatedCoupon({ valid: false, error: 'Failed to validate coupon' })
      toast.error('Failed to validate coupon')
    } finally {
      setIsValidating(false)
    }
  }

  // Process coupon checkout
  const checkoutMutation = useMutation({
    mutationFn: async () => {
      if (!validatedCoupon?.valid || !validatedCoupon.coupon || !user) {
        throw new Error('Invalid state for checkout')
      }

      const response = await fetch('/api/coupon-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          coupon_code: validatedCoupon.coupon.code,
          coupon_id: validatedCoupon.coupon.id,
          subscription_plan_id: 'dfcdbb27-1bfc-4739-97bf-e7fb7df65ee3',
          user_id: user.id,
        }),
      })

      const data: CouponCheckoutResponse = await response.json()

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Checkout failed')
      }

      return data
    },
    onSuccess: async () => {
      toast.success('Congratulations! You now have lifetime pro access!')

      // Refresh user profile to reflect the changes
      await fetchUser()

      // Close dialog
      handleOpenChange(false)
    },
    onError: (error) => {
      console.error('Checkout error:', error)
      toast.error(error.message || 'Checkout failed. Please try again.')
    },
  })

  const handleCheckout = () => {
    if (!user) {
      toast.error('Please log in to continue')
      return
    }

    if (!validatedCoupon?.valid) {
      toast.error('Please validate your coupon first')
      return
    }

    checkoutMutation.mutate()
  }

  const originalPrice = 299 // Lifetime deal original price
  const finalPrice = validatedCoupon?.valid ? 0 : originalPrice

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2">
            <Gift className="h-5 w-5 text-purple-600" />
            <DialogTitle>Lifetime Deal Checkout</DialogTitle>
          </div>
          <DialogDescription>
            Enter your 100% off coupon code to activate your lifetime pro
            access.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Coupon Input Section */}
          <div className="space-y-2">
            <Label htmlFor="coupon-code">Coupon Code</Label>
            <div className="flex gap-2">
              <Input
                id="coupon-code"
                value={couponCode}
                onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                placeholder="Enter coupon code"
                disabled={isValidating || checkoutMutation.isPending}
                className="flex-1"
              />
              <Button
                onClick={validateCoupon}
                disabled={
                  !couponCode.trim() ||
                  isValidating ||
                  checkoutMutation.isPending
                }
                variant="outline"
                size="default"
              >
                {isValidating ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  'Validate'
                )}
              </Button>
            </div>
          </div>

          {/* Validation Feedback */}
          {validatedCoupon && (
            <div
              className={`flex items-center gap-2 rounded-md p-3 ${
                validatedCoupon.valid
                  ? 'border border-green-200 bg-green-50 text-green-700'
                  : 'border border-red-200 bg-red-50 text-red-700'
              }`}
            >
              {validatedCoupon.valid ? (
                <>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">
                    Coupon validated! {validatedCoupon.coupon?.description}
                  </span>
                </>
              ) : (
                <>
                  <X className="h-4 w-4" />
                  <span className="text-sm">{validatedCoupon.error}</span>
                </>
              )}
            </div>
          )}

          {/* Price Display */}
          <div className="space-y-2 rounded-lg bg-card p-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                Lifetime Pro Access
              </span>
              <span
                className={`text-sm ${
                  finalPrice === 0 ? 'text-gray-400 line-through' : ''
                }`}
              >
                ${originalPrice}
              </span>
            </div>

            {validatedCoupon?.valid && (
              <div className="flex items-center justify-between">
                <span className="text-sm text-green-600">
                  Coupon Discount (100% off)
                </span>
                <span className="text-sm text-green-600">
                  -${originalPrice}
                </span>
              </div>
            )}

            <div className="flex items-center justify-between border-t pt-2 font-semibold">
              <span>Total</span>
              <div className="flex items-center gap-2">
                <span className="text-lg">${finalPrice}</span>
                {finalPrice === 0 && (
                  <Badge
                    variant="secondary"
                    className="bg-green-100 text-green-700"
                  >
                    FREE
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Checkout Button */}
          <Button
            onClick={handleCheckout}
            disabled={!validatedCoupon?.valid || checkoutMutation.isPending}
            className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            size="lg"
          >
            {checkoutMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Activate Lifetime Pro Access'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
