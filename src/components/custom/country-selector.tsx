import { countries } from 'country-data-list'
import { ChevronDown, ChevronsUpDown, CheckIcon, Globe } from 'lucide-react'
import React, { useCallback, useState, forwardRef, useMemo } from 'react'
import { CircleFlag } from 'react-circle-flags'

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { cn } from '@/lib/utils'

export type Country = {
  alpha2: string
  alpha3: string
  countryCallingCodes: string[]
  currencies: string[]
  emoji?: string
  ioc: string
  languages: string[]
  name: string
  status: string
}

type BaseCountrySelectorProps = {
  options?: Country[]
  disabled?: boolean
  placeholder?: string
  slim?: boolean
  inline?: boolean
  className?: string
}

type SingleCountrySelectorProps = BaseCountrySelectorProps & {
  multiple?: false
  onValueChange?: (country: Country) => void
  value?: string
}

type MultipleCountrySelectorProps = BaseCountrySelectorProps & {
  multiple: true
  onValueChange: (countries: Country[]) => void
  value?: string[]
}

type CountrySelectorProps =
  | SingleCountrySelectorProps
  | MultipleCountrySelectorProps

const CountrySelectorComponent = (
  {
    options = countries.all.filter(
      (country: Country) =>
        country.emoji && country.status !== 'deleted' && country.ioc !== 'PRK'
    ),
    onValueChange,
    value,
    disabled = false,
    placeholder = 'Select a country',
    slim = false,
    inline = false,
    multiple = false,
    className,
    ...props
  }: CountrySelectorProps,
  ref: React.ForwardedRef<HTMLButtonElement>
) => {
  const [open, setOpen] = useState(false)

  // Convert value to selected countries without useEffect
  const selectedCountries = useMemo(() => {
    if (!value) return []

    if (multiple && Array.isArray(value)) {
      return options.filter((country) => value.includes(country.alpha3))
    }

    if (!multiple && typeof value === 'string') {
      const found = options.find((country) => country.alpha3 === value)
      return found ? [found] : []
    }

    return []
  }, [value, options, multiple])

  const handleSelect = useCallback(
    (country: Country) => {
      if (multiple) {
        const isSelected = selectedCountries.some(
          (c) => c.alpha3 === country.alpha3
        )
        const newSelection = isSelected
          ? selectedCountries.filter((c) => c.alpha3 !== country.alpha3)
          : [...selectedCountries, country]

        ;(onValueChange as MultipleCountrySelectorProps['onValueChange'])?.(
          newSelection
        )
      } else {
        ;(onValueChange as SingleCountrySelectorProps['onValueChange'])?.(
          country
        )
        setOpen(false)
      }
    },
    [multiple, onValueChange, selectedCountries]
  )

  const triggerClasses = cn(
    'flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 hover:bg-secondary/80',
    slim === true && 'gap-1 w-min',
    inline && 'rounded-r-none border-r-0 gap-1 pr-1 w-min',
    className
  )

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger
        ref={ref}
        className={triggerClasses}
        disabled={disabled}
        {...props}
      >
        {selectedCountries.length > 0 ? (
          <div className="flex flex-grow items-center gap-2 overflow-hidden">
            {multiple ? (
              <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                {selectedCountries.length} selected
              </span>
            ) : (
              <>
                <div className="inline-flex h-4 w-4 shrink-0 items-center justify-center overflow-hidden rounded-full">
                  <CircleFlag
                    countryCode={selectedCountries[0].alpha2.toLowerCase()}
                    height={16}
                  />
                </div>
                {slim === false && !inline && (
                  <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                    {selectedCountries[0].name}
                  </span>
                )}
              </>
            )}
          </div>
        ) : (
          <span className="flex items-center gap-2">
            {inline || slim ? <Globe size={16} /> : placeholder}
          </span>
        )}
        {!inline ? (
          <ChevronDown size={16} />
        ) : (
          <ChevronsUpDown size={16} className="text-muted-foreground" />
        )}
      </PopoverTrigger>
      <PopoverContent
        align="start"
        side="bottom"
        className="w-[var(--radix-popover-trigger-width)] p-0"
      >
        <Command className="w-full">
          <CommandInput placeholder="Search country..." />
          <div
            className="max-h-[200px] overflow-y-auto"
            onWheel={(e) => {
              e.stopPropagation()
            }}
          >
            <CommandList>
              <CommandEmpty>No country found.</CommandEmpty>
              <CommandGroup>
                {options
                  .filter((x) => x.name)
                  .map((option) => (
                    <CommandItem
                      key={`country-${option.alpha3}`}
                      className="flex w-full items-center gap-2"
                      onSelect={() => handleSelect(option)}
                    >
                      <div className="flex flex-grow space-x-2 overflow-hidden">
                        <div className="inline-flex h-5 w-5 shrink-0 items-center justify-center overflow-hidden rounded-full">
                          <CircleFlag
                            countryCode={option.alpha2.toLowerCase()}
                            height={20}
                          />
                        </div>
                        <span className="overflow-hidden text-ellipsis whitespace-nowrap">
                          {option.name}
                        </span>
                      </div>
                      <CheckIcon
                        className={cn(
                          'ml-auto h-4 w-4 shrink-0',
                          selectedCountries.some(
                            (c) => c.alpha3 === option.alpha3
                          )
                            ? 'opacity-100'
                            : 'opacity-0'
                        )}
                      />
                    </CommandItem>
                  ))}
              </CommandGroup>
            </CommandList>
          </div>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

CountrySelectorComponent.displayName = 'CountrySelectorComponent'

export const CountrySelector = forwardRef(CountrySelectorComponent)
