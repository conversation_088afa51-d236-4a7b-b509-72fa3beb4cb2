'use client'

import Image from 'next/image'
import { useEffect, useState } from 'react'

import { Button } from '../ui/button'

export function SpecialNotification() {
  const [isOpen, setIsOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    const hasSeenNotification = localStorage.getItem(
      'hasSeenSpecialNotification'
    )

    if (!hasSeenNotification) {
      const timer = setTimeout(() => {
        setIsOpen(true)
      }, 5000) // Shows after 5 seconds

      return () => clearTimeout(timer)
    }
  }, [])

  const handleClose = () => {
    setIsOpen(false)
    localStorage.setItem('hasSeenSpecialNotification', 'true')
  }

  if (!isMounted || !isOpen) return null

  return (
    <div className="fixed inset-0 z-[100] flex items-center justify-center bg-black/70 backdrop-blur-sm">
      <div className="relative mx-4 w-[95%] max-w-[950px] overflow-hidden rounded-lg bg-background">
        <div className="absolute bottom-2 right-2 z-50">
          <Button
            onClick={handleClose}
            variant={'destructive'}
            // className="rounded-full p-1.5 hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <span className="">Close</span>
          </Button>
        </div>
        <div className="relative aspect-video w-full p-4">
          <Image
            src="https://gallery.theportfolio.in/company/1745688530017-special-note.webp"
            alt="Special Note"
            fill
            className="object-contain"
            priority
          />
        </div>
      </div>
    </div>
  )
}
