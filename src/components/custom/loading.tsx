import { Loader2 } from 'lucide-react'

interface LoadingProps {
  text?: string
}

const Loading = ({ text = 'Loading...' }: LoadingProps) => {
  return (
    <div className="flex min-h-[400px] w-full flex-col items-center justify-center space-y-4">
      <Loader2 className="h-10 w-10 animate-spin text-muted-foreground" />
      <p className="text-lg text-muted-foreground">{text}</p>
    </div>
  )
}

export default Loading
