import { AlertCircle } from 'lucide-react'

import { Button } from '@/components/ui/button'

interface ErrorProps {
  text?: string
  retry?: () => void
}

const Error = ({ text = 'Something went wrong!', retry }: ErrorProps) => {
  return (
    <div className="flex min-h-[400px] w-full flex-col items-center justify-center space-y-4">
      <div className="rounded-full bg-destructive/10 p-3">
        <AlertCircle className="h-10 w-10 text-destructive" />
      </div>
      <div className="text-center">
        <p className="text-lg font-medium text-destructive">{text}</p>
        {retry && (
          <Button onClick={retry} variant="outline" className="mt-4">
            Try Again
          </Button>
        )}
      </div>
    </div>
  )
}

export default Error
