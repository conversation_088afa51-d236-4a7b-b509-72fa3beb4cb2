'use client'
import { motion } from 'framer-motion'
import { useEffect, useMemo, useState } from 'react'

import { MainCard } from '@/components/cards/main-card'
import { useWindowSize } from '@/hooks/use-window-size'
import { ComponentWithCategory } from '@/types/custom'

import LoadingGridForComponent from '../loaders/loading-grid1'
import LoadingGridForTemplate from '../loaders/loading-grid2'

function ComponentGridCustom({
  components,
  isTemplate = false,
}: {
  components: ComponentWithCategory[]
  isTemplate?: boolean
}) {
  const { width } = useWindowSize()
  const [columns, setColumns] = useState<ComponentWithCategory[][]>([])

  useEffect(() => {
    const getColumnCount = () => {
      if (width < 640) return 1
      if (width < 1024) return 2
      if (width < 1480) return 3
      return 4
    }

    const columnCount = getColumnCount()
    const cols: ComponentWithCategory[][] = Array.from(
      { length: columnCount },
      () => []
    )

    components?.forEach((item, index) => {
      cols[index % columnCount].push(item)
    })

    setColumns(cols)
  }, [components, width])

  const renderColumns = useMemo(() => {
    return columns.map((column, columnIndex) => (
      <div key={columnIndex} className="flex flex-col gap-4 lg:gap-5">
        {column.map((item) => (
          <motion.div
            key={item.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: columnIndex * 0.1 }}
          >
            <MainCard component={item} isTemplate={isTemplate} />
          </motion.div>
        ))}
      </div>
    ))
  }, [columns, isTemplate])

  if (!columns.length) {
    return isTemplate ? <LoadingGridForTemplate /> : <LoadingGridForComponent />
  }

  return (
    <div className="mx-auto w-full px-0">
      <motion.div
        className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 lg:gap-5 2xl:grid-cols-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {renderColumns}
      </motion.div>
    </div>
  )
}

export default ComponentGridCustom
