'use client'
import { Gift, Sparkles } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useProfileStore } from '@/stores/profile-store'

import { CouponCheckoutDialog } from './coupon-checkout-dialog'

export function LifetimeDealSection() {
  const { user } = useProfileStore()
  const router = useRouter()
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const handleCheckoutClick = () => {
    if (!user) {
      router.push('/login')
      return
    } else {
      // Open the coupon checkout dialog instead of redirecting to pricing
      setIsDialogOpen(true)
    }
  }
  return (
    <div className="mx-auto mb-8 max-w-5xl px-4 sm:px-6 lg:px-8">
      <div className="pt8 relative overflow-hidden rounded-2xl border border-pink-600 bg-gradient-to-r from-purple-50 via-pink-50 to-red-50 p-6 dark:from-purple-600/20 dark:via-pink-600/20 dark:to-red-600/20 sm:p-8 sm:pt-12">
        <div className="absolute left-0 right-0 top-0 z-10  flex justify-center">
          <Badge
            variant="secondary"
            className="rounded-b-md rounded-t-none bg-gradient-to-r from-purple-600 to-pink-600 px-3 py-1 text-xs font-bold tracking-wide text-white sm:px-4  sm:py-1.5"
          >
            LIFETIME DEAL
          </Badge>
        </div>

        <div className="flex flex-col items-center justify-between gap-6 md:flex-row">
          {/* Left side - Message */}
          <div className="flex flex-col items-center gap-4 text-center md:flex-row md:items-start md:text-left">
            <div className="relative">
              <Gift className="h-16 w-16 text-yellow-400 sm:h-24 sm:w-24" />
              <Sparkles className="absolute -right-2 -top-2 h-4 w-4 text-yellow-500 sm:h-5 sm:w-5" />
            </div>

            <div className="font-heading">
              <h3 className="mb-1 text-lg font-bold tracking-normal text-foreground sm:text-xl lg:text-3xl">
                Have a Lifetime Deal Coupon?
              </h3>
              <p className="text-sm tracking-normal text-purple-200 sm:text-base lg:text-lg">
                If you have a 100% off coupon code, then kindly use that coupon
                here.
              </p>
            </div>
          </div>

          {/* Right side - CTA */}
          <div className="flex-shrink-0">
            <Button
              size="lg"
              className="w-full rounded-full bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-3 text-base font-semibold text-white hover:from-purple-700 hover:to-pink-700 sm:px-10 sm:py-4 sm:text-lg md:w-auto md:px-12 md:py-6"
              onClick={handleCheckoutClick}
            >
              Use Coupon Here
            </Button>
          </div>
        </div>
      </div>

      {/* Coupon Checkout Dialog */}
      <CouponCheckoutDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
      />
    </div>
  )
}
