'use client'
import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'

import { inter } from '@/utils'

// NOTE: Change this date to whatever date you want to countdown to :)
// const COUNTDOWN_FROM = "4/26/2024";

const targetDate = new Date()
targetDate.setDate(targetDate.getDate() + 2) // Set target date to 2 days from today

const COUNTDOWN_FROM = targetDate.toDateString()

const SECOND = 1000
const MINUTE = SECOND * 60
const HOUR = MINUTE * 60
const DAY = HOUR * 24

const ShiftingCountdown = () => {
  const intervalRef = useRef<NodeJS.Timer | null>(null)

  const [remaining, setRemaining] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  })

  useEffect(() => {
    intervalRef.current = setInterval(handleCountdown, 1000)

    return () => clearInterval(intervalRef.current || undefined)
  }, [])

  const handleCountdown = () => {
    const end = new Date(COUNTDOWN_FROM)

    const now = new Date()

    const distance = +end - +now

    const days = Math.floor(distance / DAY)
    const hours = Math.floor((distance % DAY) / HOUR)
    const minutes = Math.floor((distance % HOUR) / MINUTE)
    const seconds = Math.floor((distance % MINUTE) / SECOND)

    setRemaining({
      days,
      hours,
      minutes,
      seconds,
    })
  }

  return (
    // <div className="p-4 bg-gradient-to-br from-violet-600 to-indigo-600">
    <div className="bg-slate-950 p-4">
      <div className="mx-auto flex w-full max-w-5xl items-center bg-white">
        <CountdownItem num={remaining.days} text="days" />
        <CountdownItem num={remaining.hours} text="hours" />
        <CountdownItem num={remaining.minutes} text="minutes" />
        <CountdownItem num={remaining.seconds} text="seconds" />
      </div>
    </div>
  )
}

interface CountdownItemProps {
  num: number
  text: string
}

const CountdownItem = ({ num, text }: CountdownItemProps) => {
  return (
    <div className="flex h-24 w-1/4 flex-col items-center justify-center gap-1 border-r-[1px] border-slate-200 font-sans md:h-36 md:gap-2">
      <div className="relative w-full overflow-hidden text-center">
        <AnimatePresence mode="popLayout">
          <motion.span
            key={num}
            initial={{ y: '100%' }}
            animate={{ y: '0%' }}
            exit={{ y: '-100%' }}
            transition={{ ease: 'backIn', duration: 0.75 }}
            className="block text-2xl font-semibold text-black md:text-4xl lg:text-6xl xl:text-7xl"
          >
            {num}
          </motion.span>
        </AnimatePresence>
      </div>
      <span className="text-xs font-light text-slate-500 md:text-sm lg:text-base">
        {text}
      </span>
    </div>
  )
}

export default ShiftingCountdown
