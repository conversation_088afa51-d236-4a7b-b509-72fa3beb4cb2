import { ChevronRight } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

import Heading from './heading'

interface ComponentCategory {
  title: string
  count: number
  icon: string
}

const categories: ComponentCategory[] = [
  {
    title: 'Navbars',
    count: 4,
    icon: 'https://utfs.io/f/LhUzHyAuqPAK0M3wstiKzV6IGM3DXLytajpvSrFWd2eTUsmQ',
  },
  {
    title: 'Footers',
    count: 4,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKxykNrE1PXGUOs3opCecDqjZnIQyYFm0xu9dB',
  },
  {
    title: 'Hero Header Sections',
    count: 47,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKDCnDzCVegq35PV8fOEsBpcXI4o6F2vRkiCzt',
  },
  {
    title: 'Header Sections',
    count: 27,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKF4ACmIKGfkYpgLK3AlQSyCoctw7ROiJT6VEP',
  },
  {
    title: 'Feature Sections',
    count: 347,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKa6gmXkGlGWQzHngMoTO2bRjyFN3DZIswdShP',
  },
  {
    title: 'CTA Sections',
    count: 36,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKqNaZ0XR0zpJEGSQkWo8u4MtTKXv2OLxY93Nh',
  },
  {
    title: 'Contact Sections',
    count: 30,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKs1zpASBeNTal7yqp4hwouLCGieMIXZ2WVk91',
  },
  {
    title: 'Pricing Sections',
    count: 27,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKYToTi2FEpJc0tyHszn8fRrX2SPBLaUg9ih3A',
  },
  {
    title: 'Testimonial Sections',
    count: 31,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKAyxdfksvDawbCr8SMu1E5BFjcQWhdH42pYUs',
  },
  {
    title: 'FAQ Sections',
    count: 14,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKEhn5miXu74OeUMLyrZl9EqwKjfTGJVhBRFAg',
  },
  {
    title: 'Logo Sections',
    count: 6,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKji3fpPy93MAFqOnXGCST08oyzdlQpJH6cWrf',
  },
  {
    title: 'Team Sections',
    count: 20,
    icon: 'https://utfs.io/f/LhUzHyAuqPAK0jjLA2iKzV6IGM3DXLytajpvSrFWd2eTUsmQ',
  },
  {
    title: 'Blog Header Sections',
    count: 32,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKdfzOKAZ6JQxaCZy75Igjb2iUEAOvS0P18fwX',
  },
  {
    title: 'Blog Sections',
    count: 36,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKiaX8i7rE9J6ZH4mMSt0hwEVivgxoBFpkTcL2',
  },
  {
    title: 'Blog Posts',
    count: 5,
    icon: 'https://utfs.io/f/LhUzHyAuqPAKEJIULyXu74OeUMLyrZl9EqwKjfTGJVhBRFAg',
  },
]

export default function ComponentsOverview() {
  const chunkCategories = (arr: typeof categories, size: number) => {
    const chunks = []
    for (let i = 0; i < arr.length; i += size) {
      chunks.push(arr.slice(i, i + size))
    }
    return chunks
  }

  return (
    <div className="border-y border-slate-900 bg-background px-4 py-24 md:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl text-center">
        <Heading>
          Components Overview
          {/* <span className="ml-2 inline-block">
            <svg
              viewBox="0 0 60 20"
              fill="none"
              className="h-4 w-16 text-primary"
            >
              <path
                d="M1 19C1 19 11.5 -3.5 30 4C48.5 11.5 59 1 59 1"
                stroke="currentColor"
                strokeWidth="2"
              />
            </svg>
          </span> */}
        </Heading>

        <p className="mx-auto mb-12 max-w-3xl text-lg text-muted-foreground">
          We&apos;ve thought of everything you need to start your next WordPress
          & Elementor project at 80% completion, 20% is your minor changes of
          content.
        </p>

        {/* <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {categories.map((category) => (
            <CategoryCard key={category.title} category={category} />
          ))}
        </div> */}

        {/* slicing individually */}
        {/* <div className="mb-12 flex flex-wrap justify-center gap-4">
          <div className="mb-4 flex w-full justify-center gap-4">
            {categories.slice(0, 3).map((category) => (
              <CategoryCard key={category.title} category={category} />
            ))}
          </div>
          <div className="mb-4 flex w-full justify-center gap-4">
            {categories.slice(2, 6).map((category) => (
              <CategoryCard key={category.title} category={category} />
            ))}
          </div>
          <div className="mb-4 flex w-full justify-center gap-4">
            {categories.slice(6, 10).map((category) => (
              <CategoryCard key={category.title} category={category} />
            ))}
          </div>
          <div className="mb-4 flex w-full justify-center  gap-4">
            {categories.slice(10, 13).map((category) => (
              <CategoryCard key={category.title} category={category} />
            ))}
          </div>
          <div className="flex w-full justify-center  gap-4">
            {categories.slice(13).map((category) => (
              <CategoryCard key={category.title} category={category} />
            ))}
          </div>
        </div> */}

        {/* Dynamically generate category rows */}
        <div className=" flex flex-wrap justify-center">
          {chunkCategories(categories, 4).map((chunk, index) => (
            <div
              key={index}
              className="mb-4 flex w-full flex-wrap justify-center gap-4"
            >
              {chunk.map((category) => (
                <Link prefetch={true} key={category.title} href="/components">
                  <CategoryCard key={category.title} category={category} />
                </Link>
              ))}
            </div>
          ))}
        </div>

        <Button asChild className="mt-12" size="lg">
          <Link prefetch={true} href={'/components'}>
            Browse all components <ChevronRight className="ml-1 h-4 w-4" />
          </Link>
        </Button>
      </div>
    </div>
  )
}

function CategoryCard({ category }: { category: ComponentCategory }) {
  return (
    <Card
      key={category.title}
      className="group w-full rounded-md bg-white p-3 transition-shadow hover:shadow-lg sm:w-auto"
    >
      <CardContent className="flex items-center space-x-4 p-0">
        <div className="flex h-12 w-12 items-center justify-center rounded-[8px] border">
          <Image
            src={category.icon}
            alt=""
            width={100}
            height={100}
            className="h-full w-full rounded-[8px] opacity-75"
          />
        </div>
        <div className="flex flex-1 items-center justify-between">
          <span className="font-medium dark:text-black">{category.title}</span>
          {/* <Badge variant="secondary" className="ml-2 rounded-lg bg-primary">
            {category.count}
          </Badge> */}
        </div>
      </CardContent>
    </Card>
  )
}

// function CategoryCard1({ category }: { category: ComponentCategory }) {
//   return (
//     <div className="m-2 flex items-center rounded-lg p-3 shadow-sm">
//       <div className="mr-3 h-6 w-6 flex-shrink-0">
//         <Image
//           src={category.icon}
//           alt=""
//           width={50}
//           height={50}
//           className="h-14 w-14 object-contain"
//         />
//       </div>
//       <span className="mr-2 text-sm font-medium text-gray-900">
//         {category.title}
//       </span>
//       <Badge variant="secondary" className="bg-gray-200 text-gray-700">
//         {category.count}
//       </Badge>
//     </div>
//   )
// }
