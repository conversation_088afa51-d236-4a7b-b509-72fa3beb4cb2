'use client'

import { Heart, Loader2 } from 'lucide-react'

import { useLike } from '@/hooks/use-like'
import { useThrottle } from '@/hooks/use-throttle'
import { cn } from '@/lib/utils'

interface LikeButtonProps {
  componentId: string
  initialLikes?: number
  showCount?: boolean
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'minimal' | 'card'
  className?: string
}

export function LikeButton({
  componentId,
  initialLikes = 0,
  showCount = true,
  size = 'md',
  variant = 'default',
  className,
}: LikeButtonProps) {
  const { isLiked, isLoading, toggleLike, isAuthenticated } = useLike({
    componentId,
    initialLikes,
  })

  // Throttle the like button to prevent spam clicking
  const throttledToggleLike = useThrottle(toggleLike, 1000)

  const sizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  }

  const buttonSizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-2 py-1 text-sm',
    lg: 'px-3 py-2 text-base',
  }

  if (variant === 'minimal') {
    return (
      <button
        onClick={throttledToggleLike}
        disabled={isLoading || !isAuthenticated}
        className={cn(
          'flex items-center space-x-1 transition-colors',
          isLiked ? 'text-red-500' : 'text-gray-500 hover:text-red-500',
          !isAuthenticated && 'cursor-not-allowed opacity-50',
          className
        )}
        title={
          !isAuthenticated
            ? 'Login to like components'
            : isLiked
              ? 'Unlike'
              : 'Like'
        }
      >
        {isLoading ? (
          <Loader2 className={cn(sizeClasses[size], 'animate-spin')} />
        ) : (
          <Heart className={cn(sizeClasses[size], isLiked && 'fill-current')} />
        )}
        {showCount && <span className="font-medium">{initialLikes}</span>}
      </button>
    )
  }

  if (variant === 'card') {
    return (
      <button
        onClick={throttledToggleLike}
        disabled={isLoading || !isAuthenticated}
        className={cn(
          'flex items-center rounded-md border border-gray-200 bg-white px-2 py-1 text-xs text-gray-700 transition-all hover:border-red-200 hover:bg-red-50',
          isLiked && 'border-red-200 bg-red-50 text-red-600',
          !isAuthenticated && 'cursor-not-allowed opacity-50',
          className
        )}
        title={
          !isAuthenticated
            ? 'Login to like components'
            : isLiked
              ? 'Unlike'
              : 'Like'
        }
      >
        {isLoading ? (
          <Loader2 className={cn(sizeClasses[size], 'mr-1 animate-spin')} />
        ) : (
          <Heart
            className={cn(
              sizeClasses[size],
              'mr-1 transition-colors',
              isLiked ? 'fill-current text-red-500' : 'text-gray-500'
            )}
          />
        )}
        {showCount && <span className="font-medium">{initialLikes}</span>}
      </button>
    )
  }

  // Default variant - button style
  return (
    <button
      onClick={throttledToggleLike}
      disabled={isLoading || !isAuthenticated}
      className={cn(
        'flex items-center space-x-1 rounded-md border transition-colors',
        buttonSizeClasses[size],
        isLiked
          ? 'border-red-200 bg-red-50 text-red-600 hover:bg-red-100'
          : 'border-gray-200 bg-white text-gray-600 hover:border-red-200 hover:bg-red-50 hover:text-red-600',
        !isAuthenticated && 'cursor-not-allowed opacity-50',
        className
      )}
      title={
        !isAuthenticated
          ? 'Login to like components'
          : isLiked
            ? 'Unlike'
            : 'Like'
      }
    >
      {isLoading ? (
        <Loader2 className={cn(sizeClasses[size], 'animate-spin')} />
      ) : (
        <Heart className={cn(sizeClasses[size], isLiked && 'fill-current')} />
      )}
      {showCount && <span className="font-medium">{initialLikes}</span>}
    </button>
  )
}
