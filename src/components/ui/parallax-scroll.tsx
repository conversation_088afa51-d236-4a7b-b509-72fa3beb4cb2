'use client'
import { useScroll, useTransform, motion } from 'framer-motion'
import Image from 'next/image'
import { useRef } from 'react'

import { cn } from '@/utils/tailwind'

export const ParallaxScroll = ({
  images,
  className,
}: {
  images: string[]
  className?: string
}) => {
  const gridRef = useRef<any>(null)
  const { scrollYProgress } = useScroll({
    container: gridRef, // remove this if your container is not fixed height
    offset: ['start start', 'end start'], // remove this if your container is not fixed height
  })

  const translateFirst = useTransform(scrollYProgress, [0, 1], [0, -200])
  const translateSecond = useTransform(scrollYProgress, [0, 1], [0, 200])
  const translateThird = useTransform(scrollYProgress, [0, 1], [0, -200])

  const third = Math.ceil(images.length / 3)

  const firstPart = images.slice(0, third)
  const secondPart = images.slice(third, 2 * third)
  const thirdPart = images.slice(2 * third)

  return (
    <div
      className={cn(
        'no-scrollbar h-[40rem] w-full items-start overflow-y-auto',
        className
      )}
      ref={gridRef}
    >
      <div
        className="mx-auto grid grid-cols-1 items-start gap-3   p-8 md:grid-cols-2 md:gap-6  lg:grid-cols-3"
        ref={gridRef}
      >
        <div className="grid gap-3 md:gap-6">
          {firstPart.map((el, idx) => (
            <motion.div
              style={{ y: translateFirst }} // Apply the translateY motion value here
              key={'grid-1' + idx}
            >
              <Image
                src={el}
                className="!m-0 h-80 w-full gap-3 rounded-lg object-cover object-left-top !p-0 md:gap-6"
                height="400"
                width="400"
                alt="thumbnail"
              />
            </motion.div>
          ))}
        </div>
        <div className="grid gap-3 md:gap-6">
          {secondPart.map((el, idx) => (
            <motion.div style={{ y: translateSecond }} key={'grid-2' + idx}>
              <Image
                src={el}
                className="!m-0 h-80 w-full gap-3 rounded-lg object-cover object-left-top !p-0 md:gap-6"
                height="400"
                width="400"
                alt="thumbnail"
              />
            </motion.div>
          ))}
        </div>
        <div className="grid gap-3 md:gap-6">
          {thirdPart.map((el, idx) => (
            <motion.div style={{ y: translateThird }} key={'grid-3' + idx}>
              <Image
                src={el}
                className="!m-0 h-80 w-full gap-3 rounded-lg object-cover object-left-top !p-0 md:gap-6"
                height="400"
                width="400"
                alt="thumbnail"
              />
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}
