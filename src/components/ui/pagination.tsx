import {
  Chevron<PERSON>eft,
  ChevronRight,
  Chevrons<PERSON>eft,
  ChevronsRight,
} from 'lucide-react'
import React from 'react'

import { Button } from '@/components/ui/button'

interface PaginationProps {
  currentPage: number
  pageCount: number
  pageLimit: number
  totalItems: number // Changed from allowing null
  isLoading: boolean
  onPageChange: (page: number) => void
}

export const Pagination = ({
  currentPage,
  pageCount,
  pageLimit,
  totalItems,
  isLoading,
  onPageChange,
}: PaginationProps) => {
  return (
    <div className="mt-6 flex flex-col items-center justify-between gap-4 rounded-lg border bg-card p-4 sm:flex-row">
      <div className="text-sm text-muted-foreground">
        {totalItems > 0 && (
          <>
            Showing {(currentPage - 1) * pageLimit + 1} to{' '}
            {Math.min(currentPage * pageLimit, totalItems)} of {totalItems}{' '}
            entries
          </>
        )}
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1 || isLoading}
          className="hidden sm:flex"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1 || isLoading}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        <div className="flex items-center gap-2">
          {Array.from({ length: pageCount }, (_, i) => i + 1)
            .filter(
              (pageNum) =>
                pageNum === 1 ||
                pageNum === pageCount ||
                (pageNum >= currentPage - 1 && pageNum <= currentPage + 1)
            )
            .map((pageNum, i, arr) => (
              <React.Fragment key={pageNum}>
                {i > 0 && arr[i - 1] !== pageNum - 1 && (
                  <span className="px-2 text-muted-foreground">...</span>
                )}
                <Button
                  variant={pageNum === currentPage ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => onPageChange(pageNum)}
                  disabled={isLoading}
                  className="hidden sm:flex"
                >
                  {pageNum}
                </Button>
              </React.Fragment>
            ))}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === pageCount || isLoading}
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(pageCount)}
          disabled={currentPage === pageCount || isLoading}
          className="hidden sm:flex"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
