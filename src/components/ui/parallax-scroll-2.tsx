'use client'
import { useScroll, useTransform, motion } from 'framer-motion'
import Image from 'next/image'
import { useRef } from 'react'

import { cn } from '@/utils/tailwind'

export const ParallaxScrollSecond = ({
  images,
  className,
}: {
  images: string[]
  className?: string
}) => {
  const gridRef = useRef<any>(null)
  const { scrollYProgress } = useScroll({
    container: gridRef, // remove this if your container is not fixed height
    offset: ['start start', 'end start'], // remove this if your container is not fixed height
  })

  const translateYFirst = useTransform(scrollYProgress, [0, 1], [0, -200])
  const translateXFirst = useTransform(scrollYProgress, [0, 1], [0, -200])
  const rotateXFirst = useTransform(scrollYProgress, [0, 1], [0, -20])

  const translateYThird = useTransform(scrollYProgress, [0, 1], [0, -200])
  const translateXThird = useTransform(scrollYProgress, [0, 1], [0, 200])
  const rotateXThird = useTransform(scrollYProgress, [0, 1], [0, 20])

  const third = Math.ceil(images.length / 3)

  const firstPart = images.slice(0, third)
  const secondPart = images.slice(third, 2 * third)
  const thirdPart = images.slice(2 * third)

  return (
    <div
      className={cn('h-[40rem] w-full items-start overflow-y-auto', className)}
      ref={gridRef}
    >
      <div
        className="mx-auto grid max-w-5xl grid-cols-1 items-start gap-10 px-10 py-40 md:grid-cols-2 lg:grid-cols-3"
        ref={gridRef}
      >
        <div className="grid gap-10">
          {firstPart.map((el, idx) => (
            <motion.div
              style={{
                y: translateYFirst,
                x: translateXFirst,
                rotateZ: rotateXFirst,
              }} // Apply the translateY motion value here
              key={'grid-1' + idx}
            >
              <Image
                src={el}
                className="!m-0 h-80 w-full gap-10 rounded-lg object-cover object-left-top !p-0"
                height="400"
                width="400"
                alt="thumbnail"
              />
            </motion.div>
          ))}
        </div>
        <div className="grid gap-10">
          {secondPart.map((el, idx) => (
            <motion.div key={'grid-2' + idx}>
              <Image
                src={el}
                className="!m-0 h-80 w-full gap-10 rounded-lg object-cover object-left-top !p-0"
                height="400"
                width="400"
                alt="thumbnail"
              />
            </motion.div>
          ))}
        </div>
        <div className="grid gap-10">
          {thirdPart.map((el, idx) => (
            <motion.div
              style={{
                y: translateYThird,
                x: translateXThird,
                rotateZ: rotateXThird,
              }}
              key={'grid-3' + idx}
            >
              <Image
                src={el}
                className="!m-0 h-80 w-full gap-10 rounded-lg object-cover object-left-top !p-0"
                height="400"
                width="400"
                alt="thumbnail"
              />
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}
