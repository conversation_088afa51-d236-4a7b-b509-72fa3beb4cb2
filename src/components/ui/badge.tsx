import { cva, type VariantProps } from 'class-variance-authority'
import * as React from 'react'

import { cn } from '@/lib/utils'

const badgeVariants = cva(
  'inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80',
        secondary:
          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
        success:
          'border-transparent bg-green-300 text-green-800 shadow hover:bg-green-400',
        destructive:
          'border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80',
        outline: 'text-foreground',
        premium:
          'border-none bg-gradient-to-r from-orange-400 via-amber-400 to-orange-400 bg-[length:200%_100%] text-white',
        warning:
          'border-none bg-gradient-to-r from-yellow-400 to-yellow-500 text-white',
        // Improved variants for navigation labels
        new: 'border-none bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-[0_2px_8px_-2px_rgba(59,130,246,0.5)] dark:from-blue-400 dark:to-blue-500',
        hot: 'border-none bg-gradient-to-r from-orange-500 via-red-500 to-orange-500 text-white shadow-[0_2px_8px_-2px_rgba(249,115,22,0.5)] animate-gradient bg-[length:200%_100%]',
        beta: 'border-none bg-gradient-to-r from-violet-500 to-purple-500 text-white shadow-[0_2px_8px_-2px_rgba(139,92,246,0.5)] dark:from-violet-400 dark:to-purple-400',
        pro: 'border-transparent bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
