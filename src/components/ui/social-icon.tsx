'use client'
import { ReactNode } from 'react'

import { cn } from '@/lib/utils'

type SocialPlatform =
  | 'instagram'
  | 'linkedin'
  | 'discord'
  | 'twitter'
  | 'facebook'
  | 'youtube'
  | 'github'
  | 'tiktok'
  | 'pinterest'

interface SocialIconProps {
  platform: SocialPlatform
  href: string
  icon: ReactNode
  size?: 'sm' | 'md' | 'lg'
  className?: string
  title?: string
}

const brandColors: Record<
  SocialPlatform,
  { bg: string; hover: string; focus: string }
> = {
  instagram: {
    bg: 'bg-gradient-to-tr from-yellow-400 via-red-500 to-purple-600',
    hover: 'hover:from-yellow-500 hover:via-red-600 hover:to-purple-700',
    focus: 'focus:from-yellow-500 focus:via-red-600 focus:to-purple-700',
  },
  linkedin: {
    bg: 'bg-[#0077B5]',
    hover: 'hover:bg-[#005e8c]',
    focus: 'focus:bg-[#005e8c]',
  },
  discord: {
    bg: 'bg-[#5865F2]',
    hover: 'hover:bg-[#4752c4]',
    focus: 'focus:bg-[#4752c4]',
  },
  twitter: {
    bg: 'bg-[#1DA1F2]',
    hover: 'hover:bg-[#0c85d0]',
    focus: 'focus:bg-[#0c85d0]',
  },
  facebook: {
    bg: 'bg-[#1877F2]',
    hover: 'hover:bg-[#0c5dc9]',
    focus: 'focus:bg-[#0c5dc9]',
  },
  youtube: {
    bg: 'bg-[#FF0000]',
    hover: 'hover:bg-[#cc0000]',
    focus: 'focus:bg-[#cc0000]',
  },
  github: {
    bg: 'bg-[#333333]',
    hover: 'hover:bg-[#1a1a1a]',
    focus: 'focus:bg-[#1a1a1a]',
  },
  tiktok: {
    bg: 'bg-black',
    hover: 'hover:bg-[#121212]',
    focus: 'focus:bg-[#121212]',
  },
  pinterest: {
    bg: 'bg-[#E60023]',
    hover: 'hover:bg-[#b8001c]',
    focus: 'focus:bg-[#b8001c]',
  },
}

const sizeClasses = {
  sm: 'h-7 w-7',
  md: 'h-9 w-9',
  lg: 'h-11 w-11',
}

export function SocialIcon({
  platform,
  href,
  icon,
  size = 'md',
  className,
  title,
}: SocialIconProps) {
  const brandColor = brandColors[platform]

  return (
    <a
      href={href}
      title={title || platform.charAt(0).toUpperCase() + platform.slice(1)}
      target="_blank"
      rel="noopener noreferrer"
      className={cn(
        'flex items-center justify-center rounded-full text-white transition-all duration-300',
        'transform shadow-sm hover:-translate-y-1 hover:shadow-md',
        sizeClasses[size],
        brandColor.bg,
        brandColor.hover,
        brandColor.focus,
        className
      )}
    >
      {icon}
    </a>
  )
}
