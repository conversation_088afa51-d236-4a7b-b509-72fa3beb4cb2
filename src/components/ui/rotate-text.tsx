'use client'

import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useState } from 'react'

import { cn } from '@/lib/utils'

interface RotateTextProps {
  rotatedString: string[]
  className?: string
  interval?: number
}

export function RotateText({
  rotatedString,
  className,
  interval = 3000,
}: RotateTextProps) {
  const [index, setIndex] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setIsAnimating(true)
      setIndex((prevIndex) => (prevIndex + 1) % rotatedString.length)
    }, interval)

    return () => clearInterval(timer)
  }, [rotatedString.length, interval])

  return (
    <div className="relative h-[1.2em] overflow-hidden">
      <AnimatePresence mode="wait">
        <motion.span
          key={rotatedString[index]}
          initial={{
            y: 50,
            opacity: 0,
          }}
          animate={{
            y: 0,
            opacity: 1,
            transition: {
              duration: 0.4,
              ease: 'easeOut',
            },
          }}
          exit={{
            y: -50,
            opacity: 0,
            transition: {
              duration: 0.4,
              ease: 'easeIn',
            },
          }}
          onAnimationComplete={() => setIsAnimating(false)}
          className={cn(
            'absolute inset-0',
            'whitespace-nowrap',
            'text-center font-heading font-medium tracking-[-0.02em]',
            !isAnimating && 'transition-all duration-200',
            className
          )}
        >
          {rotatedString[index]}
        </motion.span>
      </AnimatePresence>
    </div>
  )
}
