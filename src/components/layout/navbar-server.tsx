import Image from 'next/image'
import Link from 'next/link'

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components//ui/navigation-menu'
import { resources, routeList } from '@/utils/constants/nav'

import { MobileNavbarClient } from './mobile-navbar-client'
import { NavbarClient } from './navbar-client'
import { NavbarWrapper } from './navbar-wrapper'
import { ProBadgeClient } from './pro-badge-client'

export const NavbarServer = async () => {
  return (
    <NavbarWrapper>
      <Link
        prefetch={true}
        href="/"
        className="flex items-center text-lg font-bold"
      >
        <Image
          className="rounded-lg"
          width={80}
          height={20}
          src="https://utfs.io/f/LhUzHyAuqPAKUSSOY3WlNcqwD9Fjd5n0CPIg1bEmyO3u7oRA"
          alt="CopyElement Logo"
        />
      </Link>

      {/* Mobile Navigation */}
      <MobileNavbarClient />

      {/* Desktop Navigation */}
      <NavigatinoMenuDesktop />

      <div className="hidden gap-3 lg:flex">
        <ProBadgeClient />
        <NavbarClient />
      </div>
    </NavbarWrapper>
  )
}

export const NavigatinoMenuDesktop = () => {
  return (
    <NavigationMenu className="mx-auto hidden bg-none lg:block">
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger className="bg-transparent bg-none text-sm text-primary dark:text-purple-100">
            Resources
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="grid w-[600px] grid-cols-2 gap-5 p-4">
              <Image
                src={
                  'https://utfs.io/f/qPlpyBmwd8UN6sAWpd1T2ZnPVuhMI58yDHfzrm4Sa1Epjcwx'
                }
                alt="RadixLogo"
                className="h-full w-full rounded-md object-cover"
                width={600}
                height={600}
              />
              <ul className="flex flex-col gap-2">
                {resources.map(({ title, description, href, isExternal }) => (
                  <li key={title} className="flex w-full ">
                    <Link
                      prefetch={true}
                      href={href ?? '#'}
                      target={isExternal ? '_blank' : '_self'}
                      className="cursor-pointer rounded-md p-3 px-5 font-heading text-sm font-normal hover:bg-secondary/50"
                    >
                      <p className="mb-1 font-semibold leading-none text-foreground">
                        {title}
                      </p>
                      <p className="line-clamp-2 text-muted-foreground">
                        {description}
                      </p>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>

        <NavigationMenuItem>
          {routeList.map(({ href, label }) => (
            <NavigationMenuLink key={href} asChild>
              <Link
                prefetch={true}
                href={href}
                className="px-2 font-heading text-sm font-medium tracking-normal text-primary transition-all hover:opacity-70 dark:text-white  "
              >
                {label}
              </Link>
            </NavigationMenuLink>
          ))}
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  )
}
