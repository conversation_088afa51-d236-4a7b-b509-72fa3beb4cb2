'use client'

import { motion } from 'framer-motion'
import { usePathname } from 'next/navigation'
import { ReactNode } from 'react'

import { excludesUrls } from '@/utils/constants/nav'

interface NavbarWrapperProps {
  children: ReactNode
}

export function NavbarWrapper({ children }: NavbarWrapperProps) {
  const pathname = usePathname()

  if (excludesUrls?.some((url) => pathname.startsWith(url))) {
    return null
  }

  return (
    <motion.header
      animate={{ y: 0 }}
      initial={{ y: -100 }}
      transition={{ duration: 0.5 }}
      className="bg-opacity-15 fixed left-0 right-0 top-0 z-40 mx-auto flex w-full max-w-7xl items-center justify-between rounded-none border border-[rgba(124,124,124,0.2)] p-2 shadow-inner backdrop-blur-lg md:rounded-b-2xl"
    >
      {children}
    </motion.header>
  )
}
