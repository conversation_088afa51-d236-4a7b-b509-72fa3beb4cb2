'use client'

import { useQuery } from '@tanstack/react-query'
import { AppWindowIcon, LogInIcon } from 'lucide-react'
import Link from 'next/link'

import { UserNav } from '@/components/layout/user-nav'
import { useProfileStore } from '@/stores/profile-store'
import { createClient } from '@/utils/supabase/client'

import { Button } from '../ui/button'
import Skeleton from '../ui/skeleton'

const dashboardAllowedRoles = ['admin', 'editor']

export function NavbarClient() {
  const { profile, fetchUser } = useProfileStore()

  const supabase = createClient()

  const { isLoading, error, data } = useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const { data } = await supabase.auth.getUser()
      return data
    },
  })

  const user = data?.user

  // Simple query that refetches properly after login
  useQuery({
    queryKey: ['user-profile'],
    queryFn: fetchUser,
    staleTime: 1000 * 60, // 60 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  })

  // Show loading only if we're loading AND don't have a user yet
  if (isLoading && !user) {
    return (
      <div className="flex gap-2">
        <Skeleton className="h-8 w-20" />
      </div>
    )
  }

  if (user) {
    return (
      <div className="flex gap-2">
        <UserNav />
        {dashboardAllowedRoles.includes(profile?.role as string) ? (
          <Button asChild size={'sm'}>
            <Link prefetch={true} href={'/dashboard'}>
              <AppWindowIcon className="mr-1 h-4 w-4" />
              Dashboard
            </Link>
          </Button>
        ) : (
          <Button asChild size={'sm'}>
            <Link prefetch={true} href={'/components'}>
              Components
            </Link>
          </Button>
        )}
      </div>
    )
  }

  return (
    <Button asChild aria-label="Login" size={'sm'}>
      <Link prefetch={true} aria-label="Login button" href="/login">
        <LogInIcon className="mr-1 h-4 w-4" />
        Login
      </Link>
    </Button>
  )
}
