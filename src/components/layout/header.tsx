import { ChevronsDown } from 'lucide-react'
import { cookies } from 'next/headers'
import Link from 'next/link'

import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { createClient } from '@/utils/supabase/server'

import { NavigatinoMenuDesktop } from './navbar-server'
import { UserNav } from './user-nav'

export default async function Header() {
  const cookieStore = cookies()
  // const supabase = createServerClient(cookieStore)
  const supabase = createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    // return redirect("/login");
  }

  return (
    <div className="supports-backdrop-blur:bg-background/60 fixed left-0 right-0 top-0 z-20 border-b bg-background/95 backdrop-blur">
      <nav className="flex h-14 items-center justify-between px-4">
        <div className="hidden lg:block">
          <Link
            prefetch={true}
            href="/"
            className="flex items-center text-lg font-bold"
          >
            <ChevronsDown className="mr-2 h-9 w-9 rounded-lg border border-secondary bg-gradient-to-tr from-primary via-primary/70 to-primary text-white" />
            CopyElement
          </Link>
        </div>
        <div className={cn('block lg:!hidden')}>{/* <MobileSidebar /> */}</div>

        <NavigatinoMenuDesktop />

        <div className="flex items-center gap-2">
          {/* <ThemeToggle /> */}

          {user ? (
            <UserNav />
          ) : (
            <Button asChild size={'sm'} aria-label="Login">
              <Link prefetch={true} aria-label="Login button" href="/login">
                {/* <Github className="size-5" /> */}
                Login
              </Link>
            </Button>
          )}
        </div>
      </nav>
    </div>
  )
}
