'use client'

import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'

import { useProfileStore } from '@/stores/profile-store'

import MagicBadge from '../ui/magic-badge'

export function ProBadgeClient() {
  const { user, profile, fetchUser } = useProfileStore()

  // Simple query that refetches properly after login
  useQuery({
    queryKey: ['user-profile'],
    queryFn: fetchUser,
    staleTime: 1000 * 60, // 60 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  })

  if (!profile?.is_pro && user) {
    return (
      <Link prefetch={true} aria-label="Login button" href="/pricing">
        <MagicBadge title="✨ Get PRO" />
      </Link>
    )
  }

  return null
}
