'use client'
import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components//ui/navigation-menu'
import { excludesUrls, resources, routeList } from '@/utils/constants/nav'

import { MobileNavbarClient } from './mobile-navbar-client'
import { NavbarClient } from './navbar-client'
import { ProBadgeClient } from './pro-badge-client'

export const Navbar = () => {
  const pathname = usePathname()

  if (excludesUrls?.some((url) => pathname.startsWith(url))) {
    return null
  }

  return (
    <motion.header
      animate={{ y: 0 }}
      initial={{ y: -100 }}
      transition={{ duration: 0.5 }}
      className="bg-opacity-15 fixed left-0 right-0 top-0 z-40 mx-auto flex w-full max-w-7xl items-center justify-between rounded-none border border-[rgba(124,124,124,0.2)] p-2 shadow-inner backdrop-blur-lg md:rounded-b-2xl"
    >
      <Link
        prefetch={true}
        href="/"
        className="flex items-center text-lg font-bold"
      >
        <Image
          className="rounded-lg"
          width={80}
          height={20}
          src="https://utfs.io/f/LhUzHyAuqPAKUSSOY3WlNcqwD9Fjd5n0CPIg1bEmyO3u7oRA"
          alt="CopyElement Logo"
        />
      </Link>

      {/* Mobile Navigation */}
      <MobileNavbarClient />

      {/* Desktop Navigation */}
      <NavigatinoMenuDesktop />

      <div className="hidden gap-3 lg:flex">
        <ProBadgeClient />
        <NavbarClient />
      </div>
    </motion.header>
  )
}

export const NavigatinoMenuDesktop = () => {
  return (
    <NavigationMenu className="mx-auto hidden bg-none lg:block">
      <NavigationMenuList>
        <NavigationMenuItem>
          <NavigationMenuTrigger className="bg-transparent bg-none text-sm text-primary dark:text-purple-100">
            Features
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <div className="grid w-[600px] grid-cols-2 gap-5 p-4">
              <Image
                // src="https://avatars.githubusercontent.com/u/75042455?v=4"
                src={
                  'https://utfs.io/f/qPlpyBmwd8UN6sAWpd1T2ZnPVuhMI58yDHfzrm4Sa1Epjcwx'
                }
                alt="RadixLogo"
                className="h-full w-full rounded-md object-cover"
                width={600}
                height={600}
              />
              <ul className="flex flex-col gap-2">
                {resources.map(({ title, description }) => (
                  <li
                    key={title}
                    className="rounded-md p-3 font-heading text-sm font-normal hover:bg-muted"
                  >
                    <p className="mb-1 font-semibold leading-none text-foreground">
                      {title}
                    </p>
                    <p className="line-clamp-2 text-muted-foreground">
                      {description}
                    </p>
                  </li>
                ))}
              </ul>
            </div>
          </NavigationMenuContent>
        </NavigationMenuItem>

        <NavigationMenuItem>
          {routeList.map(({ href, label }) => (
            <NavigationMenuLink key={href} asChild>
              <Link
                prefetch={true}
                href={href}
                className="px-2 font-heading text-sm font-medium tracking-normal text-primary transition-all hover:opacity-70 dark:text-white  "
              >
                {label}
              </Link>
            </NavigationMenuLink>
          ))}
          {/* <NavigationMenuLink key={'blogs'} asChild>
            <Link
              target="_blank"
              href="https://blog.copyelement.com"
              className="px-2 font-heading text-sm font-medium text-primary transition-all hover:opacity-70 dark:text-white  "
            >
              Blogs
            </Link>
          </NavigationMenuLink> */}
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  )
}
