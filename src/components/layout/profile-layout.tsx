'use client'

import { ReactNode } from 'react'

import { cn } from '@/lib/utils'

interface ProfileLayoutProps {
  children: ReactNode
  className?: string
}

export function ProfileLayout({ children, className }: ProfileLayoutProps) {
  return (
    <div className={cn('relative min-h-screen w-full', className)}>
      {/* Enhanced background with subtle animations */}
      <div className="fixed inset-0 -z-10 overflow-hidden">
        {/* Primary gradient orbs */}
        <div className="absolute -right-40 -top-40 h-80 w-80 animate-pulse rounded-full bg-gradient-to-br from-primary/20 via-primary/10 to-transparent blur-3xl [animation-duration:4s]" />
        <div className="absolute -bottom-40 -left-40 h-80 w-80 animate-pulse rounded-full bg-gradient-to-br from-accent/20 via-accent/10 to-transparent blur-3xl [animation-delay:2s] [animation-duration:6s]" />
        <div className="absolute left-1/2 top-1/2 h-60 w-60 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-gradient-to-br from-muted/20 via-muted/10 to-transparent blur-3xl [animation-delay:1s] [animation-duration:5s]" />

        {/* Secondary smaller orbs */}
        <div className="absolute right-1/4 top-1/4 h-40 w-40 animate-pulse rounded-full bg-gradient-to-br from-primary/10 to-transparent blur-2xl [animation-delay:3s] [animation-duration:7s]" />
        <div className="from-accent/15 absolute bottom-1/4 left-1/4 h-32 w-32 animate-pulse rounded-full bg-gradient-to-br to-transparent blur-2xl [animation-duration:4s] [animation-delay:4s]" />

        {/* Grid pattern overlay */}
        <div className="absolute inset-0 bg-grid-black/[0.01] [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.3))]" />

        {/* Center fade overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-background via-transparent to-background" />
        <div className="absolute inset-0 bg-gradient-to-b from-background via-transparent to-background" />
      </div>

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </div>
  )
}
