'use client'
import { useQuery } from '@tanstack/react-query'
import Link from 'next/link'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { useProfileStore } from '@/stores/profile-store'
import { createClient } from '@/utils/supabase/client'

export function UserNav() {
  // const [user, setUser] = useState<User | null>(null)
  const supabase = createClient()
  // const router = useRouter()
  const { profile } = useProfileStore()

  // useEffect(() => {
  //   const getUser = async () => {
  //     const {
  //       data: { user },
  //     } = await supabase.auth.getUser()
  //     setUser(user)
  //   }
  //   getUser()
  // }, [supabase])

  const { isLoading, error, data } = useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      // const { data } = await supabase.from('categories').select('*').range(0, 9)
      const { data } = await supabase.auth.getUser()
      return data
    },
  })

  const user = data?.user

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    // router.push('/login')
  }

  if (user) {
    return (
      // <DropdownMenu>
      //   <DropdownMenuTrigger asChild>
      <Button asChild variant="ghost" className="relative h-8 w-8 rounded-full">
        <Link href="/profile" className="flex items-center justify-center">
          <Avatar
            className={cn(
              'h-8 w-8 border-2 border-gray-600 ',
              profile && profile?.is_pro && ' border-yellow-400'
            )}
          >
            <AvatarImage
              src={
                profile?.avatar_url ??
                user.user_metadata?.avatar_url ??
                'https://utfs.io/f/qPlpyBmwd8UN033pxSZHjiT1qY2szFub9aEHfI6BdlnMVcQG'
              }
              alt={user.user_metadata?.full_name ?? ''}
            />
            <AvatarFallback>
              {user.user_metadata?.full_name?.[0]}
            </AvatarFallback>
          </Avatar>
        </Link>
      </Button>
      //   </DropdownMenuTrigger>
      //   <DropdownMenuContent className="w-56 px-4 py-3" align="end" forceMount>
      //     <DropdownMenuLabel className="font-normal">
      //       <div className="flex flex-col space-y-1">
      //         <p className="text-sm font-medium leading-none">
      //           {user.user_metadata?.full_name}
      //         </p>
      //         <p className="text-xs leading-none text-muted-foreground">
      //           {user.email}
      //         </p>
      //       </div>
      //     </DropdownMenuLabel>
      //     <DropdownMenuSeparator />
      //     <DropdownMenuGroup>
      //       <DropdownMenuItem className="w-full bg-transparent p-0">
      //         <Button
      //           variant={'outline-primary'}
      //           className="button block h-full w-full text-center"
      //           type="submit"
      //           asChild
      //         >
      //           <Link href={'/profile'} className="w-full">
      //             Profile
      //           </Link>
      //         </Button>
      //       </DropdownMenuItem>
      //       {/* <DropdownMenuItem>
      //         Billing
      //         <DropdownMenuShortcut>⌘B</DropdownMenuShortcut>
      //       </DropdownMenuItem>
      //       <DropdownMenuItem>
      //         Settings
      //         <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
      //       </DropdownMenuItem>
      //       <DropdownMenuItem>New Team</DropdownMenuItem> */}
      //     </DropdownMenuGroup>
      //     <DropdownMenuSeparator />
      //     {/* <DropdownMenuItem>
      //       <Button>
      //         {profile?.is_pro ? 'Manage Subscription' : 'Upgrade to Pro'}
      //       </Button>
      //     </DropdownMenuItem> */}
      //     <DropdownMenuItem className="bg-transparent p-0">
      //       <form
      //         className="h-full w-full "
      //         action="/auth/signout"
      //         method="post"
      //       >
      //         <Button
      //           variant={'destructive'}
      //           className="button block h-full w-full"
      //           type="submit"
      //         >
      //           Log out
      //         </Button>
      //       </form>
      //     </DropdownMenuItem>
      //   </DropdownMenuContent>
      // </DropdownMenu>
    )
  }
  return null
}
