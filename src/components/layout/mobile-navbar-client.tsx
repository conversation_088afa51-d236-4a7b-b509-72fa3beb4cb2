'use client'

import { useQuery } from '@tanstack/react-query'
import { Menu } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { useState } from 'react'

import { UserNav } from '@/components/layout/user-nav'
import { useProfileStore } from '@/stores/profile-store'
import { routeList } from '@/utils/constants/nav'

import { Button } from '../ui/button'
import MagicBadge from '../ui/magic-badge'
import { Separator } from '../ui/separator'
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>ooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '../ui/sheet'
import Skeleton from '../ui/skeleton'

export function MobileNavbarClient() {
  const [isOpen, setIsOpen] = useState(false)
  const { user, profile, isLoading, fetchUser } = useProfileStore()

  // Simple query that refetches properly after login
  useQuery({
    queryKey: ['user-profile'],
    queryFn: fetchUser,
    staleTime: 1000 * 60, // 60 seconds
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  })

  return (
    <div className="flex items-center gap-2 lg:hidden">
      <div className="flex gap-3">
        {!profile?.is_pro && user && (
          <Link prefetch={true} aria-label="Login button" href="/pricing">
            <MagicBadge title="✨ Get PRO" />
          </Link>
        )}
        {isLoading && !user ? (
          <Skeleton className="h-8 w-16" />
        ) : user ? (
          <UserNav />
        ) : (
          <Button asChild aria-label="Login" size={'sm'}>
            <Link prefetch={true} aria-label="Login button" href="/login">
              Login
            </Link>
          </Button>
        )}
      </div>

      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Menu
            onClick={() => setIsOpen(!isOpen)}
            className="cursor-pointer lg:hidden"
          />
        </SheetTrigger>

        <SheetContent
          side="left"
          className="flex flex-col justify-between rounded-br-2xl rounded-tr-2xl border-secondary bg-card"
        >
          <div>
            <SheetHeader className="mb-4 ml-4">
              <SheetTitle className="flex items-center">
                <Link prefetch={true} href="/" className="flex items-center">
                  <Image
                    className="rounded-lg"
                    width={150}
                    height={60}
                    src="https://utfs.io/f/LhUzHyAuqPAKUSSOY3WlNcqwD9Fjd5n0CPIg1bEmyO3u7oRA"
                    alt="CopyElement Logo"
                  />
                </Link>
              </SheetTitle>
            </SheetHeader>

            <div className="flex flex-col gap-2">
              {routeList.map(({ href, label }) => (
                <Button
                  key={href}
                  onClick={() => setIsOpen(false)}
                  asChild
                  variant="ghost"
                  className="justify-start text-sm"
                >
                  <Link prefetch={true} href={href}>
                    {label}
                  </Link>
                </Button>
              ))}
            </div>
          </div>

          <SheetFooter className="flex-col items-start justify-start sm:flex-col">
            <Separator className="mb-2" />
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  )
}
