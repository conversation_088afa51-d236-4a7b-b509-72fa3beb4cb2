// import Link from 'next/link'
// import { mergeTW } from 'lib/utils'
import Link from 'next/link'
import { AnchorHTMLAttributes, ReactNode } from 'react'

import { cn } from '@/lib/utils'

interface Props extends AnchorHTMLAttributes<HTMLAnchorElement> {
  children: ReactNode
  href: string
  className?: string
  variant?: 'default' | 'shiny'
}

const LinkItem = ({
  children,
  href,
  className = '',
  variant = 'default',
  ...props
}: Props) => (
  <Link
    prefetch={true}
    {...props}
    href={href}
    // className={cn(`${buttonVariants[variant]} ${className}`)}
    className={cn(` ${className}`)}
  >
    {children}
  </Link>
)

export default LinkItem
