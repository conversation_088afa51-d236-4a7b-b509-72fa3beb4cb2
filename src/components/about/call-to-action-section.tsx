import { Users } from 'lucide-react'
import Link from 'next/link'

import Heading from '@/components/custom/heading'
import { Button } from '@/components/ui/button'

const CallToActionSection = () => {
  return (
    <section className="relative overflow-hidden py-24">
      <div className="container relative z-10 mx-auto px-4 text-center">
        <div>
          <Heading>
            Ready to Supercharge Your <br /> Elementor Workflow?
          </Heading>
          <p className="mx-auto mb-8 max-w-2xl text-xl text-muted-foreground">
            Join thousands of web designers and developers who are building
            amazing websites faster than ever with CopyElement.
          </p>
          <Button size="lg" asChild>
            <Link prefetch={true} href="/components">
              Get Started for Free
              <Users className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}

export default CallToActionSection
