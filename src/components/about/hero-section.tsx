import { <PERSON>, Co<PERSON>, Zap } from 'lucide-react'
import Link from 'next/link'

import Heading from '@/components/custom/heading'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

const HeroSection = () => {
  return (
    <section className="relative min-h-screen overflow-hidden pt-10">
      {/* Background Image */}
      <div
        className="absolute inset-0 -z-10 bg-contain bg-center bg-no-repeat"
        style={{
          backgroundImage:
            'url(https://gallery.theportfolio.in/background-images/1750953293834-section-bg-13.webp)',
        }}
      />

      {/* Clean overlay for better readability */}
      <div className="absolute inset-0 -z-10 bg-black/50" />

      <div className="relative mx-auto w-full px-5 pb-32 pt-40 sm:px-10 md:px-12 lg:max-w-7xl lg:px-5">
        {/* Hero Content */}
        <div className="mx-auto max-w-4xl text-center">
          <div className="space-y-4">
            <Heading className="  font-heading">
              Build Websites Faster with CopyElement
            </Heading>
            <p className="mx-auto max-w-2xl text-base leading-relaxed text-muted-foreground md:text-lg">
              CopyElement is the largest ready-to-use, free Elementor component
              library. Quickly build websites with perfectly designed components
              you can copy and paste directly into the Elementor page builder.
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-wrap items-center justify-center gap-6 pt-12">
            <Button
              asChild
              size="lg"
              className="bg-zinc-100 text-zinc-900 hover:bg-zinc-700 hover:text-zinc-50"
              variant={'default'}
            >
              <Link prefetch={true} href="/components">
                Explore Components
              </Link>
            </Button>
            <Button
              variant="default"
              size="lg"
              className="bg-zinc-100 text-zinc-900 hover:bg-zinc-700 hover:text-zinc-50"
            >
              <Link prefetch={true} href="/templates">
                Explore Templates
              </Link>
            </Button>
          </div>
        </div>

        {/* Feature Grid */}
        <div className="mx-auto mt-24 grid max-w-6xl gap-8 lg:grid-cols-3">
          <FeatureCard
            icon={<Copy className="h-7 w-7" />}
            title="Largest Library"
            description="Access thousands of ready-to-use Elementor components designed for modern websites."
          />
          <FeatureCard
            icon={<Zap className="h-7 w-7" />}
            title="Lightning Fast"
            description="Copy and paste components directly into Elementor for rapid development and deployment."
          />
          <FeatureCard
            icon={<Clock className="h-7 w-7" />}
            title="Time-Saving"
            description="Reduce development time significantly with pre-designed elements and layouts."
          />
        </div>
      </div>
    </section>
  )
}

const FeatureCard = ({
  icon,
  title,
  description,
}: {
  icon: React.ReactNode
  title: string
  description: string
}) => {
  return (
    <Card className="  border-border transition-all duration-300 ">
      <CardHeader className="flex flex-col items-center text-center">
        <div className="mb-4 rounded-xl bg-secondary p-3 text-primary-foreground shadow-lg transition-all duration-300 group-hover:scale-110 group-hover:shadow-primary/25">
          {icon}
        </div>
        <CardTitle className="text-white">{title}</CardTitle>
      </CardHeader>
      <CardContent className="text-center">
        <CardDescription className="text-white/80">
          {description}
        </CardDescription>
      </CardContent>
    </Card>
  )
}

export default HeroSection
