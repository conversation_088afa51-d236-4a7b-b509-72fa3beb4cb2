'use client'

import { useState } from 'react'

import { Profile } from '@/types/custom'

import { ProfileHeader } from './profile-header'
import { ProfileStats } from './profile-stats'
import { ProfileTabs } from './profile-tabs'

interface UserProfileProps {
  profile: Profile
}

export function UserProfile({ profile }: UserProfileProps) {
  const [isEditing, setIsEditing] = useState(false)

  return (
    <div className="container mx-auto max-w-7xl space-y-8 px-4 py-8 sm:space-y-12 sm:px-6 sm:py-12">
      {/* Profile Header */}
      <ProfileHeader
        profile={profile}
        onEditToggle={() => setIsEditing(!isEditing)}
        isEditing={isEditing}
      />

      {/* Profile Stats */}
      <ProfileStats profile={profile} />

      {/* Profile Tabs */}
      <ProfileTabs
        profile={profile}
        isEditing={isEditing}
        onEditToggle={() => setIsEditing(!isEditing)}
        onEditSuccess={() => setIsEditing(false)}
      />
    </div>
  )
}
