'use client'

import { motion } from 'framer-motion'
import { ArrowLeft, Heart, Sparkles } from 'lucide-react'

import LinkItem from '@/components/LInkItem/LinkItem'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface LikedPageBannerProps {
  likedComponentsCount: number
}

export function LikedPageBanner({
  likedComponentsCount,
}: LikedPageBannerProps) {
  return (
    <div className="bg-background">
      <Card className="border-border/50">
        <CardContent className="p-8">
          <div className="flex items-center justify-between">
            {/* Left Content */}
            <div className="flex-1">
              {/* Back Button */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
                className="mb-6"
              >
                <LinkItem href="/profile">
                  <Button variant="outline" size="sm" className="gap-2">
                    <ArrowLeft className="h-4 w-4" />
                    Back to Profile
                  </Button>
                </LinkItem>
              </motion.div>

              {/* Title and Description */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <div className="flex items-center gap-4">
                  <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-rose-500 text-primary-foreground">
                    <Heart className="h-6 w-6" fill="currentColor" />
                  </div>
                  <div>
                    <h1 className="text-3xl font-bold text-foreground">
                      Liked Components
                    </h1>
                    <p className="text-muted-foreground">
                      Your curated collection of favorite UI components
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Right Content - Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="flex flex-col items-end gap-3"
            >
              <Badge variant="hot" className="gap-2">
                <Heart className="h-4 w-4" />
                <span>
                  {likedComponentsCount} Component
                  {likedComponentsCount !== 1 ? 's' : ''}
                </span>
              </Badge>

              <div className="flex items-center gap-2 text-sm text-primary-foreground">
                <Sparkles className="h-4 w-4" />
                <span>Ready to use</span>
              </div>
            </motion.div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
