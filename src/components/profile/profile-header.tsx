'use client'

import { useMutation, useQuery } from '@tanstack/react-query'
import { BadgeCheck, Crown, Edit, Heart, Mail, User, X } from 'lucide-react'
import { toast } from 'sonner'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Profile } from '@/types/custom'
import { createClient } from '@/utils/supabase/client'

import LinkItem from '../LInkItem/LinkItem'

interface ProfileHeaderProps {
  profile: Profile
  onEditToggle: () => void
  isEditing: boolean
}

export function ProfileHeader({
  profile,
  onEditToggle,
  isEditing,
}: ProfileHeaderProps) {
  const supabase = createClient()

  // Fetch auth user data
  const { data: userData } = useQuery({
    queryKey: ['auth-user'],
    queryFn: async () => {
      const { data } = await supabase.auth.getUser()
      return data
    },
  })

  const authUser = userData?.user

  const resendVerificationEmail = useMutation({
    mutationFn: async () => {
      if (!profile.email) return
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: profile.email,
      })
      if (error) throw error
    },
    onSuccess: () => {
      toast.success('Verification email sent successfully')
    },
    onError: () => {
      toast.error('Failed to send verification email')
    },
  })

  const getFullName = () => {
    if (profile.first_name && profile.last_name) {
      return `${profile.first_name} ${profile.last_name}`
    }
    return profile.username || 'Unnamed User'
  }

  const getInitials = () => {
    if (profile.first_name && profile.last_name) {
      return `${profile.first_name[0]}${profile.last_name[0]}`.toUpperCase()
    }
    if (profile.username) {
      return profile.username.slice(0, 2).toUpperCase()
    }
    return 'UN'
  }

  return (
    <div className="relative h-full overflow-hidden">
      {/* Hero Background - Vibrant */}
      <div className="min-h-64  relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-700 via-purple-700 to-blue-700 dark:from-indigo-900 dark:via-purple-900 dark:to-blue-900 sm:h-80 sm:rounded-3xl">
        {/* Colorful geometric shapes */}
        <div className="animate-float absolute left-10 top-10 h-16 w-16 rounded-full bg-gradient-to-br from-blue-400/20 to-cyan-400/20 backdrop-blur-sm" />
        <div className="animate-float absolute right-20 top-20 h-12 w-12 rotate-45 bg-gradient-to-br from-purple-400/20 to-pink-400/20 backdrop-blur-sm [animation-delay:0.5s]" />
        <div className="animate-float absolute bottom-20 left-1/4 h-8 w-8 rounded-full bg-gradient-to-br from-emerald-400/20 to-teal-400/20 backdrop-blur-sm [animation-delay:1s]" />

        {/* Profile Content */}
        <div className="relative flex h-full flex-col justify-end p-4 sm:p-6 lg:p-8">
          <div className="flex flex-col gap-6 sm:gap-8 lg:flex-row lg:items-end lg:justify-between">
            {/* Avatar and Basic Info */}
            <div className="flex flex-col gap-4 sm:gap-6 lg:flex-row lg:items-end">
              {/* Profile Avatar */}
              <div className="group relative mx-auto lg:mx-0">
                <div className="animate-tilt absolute -inset-4 rounded-full bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-500 opacity-75 blur-lg transition duration-1000 group-hover:opacity-100 group-hover:duration-200" />
                <Avatar className="relative h-24 w-24 border-4 border-white/20 shadow-2xl ring-4 ring-blue-400/20 backdrop-blur-sm sm:h-32 sm:w-32 sm:border-8 lg:h-40 lg:w-40">
                  <AvatarImage
                    src={profile.avatar_url || ''}
                    alt={getFullName()}
                    className="object-cover"
                  />
                  <AvatarFallback className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 text-lg font-bold text-white sm:text-2xl lg:text-3xl">
                    {getInitials()}
                  </AvatarFallback>
                </Avatar>
                {/* Status indicator with pulse animation */}
                <div className="absolute bottom-1 right-1 h-6 w-6 rounded-full border-2 border-white bg-emerald-500 shadow-lg sm:bottom-2 sm:right-2 sm:h-8 sm:w-8 sm:border-4">
                  <div className="absolute inset-0 animate-ping rounded-full bg-emerald-500 opacity-75" />
                </div>
              </div>

              {/* Name and Info */}
              <div className="space-y-3 text-center text-white lg:text-left">
                <div className="flex flex-col gap-3 sm:items-center lg:flex-row lg:items-center">
                  <h1 className="text-2xl font-bold sm:text-3xl lg:text-4xl xl:text-5xl">
                    {getFullName()}
                  </h1>
                  {profile.is_pro && (
                    <Badge className="mx-auto w-fit bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 px-3 py-1.5 text-xs font-semibold text-white shadow-lg transition-shadow duration-300 hover:shadow-xl sm:px-4 sm:py-2 sm:text-sm lg:mx-0">
                      <Crown className="mr-1 h-3 w-3 sm:mr-2 sm:h-4 sm:w-4" />
                      PRO MEMBER
                    </Badge>
                  )}
                </div>

                <div className="flex flex-col gap-3 text-white/90 sm:gap-4 lg:flex-row lg:items-center">
                  <div className="flex items-center justify-center gap-2 lg:justify-start">
                    <Mail className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span className="text-sm font-medium sm:text-base">
                      {authUser?.email}
                    </span>
                    {authUser?.email_confirmed_at ? (
                      <BadgeCheck className="h-4 w-4 text-emerald-300 sm:h-5 sm:w-5" />
                    ) : (
                      <X className="h-4 w-4 text-red-300 sm:h-5 sm:w-5" />
                    )}
                  </div>
                  {profile.username && (
                    <div className="flex items-center justify-center gap-2 lg:justify-start">
                      <User className="h-4 w-4 sm:h-5 sm:w-5" />
                      <span className="text-sm font-medium sm:text-base">
                        @{profile.username}
                      </span>
                    </div>
                  )}
                </div>

                {!authUser?.email_confirmed_at && (
                  <Button
                    variant="secondary"
                    className="mt-3 border-white/30 bg-white/20 text-white backdrop-blur-sm hover:bg-white/30"
                    onClick={() => resendVerificationEmail.mutate()}
                    disabled={resendVerificationEmail.isPending}
                  >
                    {resendVerificationEmail.isPending
                      ? 'Sending...'
                      : 'Verify your email'}
                  </Button>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-3 sm:flex-row sm:justify-center lg:justify-start">
              {!isEditing && (
                <Button
                  onClick={onEditToggle}
                  className="border-white/30 bg-white/20 px-4 py-2.5 text-sm font-semibold text-white backdrop-blur-sm hover:bg-white/30 sm:px-6 sm:py-3 sm:text-base"
                  variant="outline"
                >
                  <Edit className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                  Edit Profile
                </Button>
              )}
              <LinkItem href="/profile/wishlist" className="w-full sm:w-auto">
                <Button className="w-full bg-gradient-to-r from-pink-600 to-rose-600 px-4 py-2.5 text-sm font-semibold text-white shadow-lg transition-all duration-300 hover:from-pink-500 hover:to-rose-500 hover:shadow-xl sm:px-6 sm:py-3 sm:text-base">
                  <Heart className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="hidden sm:inline">
                    View Liked Components
                  </span>
                  <span className="sm:hidden">Liked Components</span>
                </Button>
              </LinkItem>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
