'use client'

import { Crown, Heart, LucideIcon, Package, User } from 'lucide-react'

import { Card, CardContent } from '@/components/ui/card'
import { Profile } from '@/types/custom'

interface StatCardProps {
  title: string
  value: string | number
  description: string
  icon: LucideIcon
  profile: Profile
  color: 'rose' | 'purple' | 'amber'
  progressPercentage?: number
}

interface ProfileStatsProps {
  profile: Profile
}

function StatCard({
  title,
  value,
  description,
  icon: Icon,
  profile,
  color,
  progressPercentage = 0,
}: StatCardProps) {
  const colorClasses = {
    rose: {
      border: 'border-rose-300 dark:border-rose-700',
      bgGradient:
        'bg-gradient-to-br from-rose-50 to-pink-50 dark:from-rose-950 dark:to-pink-950',
      iconColor: 'text-rose-500',
      iconBgGradient:
        'bg-gradient-to-br from-rose-500 to-pink-600 dark:from-rose-700 dark:to-pink-800',
      valueColor: 'text-rose-800 dark:text-rose-300',
      labelColor: 'text-rose-600 dark:text-rose-400',
      progressBarBg: 'bg-rose-200 dark:bg-rose-800',
      progressBarFill: 'bg-gradient-to-r from-rose-500 to-pink-500',
      hoverShadow:
        'hover:shadow-lg dark:hover:shadow-xl dark:hover:shadow-rose-900/20',
    },
    purple: {
      border: 'border-purple-300 dark:border-purple-700',
      bgGradient:
        'bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-950 dark:to-indigo-950',
      iconColor: 'text-purple-500',
      iconBgGradient:
        'bg-gradient-to-br from-purple-500 to-indigo-600 dark:from-purple-700 dark:to-indigo-800',
      valueColor: 'text-purple-800 dark:text-purple-300',
      labelColor: 'text-purple-600 dark:text-purple-400',
      progressBarBg: 'bg-purple-200 dark:bg-purple-800',
      progressBarFill: 'bg-gradient-to-r from-purple-500 to-indigo-500',
      hoverShadow:
        'hover:shadow-lg dark:hover:shadow-xl dark:hover:shadow-purple-900/20',
    },
    amber: {
      border: 'border-amber-300 dark:border-amber-700',
      bgGradient:
        'bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950 dark:to-orange-950',
      iconColor: 'text-amber-500',
      iconBgGradient:
        'bg-gradient-to-br from-amber-500 to-orange-600 dark:from-amber-700 dark:to-orange-800',
      valueColor: 'text-amber-800 dark:text-amber-300',
      labelColor: 'text-amber-600 dark:text-amber-400',
      progressBarBg: 'bg-amber-200 dark:bg-amber-800',
      progressBarFill: 'bg-gradient-to-r from-amber-500 to-orange-500',
      hoverShadow:
        'hover:shadow-lg dark:hover:shadow-xl dark:hover:shadow-amber-900/20',
    },
  }[color]

  return (
    <Card
      className={`group relative overflow-hidden transition-all duration-300 ${colorClasses.border} ${colorClasses.bgGradient} ${colorClasses.hoverShadow}`}
    >
      <div
        className={`absolute right-4 top-4 opacity-10 transition-opacity duration-300 group-hover:opacity-20 ${colorClasses.iconColor}`}
      >
        <Icon className="h-16 w-16 sm:h-20 sm:w-20" />
      </div>
      <CardContent className="relative p-6 lg:p-8">
        <div className="mb-6 flex items-center justify-between">
          <div
            className={`inline-flex h-14 w-14 items-center justify-center rounded-xl text-white shadow-md transition-transform duration-300 group-hover:scale-105 sm:h-16 sm:w-16 ${colorClasses.iconBgGradient}`}
          >
            <Icon className="h-7 w-7 sm:h-8 sm:w-8" />
          </div>
          <div className="text-right">
            <div
              className={`text-3xl font-bold transition-transform duration-300 group-hover:scale-105 ${colorClasses.valueColor}`}
            >
              {value}
            </div>
            <div className={`text-sm font-medium ${colorClasses.labelColor}`}>
              {title}
            </div>
          </div>
        </div>
        <div className="space-y-2">
          <div className="text-sm text-muted-foreground">{description}</div>
          {progressPercentage > 0 && (
            <div
              className={`h-2 w-full rounded-full ${colorClasses.progressBarBg}`}
            >
              <div
                className={`h-2 rounded-full transition-all duration-500 ease-out ${colorClasses.progressBarFill}`}
                style={{
                  width: `${Math.min(progressPercentage, 100)}%`,
                }}
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export function ProfileStats({ profile }: ProfileStatsProps) {
  const likedCount = profile.liked_components?.length || 0
  const createdCount = profile.submitted_components_count || 0

  return (
    <div className="grid grid-cols-1 gap-6 sm:gap-8 md:grid-cols-3 lg:gap-10">
      <StatCard
        title="Liked Components"
        value={likedCount}
        description="Components you've saved for inspiration"
        icon={Heart}
        profile={profile}
        color="rose"
        progressPercentage={(likedCount / 10) * 100} // Example progress based on 10
      />
      <StatCard
        title="Components Created"
        value={createdCount}
        description="Your contributions to the community"
        icon={Package}
        profile={profile}
        color="purple"
        progressPercentage={(createdCount / 5) * 100} // Example progress based on 5
      />
      <StatCard
        title="Account Plan"
        value={profile.is_pro ? 'PRO' : 'FREE'}
        description={
          profile.is_pro
            ? 'Premium features unlocked'
            : 'Upgrade for more features'
        }
        icon={profile.is_pro ? Crown : User}
        profile={profile}
        color="amber"
        progressPercentage={profile.is_pro ? 100 : 25} // Fixed progress for plan
      />
    </div>
  )
}
