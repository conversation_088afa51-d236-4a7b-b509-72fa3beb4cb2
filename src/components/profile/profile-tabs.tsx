'use client'

import { <PERSON><PERSON><PERSON>, Shield, User } from 'lucide-react'

import { InfoCard } from '@/components/cards/info-card'
import { ProfileUpdateForm } from '@/components/forms/profile-update-form'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { GlowingEffect } from '@/components/ui/glowing-effect'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Profile } from '@/types/custom'

import { SecuritySettings } from './security-settings'

interface ProfileTabsProps {
  profile: Profile
  isEditing: boolean
  onEditToggle: () => void
  onEditSuccess: () => void
}

export function ProfileTabs({
  profile,
  isEditing,
  onEditToggle,
  onEditSuccess,
}: ProfileTabsProps) {
  return (
    <Tabs defaultValue="general" className="space-y-6 sm:space-y-8">
      <div className="flex justify-center lg:justify-start">
        <TabsList className="relative inline-flex h-12 w-full max-w-md items-center justify-center rounded-2xl border border-primary/20 bg-background/80 p-1 text-muted-foreground shadow-lg backdrop-blur-sm dark:border-primary/30 dark:bg-background/60 sm:w-auto sm:rounded-2xl lg:max-w-none">
          <GlowingEffect
            spread={50}
            glow={true}
            disabled={false}
            proximity={80}
            inactiveZone={0.01}
          />
          <TabsTrigger
            value="general"
            className="inline-flex flex-1 items-center justify-center whitespace-nowrap rounded-xl px-4 py-2.5 text-sm font-medium ring-offset-background transition-all hover:bg-purple-100/60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-violet-500 data-[state=active]:text-white data-[state=active]:shadow-lg dark:hover:bg-purple-900/50 dark:data-[state=active]:from-purple-600 dark:data-[state=active]:to-violet-600 sm:flex-none sm:px-6"
          >
            <User className="mr-2 h-4 w-4" />
            <span>General</span>
          </TabsTrigger>

          <TabsTrigger
            value="security"
            className="inline-flex flex-1 items-center justify-center whitespace-nowrap rounded-xl px-4 py-2.5 text-sm font-medium ring-offset-background transition-all hover:bg-emerald-100/60 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-gradient-to-r data-[state=active]:from-emerald-500 data-[state=active]:to-teal-500 data-[state=active]:text-white data-[state=active]:shadow-lg dark:hover:bg-emerald-900/50 dark:data-[state=active]:from-emerald-600 dark:data-[state=active]:to-teal-600 sm:flex-none sm:px-6"
          >
            <Shield className="mr-2 h-4 w-4" />
            <span>Security</span>
          </TabsTrigger>
        </TabsList>
      </div>

      <TabsContent value="general">
        {isEditing ? (
          <div className="relative overflow-hidden rounded-2xl border border-primary/20 bg-background/50 p-4 backdrop-blur-sm dark:border-primary/30 dark:bg-background/30 sm:rounded-3xl sm:p-6 lg:p-8">
            <GlowingEffect
              spread={40}
              glow={true}
              disabled={false}
              proximity={64}
              inactiveZone={0.01}
            />
            <div className="relative">
              <ProfileUpdateForm onSuccess={onEditSuccess} />
            </div>
          </div>
        ) : (
          <Card className="relative overflow-hidden rounded-2xl border-primary/20 bg-background/80 backdrop-blur-sm dark:border-primary/30 dark:bg-background/60 sm:rounded-3xl">
            <GlowingEffect
              spread={40}
              glow={true}
              disabled={false}
              proximity={64}
              inactiveZone={0.01}
            />
            <div className="absolute right-4 top-4 z-10">
              <Button
                onClick={onEditToggle}
                variant="outline"
                className="backdrop-blur-sm"
              >
                <EditIcon className="mr-2 h-4 w-4 sm:h-5 sm:w-5" />
                Edit Profile
              </Button>
            </div>
            <CardContent className="space-y-6 p-4 sm:space-y-8 sm:p-6 lg:p-8">
              <div className="mb-6 text-center sm:mb-8">
                <h2 className="text-xl font-bold text-slate-900 dark:text-slate-100 sm:text-2xl">
                  Profile Information
                </h2>
                <p className="mt-2 text-sm text-muted-foreground sm:text-base">
                  Your personal details and account information
                </p>
              </div>

              <div className="grid gap-4 sm:gap-6 md:grid-cols-2">
                <InfoCard
                  title="Username"
                  value={profile.username}
                  icon={User}
                  color="primary"
                />
                <InfoCard
                  title="Email"
                  value={profile.email}
                  icon={User}
                  color="secondary"
                />
                <InfoCard
                  title="First Name"
                  value={profile.first_name}
                  icon={User}
                  color="accent"
                />
                <InfoCard
                  title="Last Name"
                  value={profile.last_name}
                  icon={User}
                  color="muted"
                />
              </div>
            </CardContent>
          </Card>
        )}
      </TabsContent>

      <TabsContent value="security">
        <Card className="relative overflow-hidden rounded-2xl border-primary/20 bg-background/80 backdrop-blur-sm dark:border-primary/30 dark:bg-background/60 sm:rounded-3xl">
          <GlowingEffect
            spread={40}
            glow={true}
            disabled={false}
            proximity={64}
            inactiveZone={0.01}
          />
          <CardContent className="relative grid gap-6 p-4 sm:gap-8 sm:p-6 lg:grid-cols-2 lg:p-8">
            <div className="col-span-2 space-y-6 sm:space-y-8">
              <SecuritySettings profile={profile} />
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
