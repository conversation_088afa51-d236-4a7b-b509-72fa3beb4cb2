'use client'

import { useMutation } from '@tanstack/react-query'
import { Eye, EyeOff, Shield } from 'lucide-react'
import { useState } from 'react'
import { toast } from 'sonner'
import * as z from 'zod'

import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Profile } from '@/types/custom'
import { createClient } from '@/utils/supabase/client'

// Password validation schema
const passwordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z.string().min(8, {
    message: 'Password must be at least 8 characters.',
  }),
  // newPassword: z
  //   .string()
  //   .min(8, 'New password must be at least 8 characters long')
  //   .refine((password) => /[A-Z]/.test(password), 'Password must contain at least one uppercase letter')
  //   .refine((password) => /[a-z]/.test(password), 'Password must contain at least one lowercase letter')
  //   .refine((password) => /[0-9]/.test(password), 'Password must contain at least one number')
  //   .refine((password) => /[^A-Za-z0-9]/.test(password), 'Password must contain at least one special character'),
})

type PasswordFormValues = z.infer<typeof passwordSchema>

export function SecuritySettings({ profile }: { profile: Profile }) {
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const supabase = createClient()

  const validatePasswords = (): { success: boolean; error?: string } => {
    try {
      passwordSchema.parse({
        currentPassword,
        newPassword,
      })

      // Additional validation to ensure new password is different
      if (currentPassword === newPassword) {
        return {
          success: false,
          error: 'New password must be different from current password',
        }
      }

      return { success: true }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          error: error.errors[0].message,
        }
      }
      return {
        success: false,
        error: 'Invalid password format',
      }
    }
  }

  const updatePassword = useMutation({
    mutationFn: async () => {
      // Validate passwords using Zod
      const validation = validatePasswords()
      if (!validation.success) {
        throw new Error(validation.error)
      }

      if (!profile.email) {
        toast.error('Email not found')
        return
      }

      // First verify the current password
      const {
        data: { user },
        error: signInError,
      } = await supabase.auth.signInWithPassword({
        email: profile.email,
        password: currentPassword,
      })

      if (signInError) {
        throw new Error('Current password is incorrect')
      }

      // If current password is correct, update to new password
      const { error: updateError } = await supabase.auth.updateUser({
        password: newPassword,
      })

      if (updateError) throw updateError
    },
    onSuccess: () => {
      toast.success('Password updated successfully')
      setCurrentPassword('')
      setNewPassword('')
    },
    onError: (error) => {
      toast.error(
        error instanceof Error ? error.message : 'Failed to update password'
      )
    },
  })

  return (
    <Card className="space-y-6 p-6">
      <div className="flex items-center gap-2">
        <Shield className="h-5 w-5 text-primary" />
        <h2 className="text-xl font-semibold">Security Settings</h2>
      </div>

      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium">Current Password</label>
          <div className="relative">
            <Input
              type={showCurrentPassword ? 'text' : 'password'}
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              className="mt-1 pr-10"
            />
            <button
              type="button"
              onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
            >
              {showCurrentPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
        </div>

        <div>
          <label className="text-sm font-medium">New Password</label>
          <div className="relative">
            <Input
              type={showNewPassword ? 'text' : 'password'}
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className="mt-1 pr-10"
            />
            <button
              type="button"
              onClick={() => setShowNewPassword(!showNewPassword)}
              className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
            >
              {showNewPassword ? <EyeOff size={20} /> : <Eye size={20} />}
            </button>
          </div>
          <p className="mt-2 text-sm text-muted-foreground">
            Password must be at least 8 characters long and contain uppercase,
            lowercase, number, and special character.
          </p>
        </div>

        <Button
          onClick={() => updatePassword.mutate()}
          disabled={
            !currentPassword || !newPassword || updatePassword.isPending
          }
          className="w-full"
        >
          {updatePassword.isPending ? 'Updating...' : 'Update Password'}
        </Button>
      </div>

      {/* <div className="mt-6 rounded-lg bg-muted p-4">
        <div className="flex items-center gap-2">
          <KeyRound className="h-5 w-5 text-muted-foreground" />
          <h3 className="font-medium">Two-Factor Authentication</h3>
        </div>
        <p className="mt-2 text-sm text-muted-foreground">
          Add an extra layer of security to your account by enabling two-factor
          authentication.
        </p>
        <Button variant="outline" className="mt-4">
          Enable 2FA
        </Button>
      </div> */}
    </Card>
  )
}
