'use client'

import { <PERSON>ert<PERSON>ir<PERSON>, LoaderIcon } from 'lucide-react'
import { ReactNode } from 'react'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { useProfileStore } from '@/stores/profile-store'
import { Profile } from '@/types/custom'

interface ProfileWrapperProps {
  children: (profile: Profile) => ReactNode
  loadingMessage?: string
  loadingDescription?: string
}

export function ProfileWrapper({
  children,
  loadingMessage = 'Loading your profile',
  loadingDescription = 'Preparing your personalized experience...',
}: ProfileWrapperProps) {
  const { profile, isLoading, error } = useProfileStore()

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="relative">
            <div className="absolute inset-0 animate-ping rounded-full bg-primary/20 blur-xl" />
            <LoaderIcon className="relative mx-auto h-12 w-12 animate-spin text-primary" />
          </div>
          <div className="mt-6 space-y-2">
            <h3 className="text-lg font-semibold text-foreground">
              {loadingMessage}
            </h3>
            <p className="text-sm text-muted-foreground">
              {loadingDescription}
            </p>
          </div>
          <div className="mt-4 flex justify-center space-x-1">
            <div className="h-2 w-2 animate-bounce rounded-full bg-primary [animation-delay:-0.3s]" />
            <div className="h-2 w-2 animate-bounce rounded-full bg-primary [animation-delay:-0.15s]" />
            <div className="h-2 w-2 animate-bounce rounded-full bg-primary" />
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen">
        <div className="container mx-auto max-w-2xl py-20">
          <Alert
            variant="destructive"
            className="border-red-300 bg-red-100/50 backdrop-blur-sm dark:border-red-700 dark:bg-red-900/50"
          >
            <AlertCircle className="h-5 w-5" />
            <AlertDescription className="text-base">
              Failed to load profile. Please try refreshing the page.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="min-h-screen">
        <div className="container mx-auto max-w-2xl py-20">
          <Alert
            variant="destructive"
            className="border-yellow-300 bg-yellow-100/50 backdrop-blur-sm dark:border-yellow-700 dark:bg-yellow-900/50"
          >
            <AlertCircle className="h-5 w-5" />
            <AlertDescription className="text-base">
              Profile not found. Please try logging in again.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    )
  }

  return <>{children(profile)}</>
}
