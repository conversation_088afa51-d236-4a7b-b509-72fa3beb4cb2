'use client'

import { motion } from 'framer-motion'
import { Heart, Search, Sparkles } from 'lucide-react'

import LinkItem from '@/components/LInkItem/LinkItem'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export function LikedPageEmptyState() {
  return (
    <div className="flex min-h-[40vh] items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="max-w-lg text-center"
      >
        <Card className="border bg-white p-8 shadow-sm dark:bg-slate-900">
          {/* Illustration */}
          <div className="mb-6 flex justify-center">
            <div className="relative">
              {/* Main Heart */}
              <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700">
                <Heart
                  className="h-8 w-8 text-muted-foreground"
                  strokeWidth={1.5}
                />
              </div>

              {/* Floating Elements */}
              <motion.div
                animate={{
                  y: [0, -8, 0],
                  rotate: [0, 5, 0],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: 'easeInOut',
                }}
                className="absolute -right-1 -top-1 flex h-6 w-6 items-center justify-center rounded-full bg-primary/20"
              >
                <Sparkles className="h-3 w-3 text-primary" />
              </motion.div>
            </div>
          </div>

          {/* Content */}
          <h2 className="mb-4 text-2xl font-bold text-foreground">
            Start Your Collection
          </h2>
          <p className="mb-6 text-muted-foreground">
            You haven&apos;t liked any components yet. Discover amazing UI
            components and save your favorites to build faster.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
            <LinkItem href="/components">
              <Button
                size="lg"
                className="w-full bg-primary hover:bg-primary/90 sm:w-auto"
              >
                <Search className="mr-2 h-4 w-4" />
                Explore Components
              </Button>
            </LinkItem>
            <LinkItem href="/categories">
              <Button size="lg" variant="outline" className="w-full sm:w-auto">
                <Sparkles className="mr-2 h-4 w-4" />
                Browse Categories
              </Button>
            </LinkItem>
          </div>

          {/* Tips */}
          <div className="mt-6 rounded-lg bg-slate-50 p-4 dark:bg-slate-800">
            <h4 className="mb-2 text-sm font-semibold text-foreground">
              Quick tip:
            </h4>
            <p className="text-sm text-muted-foreground">
              Click the heart icon on any component to add it to your collection
            </p>
          </div>
        </Card>
      </motion.div>
    </div>
  )
}
