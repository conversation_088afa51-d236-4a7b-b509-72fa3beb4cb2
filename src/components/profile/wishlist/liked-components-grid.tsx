'use client'
/* eslint-disable @typescript-eslint/no-unused-vars */

import { motion } from 'framer-motion'

import { LikedComponentCard } from '@/components/cards/liked-component-card'
import { ComponentWithCategory } from '@/types/custom'

interface LikedComponentsGridProps {
  components: ComponentWithCategory[]
  // eslint-disable-next-line @typescript-eslint/ban-types
  onRemove: (componentId: string) => void
  removingComponents: Set<string>
  isLoading?: boolean
}

export function LikedComponentsGrid({
  components,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onRemove,
  removingComponents,
  isLoading = false,
}: LikedComponentsGridProps) {
  if (isLoading) {
    return (
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {Array.from({ length: 8 }).map((_, index) => (
          <div
            key={index}
            className="aspect-[4/5] animate-pulse rounded-xl bg-muted"
          />
        ))}
      </div>
    )
  }

  if (components.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="col-span-full flex flex-col items-center justify-center py-16 text-center"
      >
        <div className="mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-muted">
          <svg
            className="h-10 w-10 text-muted-foreground"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"
            />
          </svg>
        </div>
        <h3 className="mb-2 text-lg font-semibold text-foreground">
          No components found
        </h3>
        <p className="text-muted-foreground">
          Start liking components to see them here.
        </p>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Grid */}
      <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3 ">
        {components.map((component, index) => (
          <motion.div
            key={component.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
          >
            <LikedComponentCard
              component={component}
              onRemove={onRemove}
              isRemoving={removingComponents.has(component.id)}
            />
          </motion.div>
        ))}
      </div>
    </motion.div>
  )
}
