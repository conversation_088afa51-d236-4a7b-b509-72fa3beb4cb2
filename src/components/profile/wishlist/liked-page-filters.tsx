'use client'

import { motion } from 'framer-motion'
import {
  Filter,
  Grid3X3,
  LayoutGrid,
  List,
  Search,
  SortAsc,
} from 'lucide-react'
import { useState } from 'react'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'

interface LikedPageFiltersProps {
  componentsCount: number
  onSearch: (query: string) => void
  onSort: (sort: string) => void
  onFilter: (filter: string) => void
  searchQuery: string
  currentSort: string
  currentFilter: string
}

export function LikedPageFilters({
  componentsCount,
  onSearch,
  onSort,
  onFilter,
  searchQuery,
  currentSort,
  currentFilter,
}: LikedPageFiltersProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mb-8"
    >
      <Card className="border-0 bg-white/50 p-6 shadow-lg backdrop-blur-sm dark:bg-slate-900/50">
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          {/* Left Section - Stats & Search */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            {/* Stats */}
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 rounded-lg bg-primary/10 px-3 py-2">
                <LayoutGrid className="h-4 w-4 text-primary" />
                <span className="font-semibold text-foreground">
                  {componentsCount}
                </span>
                <span className="text-sm text-muted-foreground">
                  Components
                </span>
              </div>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
              >
                All Saved
              </Badge>
            </div>

            <Separator orientation="vertical" className="hidden h-8 sm:block" />

            {/* Search */}
            <div className="relative min-w-0 flex-1 sm:max-w-xs">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search components..."
                value={searchQuery}
                onChange={(e) => onSearch(e.target.value)}
                className="border-0 bg-white/70 pl-10 focus:bg-white dark:bg-slate-800/70 dark:focus:bg-slate-800"
              />
            </div>
          </div>

          {/* Right Section - Filters & Actions */}
          <div className="flex flex-wrap items-center gap-3">
            {/* Sort */}
            <Select value={currentSort} onValueChange={onSort}>
              <SelectTrigger className="w-[140px] border-0 bg-white/70 dark:bg-slate-800/70">
                <SortAsc className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="name">Name A-Z</SelectItem>
                <SelectItem value="category">Category</SelectItem>
              </SelectContent>
            </Select>

            {/* Filter */}
            <Select value={currentFilter} onValueChange={onFilter}>
              <SelectTrigger className="w-[120px] border-0 bg-white/70 dark:bg-slate-800/70">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="buttons">Buttons</SelectItem>
                <SelectItem value="cards">Cards</SelectItem>
                <SelectItem value="forms">Forms</SelectItem>
                <SelectItem value="navigation">Navigation</SelectItem>
                <SelectItem value="layout">Layout</SelectItem>
              </SelectContent>
            </Select>

            <Separator orientation="vertical" className="h-8" />

            {/* View Mode Toggle */}
            <div className="flex items-center rounded-lg bg-white/70 p-1 dark:bg-slate-800/70">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="h-8 w-8 p-0"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="h-8 w-8 p-0"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(searchQuery ||
          currentFilter !== 'all' ||
          currentSort !== 'newest') && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-4 border-t border-border/50 pt-4"
          >
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Active filters:
              </span>
              {searchQuery && (
                <Badge variant="outline" className="gap-1">
                  Search: &ldquo;{searchQuery}&rdquo;
                  <button
                    onClick={() => onSearch('')}
                    className="ml-1 hover:text-destructive"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {currentFilter !== 'all' && (
                <Badge variant="outline" className="gap-1">
                  Type: {currentFilter}
                  <button
                    onClick={() => onFilter('all')}
                    className="ml-1 hover:text-destructive"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {currentSort !== 'newest' && (
                <Badge variant="outline" className="gap-1">
                  Sort: {currentSort}
                  <button
                    onClick={() => onSort('newest')}
                    className="ml-1 hover:text-destructive"
                  >
                    ×
                  </button>
                </Badge>
              )}
            </div>
          </motion.div>
        )}
      </Card>
    </motion.div>
  )
}
