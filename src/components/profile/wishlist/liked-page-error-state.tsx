'use client'

import { motion } from 'framer-motion'
import { AlertCircle, RefreshCw } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

interface LikedPageErrorStateProps {
  error: string
  onRetry?: () => void
}

export function LikedPageErrorState({
  error,
  onRetry,
}: LikedPageErrorStateProps) {
  return (
    <div className="flex min-h-[60vh] items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="max-w-md bg-card p-8 text-center shadow-lg">
          {/* Error Icon */}
          <div className="mb-6 flex justify-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
              <AlertCircle className="h-8 w-8 text-destructive" />
            </div>
          </div>

          {/* Error Content */}
          <h3 className="mb-3 text-xl font-semibold text-foreground">
            Something went wrong
          </h3>
          <p className="mb-6 text-muted-foreground">
            {error ||
              'We encountered an error while loading your liked components.'}
          </p>

          {/* Action Button */}
          {onRetry && (
            <Button onClick={onRetry} className="w-full">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
        </Card>
      </motion.div>
    </div>
  )
}
