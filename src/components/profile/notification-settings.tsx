// 'use client'

// import { useMutation } from '@tanstack/react-query'
// import { Bell } from 'lucide-react'
// import { toast } from 'sonner'

// import { Card } from '@/components/ui/card'
// import { Label } from '@/components/ui/label'
// import { Switch } from '@/components/ui/switch'
// import { Profile } from '@/types/custom'
// import { createClient } from '@/utils/supabase/client'

// export function NotificationSettings({ profile }: { profile: Profile }) {
//   const supabase = createClient()

//   const updateNotifications = useMutation({
//     mutationFn: async (settings: any) => {
//       const { error } = await supabase
//         .from('profiles')
//         .update({
//           notification_settings: settings,
//           updated_at: new Date().toISOString(),
//         })
//         .eq('id', profile.id)

//       if (error) throw error
//     },
//     onSuccess: () => {
//       toast.success('Notification settings updated')
//     },
//     onError: () => {
//       toast.error('Failed to update notification settings')
//     },
//   })

//   return (
//     <Card className="space-y-6 p-6">
//       <div className="flex items-center gap-2">
//         <Bell className="h-5 w-5 text-primary" />
//         <h2 className="text-xl font-semibold">Notification Preferences</h2>
//       </div>

//       <div className="space-y-4">
//         <div className="flex items-center justify-between">
//           <div className="space-y-0.5">
//             <Label>Email Notifications</Label>
//             <p className="text-sm text-muted-foreground">
//               Receive email updates about your account activity
//             </p>
//           </div>
//           <Switch
//             checked={profile?.notification_settings?.email ?? true}
//             onCheckedChange={(checked) =>
//               updateNotifications.mutate({
//                 ...profile?.notification_settings,
//                 email: checked,
//               })
//             }
//           />
//         </div>

//         <div className="flex items-center justify-between">
//           <div className="space-y-0.5">
//             <Label>Marketing Emails</Label>
//             <p className="text-sm text-muted-foreground">
//               Receive emails about new features and updates
//             </p>
//           </div>
//           <Switch
//             checked={profile.notification_settings?.marketing ?? false}
//             onCheckedChange={(checked) =>
//               updateNotifications.mutate({
//                 ...profile.notification_settings,
//                 marketing: checked,
//               })
//             }
//           />
//         </div>

//         <div className="flex items-center justify-between">
//           <div className="space-y-0.5">
//             <Label>Security Alerts</Label>
//             <p className="text-sm text-muted-foreground">
//               Get notified about important security updates
//             </p>
//           </div>
//           <Switch
//             checked={profile.notification_settings?.security ?? true}
//             onCheckedChange={(checked) =>
//               updateNotifications.mutate({
//                 ...profile.notification_settings,
//                 security: checked,
//               })
//             }
//           />
//         </div>
//       </div>
//     </Card>
//   )
// }

export const NotificationSetting = () => {
  return <div>NotificationSetting</div>
}
