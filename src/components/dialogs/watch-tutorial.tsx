'use client'

import { PlayCircle } from 'lucide-react'

import { But<PERSON> } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'

export function WatchTutorialDialog() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="gradient-primary" size="sm" className="gap-2">
          <PlayCircle className="h-4 w-4" />
          Watch Tutorial
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>Quick Tutorial</DialogTitle>
          <DialogDescription>
            Learn how to use our components effectively
          </DialogDescription>
        </DialogHeader>
        <div className="aspect-video w-full">
          <iframe
            width="100%"
            height="100%"
            src="https://www.youtube.com/embed/UVETZW78Uq4"
            title="Tutorial Video"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="rounded-lg"
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}
