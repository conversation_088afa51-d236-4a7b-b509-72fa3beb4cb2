'use client'

import { ReloadIcon } from '@radix-ui/react-icons'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'

import { revalidatePathAction } from '@/actions/revalidate'
import { fetchCategories } from '@/actions/server-categories'
import { Breadcrumbs } from '@/components/breadcrumbs'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Revalidate', link: '/dashboard/revalidate' },
]

const commonPaths = [
  { path: '/', label: 'Home Page' },
  { path: '/dashboard', label: 'Dashboard' },
  { path: '/components', label: 'Components Page' },
  { path: '/templates', label: 'Templates Page' },
  { path: '/pricing', label: 'Pricing Page' },
]

type Category = {
  id: string | number
  slug?: string
  name?: string
}

type RevalidationHistoryItem = {
  path: string
  timestamp: string
  status: 'success' | 'error'
  message: string
}

type BulkLoadingType = 'components' | 'templates' | null

export default function RevalidatePage() {
  const [path, setPath] = useState<string>('')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [revalidationHistory, setRevalidationHistory] = useState<
    RevalidationHistoryItem[]
  >([])
  const [componentCategories, setComponentCategories] = useState<Category[]>([])
  const [templateCategories, setTemplateCategories] = useState<Category[]>([])
  const [catLoading, setCatLoading] = useState<boolean>(false)
  const [bulkLoading, setBulkLoading] = useState<BulkLoadingType>(null)
  const router = useRouter()

  useEffect(() => {
    const fetchCats = async () => {
      setCatLoading(true)
      const [components, templates] = await Promise.all([
        fetchCategories(false), // component-only
        fetchCategories(true), // template-only
      ])
      setComponentCategories(components)
      setTemplateCategories(templates)
      setCatLoading(false)
    }
    fetchCats()
  }, [])

  const handleRevalidate = async (pathToRevalidate: string): Promise<void> => {
    setIsLoading(true)
    try {
      const result = await revalidatePathAction(pathToRevalidate)

      // Add to history
      setRevalidationHistory((prev) => [
        {
          path: pathToRevalidate,
          timestamp: new Date().toLocaleString(),
          status: result.success ? 'success' : 'error',
          message: result.message,
        },
        ...prev,
      ])

      if (result.success) {
        toast.success(result.message)
        router.refresh() // Refresh the current page
      } else {
        toast.error(result.message)
      }
    } catch (error) {
      toast.error('Failed to revalidate path')
    } finally {
      setIsLoading(false)
      setPath('') // Clear input after submission
    }
  }

  // Helper to get slug (or fallback to id)
  const getSlug = (cat: Category) => cat.slug || cat.id || ''

  // Bulk revalidate handlers
  const handleRevalidateAllComponents = async (): Promise<void> => {
    setBulkLoading('components')
    try {
      const results: RevalidationHistoryItem[] = await Promise.all(
        componentCategories.map((cat) =>
          revalidatePathAction(`/components/category/${getSlug(cat)}`).then(
            (result): RevalidationHistoryItem => ({
              path: `/components/category/${getSlug(cat)}`,
              timestamp: new Date().toLocaleString(),
              status: result.success ? 'success' : 'error',
              message: result.message,
            })
          )
        )
      )
      setRevalidationHistory((prev) => [...results, ...prev])
      toast.success('All component categories revalidated!')
    } catch (error) {
      toast.error('Failed to revalidate all component categories')
    } finally {
      setBulkLoading(null)
    }
  }

  const handleRevalidateAllTemplates = async (): Promise<void> => {
    setBulkLoading('templates')
    try {
      const results: RevalidationHistoryItem[] = await Promise.all(
        templateCategories.map((cat) =>
          revalidatePathAction(`/templates/category/${getSlug(cat)}`).then(
            (result): RevalidationHistoryItem => ({
              path: `/templates/category/${getSlug(cat)}`,
              timestamp: new Date().toLocaleString(),
              status: result.success ? 'success' : 'error',
              message: result.message,
            })
          )
        )
      )
      setRevalidationHistory((prev) => [...results, ...prev])
      toast.success('All template categories revalidated!')
    } catch (error) {
      toast.error('Failed to revalidate all template categories')
    } finally {
      setBulkLoading(null)
    }
  }

  return (
    <ScrollArea className="h-full">
      <div className="flex-1 space-y-4 p-8">
        <Breadcrumbs items={breadcrumbItems} />

        <div className="grid gap-4">
          {/* Revalidation Form */}
          <Card>
            <CardHeader>
              <CardTitle>Revalidate Path</CardTitle>
              <CardDescription>
                Enter a path to revalidate its cache. The path should start with
                a forward slash (/).
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <div className="flex-1">
                  <Label htmlFor="path">Path</Label>
                  <Input
                    id="path"
                    placeholder="/your/path"
                    value={path}
                    onChange={(e) => setPath(e.target.value)}
                  />
                </div>
                <Button
                  className="mt-auto"
                  onClick={() => handleRevalidate(path)}
                  disabled={!path || isLoading}
                >
                  {isLoading && (
                    <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Revalidate
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Common Paths */}
          <Card>
            <CardHeader>
              <CardTitle>Common Paths</CardTitle>
              <CardDescription>
                Quick access to frequently revalidated paths
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {commonPaths.map((item) => (
                  <Button
                    key={item.path}
                    variant="outline"
                    onClick={() => handleRevalidate(item.path)}
                    disabled={isLoading}
                  >
                    {item.label}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Component Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Component Categories</CardTitle>
              <CardDescription>
                Revalidate all component category pages
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-2">
                <Button
                  onClick={handleRevalidateAllComponents}
                  disabled={
                    catLoading ||
                    componentCategories.length === 0 ||
                    bulkLoading === 'components'
                  }
                  variant="default"
                >
                  {bulkLoading === 'components' ? (
                    <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  Revalidate All Component Categories
                </Button>
              </div>
              {catLoading ? (
                <div>Loading categories...</div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {componentCategories.map((cat) => (
                    <Button
                      key={cat.id}
                      variant="outline"
                      onClick={() =>
                        handleRevalidate(`/components/category/${getSlug(cat)}`)
                      }
                      disabled={isLoading || bulkLoading === 'components'}
                    >
                      {cat.name || getSlug(cat)}
                    </Button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Template Categories */}
          <Card>
            <CardHeader>
              <CardTitle>Template Categories</CardTitle>
              <CardDescription>
                Revalidate all template category pages
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-2">
                <Button
                  onClick={handleRevalidateAllTemplates}
                  disabled={
                    catLoading ||
                    templateCategories.length === 0 ||
                    bulkLoading === 'templates'
                  }
                  variant="default"
                >
                  {bulkLoading === 'templates' ? (
                    <ReloadIcon className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  Revalidate All Template Categories
                </Button>
              </div>
              {catLoading ? (
                <div>Loading categories...</div>
              ) : (
                <div className="flex flex-wrap gap-2">
                  {templateCategories.map((cat) => (
                    <Button
                      key={cat.id}
                      variant="outline"
                      onClick={() =>
                        handleRevalidate(`/templates/category/${getSlug(cat)}`)
                      }
                      disabled={isLoading || bulkLoading === 'templates'}
                    >
                      {cat.name || getSlug(cat)}
                    </Button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Revalidation History */}
          <Card>
            <CardHeader>
              <CardTitle>Revalidation History</CardTitle>
              <CardDescription>
                Recent path revalidation attempts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {revalidationHistory.length === 0 ? (
                  <p className="text-sm text-muted-foreground">
                    No revalidation history yet
                  </p>
                ) : (
                  revalidationHistory.map((item, index) => (
                    <div key={index}>
                      {index > 0 && <Separator className="my-2" />}
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{item.path}</p>
                          <p className="text-sm text-muted-foreground">
                            {item.timestamp}
                          </p>
                        </div>
                        <div
                          className={`text-sm ${
                            item.status === 'success'
                              ? 'text-green-500'
                              : 'text-red-500'
                          }`}
                        >
                          {item.status === 'success' ? '✓ Success' : '✗ Failed'}
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ScrollArea>
  )
}
