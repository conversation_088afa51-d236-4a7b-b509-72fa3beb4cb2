import { Plus } from 'lucide-react'
import Link from 'next/link'

import { ProtectedWrapper } from '@/components/auth/protected-wrapper'
import { Breadcrumbs } from '@/components/breadcrumbs'
// import { columns } from '@/components/tables/employee-tables/columns'
// import { EmployeeTable } from '@/components/tables/employee-tables/employee-table'
import { buttonVariants } from '@/components/ui/button'
import { Heading } from '@/components/ui/heading'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { Component } from '@/types/custom'
import { createClient } from '@/utils/supabase/server'
// import Link from 'next/link'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Employee', link: '/dashboard/employee' },
]

type paramsProps = {
  searchParams: {
    [key: string]: string | string[] | undefined
  }
}

export default async function page({ searchParams }: paramsProps) {
  const page = Number(searchParams.page) || 1
  const pageLimit = Number(searchParams.limit) || 10
  const offset = (page - 1) * pageLimit
  const supabase = createClient()

  // Fetch components from Supabase with pagination
  const {
    data: components,
    count,
    error,
  } = (await supabase
    .from('components')
    .select('id, name, description, image_url, tags, meta_desc, is_pro, slug', {
      count: 'exact',
    }) // Excluding code field
    .range(offset, offset + pageLimit - 1)) as {
    data: Component[] | null
    count: number | null
    error: any
  } // Apply pagination

  // console.log(components)
  if (error) {
    console.error('Error fetching components:', error)
  }

  const pageCount = Math.ceil((count || 0) / pageLimit)

  return (
    <ProtectedWrapper allowedRoles={['admin']}>
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <Breadcrumbs items={breadcrumbItems} />

        <div className="flex items-start justify-between">
          <Heading
            title={`Components (${count})`}
            description="Manage components (Server-side table functionalities.)"
          />

          <Link
            prefetch={true}
            href={'/dashboard/employee/new'}
            className={cn(buttonVariants({ variant: 'default' }))}
          >
            <Plus className="mr-2 h-4 w-4" /> Add New
          </Link>
        </div>
        <Separator />

        {/* <EmployeeTable
          searchKey="name"
          pageNo={page}
          columns={columns}
          totalUsers={count || 0} // Use count from Supabase for total users
          data={components} // Pass fetched components
          pageCount={pageCount}
        /> */}
      </div>
    </ProtectedWrapper>
  )
}
