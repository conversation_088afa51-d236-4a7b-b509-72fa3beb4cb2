'use client'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'next/navigation'

import { getTemplate } from '@/actions/template'
import { Breadcrumbs } from '@/components/breadcrumbs'
import { TemplateForm } from '@/components/forms/template-form'
import { ScrollArea } from '@/components/ui/scroll-area'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Templates', link: '/dashboard/templates' },
  { title: 'Add New', link: '/dashboard/templates/new' },
]

export default function Page() {
  const params = useParams<{ templateId: string }>()

  const {
    isLoading,
    error,
    data: initialData,
  } = useQuery({
    queryKey: ['template', params.templateId],
    queryFn: () => getTemplate(params.templateId),
    enabled: params.templateId !== 'new', // Don't fetch data if it's a new component
  })

  // console.log(params, 'params')
  // console.log(initialData, 'initialData')

  // Handle loading state
  if (isLoading) {
    return <div>Loading...</div>
  }

  // Handle error state
  if (error) {
    return <div>Error: {(error as Error).message}</div> // Cast error to Error to access the message property
  }

  return (
    <ScrollArea className="h-full">
      <div className="flex-1 space-y-4 p-8">
        <Breadcrumbs items={breadcrumbItems} />
        <TemplateForm
          initialData={params.templateId === 'new' ? null : initialData}
          key={params.templateId} // Key to ensure the form rerenders when componentId changes
        />
      </div>
    </ScrollArea>
  )
}
