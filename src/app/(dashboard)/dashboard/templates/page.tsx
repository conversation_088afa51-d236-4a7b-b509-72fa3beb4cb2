import { Plus } from 'lucide-react'
import Link from 'next/link'

import { ProtectedWrapper } from '@/components/auth/protected-wrapper'
import { Breadcrumbs } from '@/components/breadcrumbs'
import { columns } from '@/components/tables/component-table/columns'
import { ComponentTable } from '@/components/tables/component-table/component-table'
import { buttonVariants } from '@/components/ui/button'
import { Heading } from '@/components/ui/heading'
import { Separator } from '@/components/ui/separator'
import { canCreate } from '@/lib/permissions'
import { cn } from '@/lib/utils'
import { Template } from '@/types/custom'
import { createClient } from '@/utils/supabase/server'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Templates', link: '/dashboard/templates' },
]

type paramsProps = {
  searchParams: {
    [key: string]: string | string[] | undefined
  }
}

export default async function page({ searchParams }: paramsProps) {
  const page = Number(searchParams.page) || 1
  const pageLimit = Number(searchParams.limit) || 10
  const offset = (page - 1) * pageLimit
  const supabase = createClient()

  // Fetch templates from Supabase with pagination
  const {
    data: templates,
    count,
    error,
  } = (await supabase
    .from('templates')
    .select('id, name, description, image_url, tags, meta_desc, is_pro, slug', {
      count: 'exact',
    })
    .order('created_at', { ascending: false }) // Excluding code field
    .range(offset, offset + pageLimit - 1)) as {
    data: Template[] | null
    count: number | null
    error: any
  } // Apply pagination

  if (error) {
    console.error('Error fetching templates:', error)
  }

  const pageCount = Math.ceil((count || 0) / pageLimit)

  // Get user role for permission check
  const {
    data: { user },
  } = await supabase.auth.getUser()
  let userRole = null

  if (user) {
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    userRole = profile?.role
  }

  return (
    <ProtectedWrapper allowedRoles={['admin', 'editor']}>
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <Breadcrumbs items={breadcrumbItems} />

        <div className="flex items-start justify-between">
          <Heading
            title={`Templates (${count})`}
            description="Manage templates (Server-side table functionalities.)"
          />

          {canCreate(userRole as any) && (
            <Link
              prefetch={true}
              href={'/dashboard/templates/new'}
              className={cn(buttonVariants({ variant: 'default' }))}
            >
              <Plus className="mr-2 h-4 w-4" /> Add New
            </Link>
          )}
        </div>
        <Separator />

        <ComponentTable
          searchKey="name"
          pageNo={page}
          columns={columns}
          totalComponents={count || 0} // Use count from Supabase for total users
          data={templates} // Pass fetched templates
          pageCount={pageCount}
        />
      </div>
    </ProtectedWrapper>
  )
}
