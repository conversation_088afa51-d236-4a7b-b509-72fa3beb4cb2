'use client'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'

import { fetchPaymentsWithPagination } from '@/actions/payments'
import { ProtectedWrapper } from '@/components/auth/protected-wrapper'
import { Breadcrumbs } from '@/components/breadcrumbs'
import { PaymentsTable } from '@/components/tables/payment-tables/payments-table'
import { Heading } from '@/components/ui/heading'
import { Separator } from '@/components/ui/separator'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Payments', link: '/dashboard/payments' },
]

export default function Page() {
  return (
    <ProtectedWrapper allowedRoles={['admin']}>
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <Breadcrumbs items={breadcrumbItems} />
        <div className="flex items-start justify-between">
          <Heading
            title="Payments"
            description="View all payment transactions"
          />
        </div>
        <Separator />
        <Suspense>
          <Payments />
        </Suspense>
      </div>
    </ProtectedWrapper>
  )
}

const Payments = () => {
  const searchParams = useSearchParams()
  const page = Number(searchParams.get('page')) || 1
  const pageLimit = Number(searchParams.get('limit')) || 10

  const { isLoading, error, data } = useQuery({
    queryKey: ['payments', page, pageLimit],
    queryFn: () => fetchPaymentsWithPagination(page, pageLimit),
  })

  const { payments, count } = data || {}

  const pageCount = Math.ceil((count || 0) / pageLimit)

  return (
    <PaymentsTable
      pageNo={page}
      totalCount={count ? count : 0}
      data={payments ? payments : []}
      pageCount={pageCount}
      isLoading={isLoading}
    />
  )
}
