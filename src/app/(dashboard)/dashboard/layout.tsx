import { redirect } from 'next/navigation'

import { allowedRoles } from '@/actions/users'
import { ProtectedWrapper } from '@/components/auth/protected-wrapper'
import { createClient } from '@/utils/supabase/server'

import DashboardSidebar from './dasbhoard-sidebar'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = createClient()

  // Check if user is authenticated
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()
  if (!user || userError) {
    redirect('/login')
  }

  // Check if user is admin or editor
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('role')
    .eq('id', user.id)
    .single()

  if (!profile || profileError || !allowedRoles.includes(profile.role)) {
    redirect('/')
  }

  return (
    <div className="relative h-full overflow-hidden bg-background">
      <DashboardSidebar />
      <main
        id="content"
        className={`h-full overflow-x-hidden pt-16 transition-[margin] md:ml-64 md:overflow-y-hidden md:pt-0`}
      >
        <div className="h-full w-full overflow-y-auto">
          <ProtectedWrapper allowedRoles={allowedRoles}>
            {children}
          </ProtectedWrapper>
        </div>
      </main>
    </div>
  )
}
