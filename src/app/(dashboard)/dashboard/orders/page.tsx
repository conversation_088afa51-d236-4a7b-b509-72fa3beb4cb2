'use client'
import { useQuery } from '@tanstack/react-query'
import { LayoutGrid, List } from 'lucide-react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Suspense, useState } from 'react'

import { fetchOrdersWithPaginationComplete } from '@/actions/orders'
import { ProtectedWrapper } from '@/components/auth/protected-wrapper'
import { Breadcrumbs } from '@/components/breadcrumbs'
import { OrdersTable } from '@/components/tables/order-tables/orders-table'
import { Button } from '@/components/ui/button'
import { Heading } from '@/components/ui/heading'
import { Separator } from '@/components/ui/separator'
import { CompleteOrder } from '@/types/custom'

import { OrderCard } from './components/order-card'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Orders', link: '/dashboard/orders' },
]

export default function Page() {
  return (
    <ProtectedWrapper allowedRoles={['admin']}>
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <Breadcrumbs items={breadcrumbItems} />
        <div className="flex items-start justify-between">
          <Heading
            title="Orders"
            description="Manage orders and transactions"
          />
        </div>
        <Separator />
        <Suspense fallback={<div>Loading...</div>}>
          <Orders />
        </Suspense>
      </div>
    </ProtectedWrapper>
  )
}

const Orders = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const page = Number(searchParams.get('page')) || 1
  const pageLimit = Number(searchParams.get('limit')) || 10
  const [viewMode, setViewMode] = useState<'table' | 'card'>('table')

  const { isLoading, error, data } = useQuery({
    queryKey: ['orders', page, pageLimit],
    queryFn: async () => {
      try {
        const response = await fetchOrdersWithPaginationComplete(
          page,
          pageLimit
        )
        return response
      } catch (error: any) {
        if (error?.message?.includes('Unauthorized')) {
          router.push('/login')
        }
        throw error // Re-throw the error to be caught by React Query's error handling
      }
    },
  })

  if (error) {
    return (
      <div className="flex h-[200px] w-full items-center justify-center text-destructive">
        Error loading orders: {(error as Error).message}
      </div>
    )
  }

  const { orders, count } = data || { orders: [], count: 0 }
  const pageCount = Math.ceil((count || 0) / pageLimit)

  return (
    <div className="w-full space-y-4">
      <div className="flex justify-end">
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'table' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('table')}
          >
            <List className="mr-2 h-4 w-4" />
            Table
          </Button>
          <Button
            variant={viewMode === 'card' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('card')}
          >
            <LayoutGrid className="mr-2 h-4 w-4" />
            Cards
          </Button>
        </div>
      </div>

      {viewMode === 'table' ? (
        <OrdersTable
          pageNo={page}
          totalCount={count || 0}
          data={orders || []}
          pageCount={pageCount}
          isLoading={isLoading}
        />
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
          {isLoading ? (
            Array.from({ length: pageLimit }).map((_, idx) => (
              <OrderCard
                key={idx}
                order={{} as CompleteOrder}
                isLoading={true}
              />
            ))
          ) : orders?.length > 0 ? (
            orders.map((order) => <OrderCard key={order.id} order={order} />)
          ) : (
            <div className="col-span-full py-10 text-center">
              No orders found
            </div>
          )}
        </div>
      )}
    </div>
  )
}
