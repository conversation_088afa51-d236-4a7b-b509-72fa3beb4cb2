'use client'
import { MoreH<PERSON>zontal } from 'lucide-react'
import { useState } from 'react'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import Skeleton from '@/components/ui/skeleton'
import { CompleteOrder } from '@/types/custom'

interface OrderCardProps {
  order: CompleteOrder
  isLoading?: boolean
}

export function OrderCard({ order, isLoading = false }: OrderCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (isLoading) {
    return (
      <Card className="overflow-hidden">
        <CardHeader className="pb-2">
          <div className="flex justify-between">
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </CardHeader>
        <CardContent className="pb-3">
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        </CardContent>
        <CardFooter>
          <Skeleton className="h-9 w-full" />
        </CardFooter>
      </Card>
    )
  }

  const getStatusBadgeVariant = (status: string) => {
    return status?.toLowerCase() === 'completed' ||
      status?.toLowerCase() === 'paid'
      ? 'success'
      : status?.toLowerCase() === 'pending'
        ? 'destructive'
        : 'secondary'
  }

  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex justify-between">
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <h3 className="font-medium">{order.order_id}</h3>
              <Badge variant={getStatusBadgeVariant(order.payment_status)}>
                {order.payment_status || 'N/A'}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              {new Date(order.created_at).toLocaleDateString()}
            </p>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(order.id)}
              >
                Copy order ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem>View details</DropdownMenuItem>
              <DropdownMenuItem>View customer</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pb-3">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm font-medium">Customer:</span>
            <span className="text-sm">{order.user_name || 'N/A'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium">Email:</span>
            <span className="text-sm">{order.user_email || 'N/A'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm font-medium">Amount:</span>
            <span className="text-sm">${order.amount || '0.00'}</span>
          </div>
          {isExpanded && (
            <>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Payment Method:</span>
                <span className="text-sm capitalize">
                  {order.payment_method || 'N/A'}
                </span>
              </div>
              {order.coupon_code && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium">Coupon:</span>
                  <span className="text-sm">{order.coupon_code}</span>
                </div>
              )}
            </>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? 'Show Less' : 'Show More'}
        </Button>
      </CardFooter>
    </Card>
  )
}
