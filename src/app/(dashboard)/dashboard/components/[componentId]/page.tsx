'use client'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'next/navigation'
import React from 'react'

import { getComponent } from '@/actions/components' // Ensure this function works properly
import { Breadcrumbs } from '@/components/breadcrumbs'
import { ComponentForm } from '@/components/forms/component-form'
import { ScrollArea } from '@/components/ui/scroll-area'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Components', link: '/dashboard/components' },
  { title: 'Add New', link: '/dashboard/components/new' },
]

// export default function Page({ params }: { params: { componentId: string } }) {
export default function Page() {
  // Use React Query to fetch data if the componentId is not "new"

  const params = useParams<{ componentId: string }>()

  const {
    isLoading,
    error,
    data: initialData,
  } = useQuery({
    queryKey: ['component', params.componentId],
    queryFn: () => getComponent(params.componentId),
    enabled: params.componentId !== 'new', // Don't fetch data if it's a new component
  })

  // console.log(params, 'params')
  // console.log(initialData, 'initialData')

  // Handle loading state
  if (isLoading) {
    return <div>Loading...</div>
  }

  // Handle error state
  if (error) {
    return <div>Error: {(error as Error).message}</div> // Cast error to Error to access the message property
  }

  return (
    <ScrollArea className="h-full">
      <div className="flex-1 space-y-4 p-8">
        <Breadcrumbs items={breadcrumbItems} />
        <ComponentForm
          initialData={params.componentId === 'new' ? null : initialData}
          key={params.componentId} // Key to ensure the form rerenders when componentId changes
        />
      </div>
    </ScrollArea>
  )
}
