'use client'
import { ChevronLeftIcon, MenuIcon, XIcon } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { useEffect, useState } from 'react'
// import { IconChevronsLeft, IconMenu2, IconX } from '@tabler/icons-react'
// import { Layout } from './custom/layout'
// import { Button } from './custom/button'

import { Layout } from '@/components/custom/layout'
import Nav from '@/components/custom/nav'
import { Button } from '@/components/ui/button'
import { dashboardNavItems } from '@/constants/data'
import { useSidebar } from '@/hooks/useSidebar'
import { cn } from '@/lib/utils'

type SidebarProps = React.HTMLAttributes<HTMLElement>

export default function DashboardSidebar({
  className,
  //   isCollapsed,
  //   setIsCollapsed,
}: SidebarProps) {
  const [navOpened, setNavOpened] = useState(false)
  const { isCollapsed, toggle } = useSidebar()

  // console.log(categories)

  /* Make body not scrollable when navBar is opened */
  useEffect(() => {
    if (navOpened) {
      document.body.classList.add('overflow-hidden')
    } else {
      document.body.classList.remove('overflow-hidden')
    }
  }, [navOpened])

  return (
    <aside
      className={cn(
        `md:h-svh fixed left-0 right-0 top-0 z-50 w-full border-r-2 border-r-muted transition-[width] md:bottom-0 md:right-auto ${
          isCollapsed ? 'md:w-14' : 'md:w-64'
        }`,
        className
      )}
    >
      {/* Overlay in mobile */}
      <div
        onClick={() => setNavOpened(false)}
        className={`absolute inset-0 transition-[opacity] delay-100 duration-700 ${
          navOpened ? 'h-svh opacity-50' : 'h-0 opacity-0'
        } w-full bg-black md:hidden`}
      />

      <Layout fixed className={navOpened ? 'h-svh' : ''}>
        {/* Header */}
        <Layout.Header
          sticky
          className="z-50 flex justify-between px-4 py-3 shadow-sm md:px-4"
        >
          <Link
            prefetch={true}
            href={'/'}
            className={`flex items-center ${!isCollapsed ? 'gap-2' : ''}`}
          >
            {/* <ChevronsDown className="mr-2 h-9 w-9 rounded-lg border border-secondary bg-gradient-to-tr from-primary via-primary/70 to-primary text-white" />

            <div
              className={`flex flex-col justify-end truncate ${
                isCollapsed ? 'invisible w-0' : 'visible w-auto'
              }`}
            >
              <span className="font-medium">CopyElement</span>

            </div> */}

            <div
              className={`flex flex-col justify-end truncate ${
                isCollapsed ? ' ' : 'visible w-auto'
              }`}
            >
              {/* <span className="font-medium">CopyElement</span> */}
              <Image
                className="rounded-lg "
                width={80}
                height={20}
                // src="https://utfs.io/f/LhUzHyAuqPAKsszXMpeNTal7yqp4hwouLCGieMIXZ2WVk91B"
                src={
                  isCollapsed
                    ? 'https://utfs.io/f/LhUzHyAuqPAKx520Mrc1PXGUOs3opCecDqjZnIQyYFm0xu9d'
                    : 'https://utfs.io/f/LhUzHyAuqPAKUSSOY3WlNcqwD9Fjd5n0CPIg1bEmyO3u7oRA'
                }
                alt="CopyElement Logo"
              />
            </div>
          </Link>

          {/* Toggle Button in mobile */}
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            aria-label="Toggle Navigation"
            aria-controls="sidebar-menu"
            aria-expanded={navOpened}
            onClick={() => setNavOpened((prev) => !prev)}
          >
            {navOpened ? <XIcon /> : <MenuIcon />}
          </Button>
        </Layout.Header>

        {/* Navigation links */}
        <Nav
          id="sidebar-menu"
          className={`z-40 h-full flex-1 overflow-auto ${
            navOpened ? 'max-h-screen' : 'max-h-0 py-0 md:max-h-screen md:py-2'
          }`}
          closeNav={() => setNavOpened(false)}
          isCollapsed={isCollapsed}
          links={[...dashboardNavItems]}
          // links={categories ? categories : []}
        />

        {/* Scrollbar width toggle button */}
        <Button
          onClick={toggle}
          size="icon"
          variant="outline"
          className="absolute -right-5 top-1/2 z-50 hidden rounded-full md:inline-flex"
        >
          <ChevronLeftIcon
            className={`h-5 w-5 ${isCollapsed ? 'rotate-180' : ''}`}
          />
        </Button>
      </Layout>
    </aside>
  )
}
