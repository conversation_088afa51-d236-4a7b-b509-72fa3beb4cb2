'use client'

import Link from 'next/link'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Transaction } from '@/types/custom'
import { getBadgeVariant } from '@/utils/functions/small-functions'

export interface TransactionCardProps {
  transaction: Transaction
  isExpanded: boolean
}

export function TransactionCard({
  transaction,
  isExpanded,
}: TransactionCardProps) {
  const {
    id,
    status,
    created_at,
    user_id,
    amount,
    currency,
    payment_method,
    coupon_id,
    invoice_url,
    subscription_id,
    updated_at,
  } = transaction

  return (
    <Card className="overflow-hidden border-muted bg-background shadow-md">
      <CardHeader className="bg-muted/40 pb-3">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold tracking-tight text-primary-foreground">
                {id ? (
                  <Link
                    prefetch={true}
                    href={`/transactions/${id}`}
                    className="text-primary-foreground/70 underline transition hover:text-muted-foreground"
                  >
                    {id}
                  </Link>
                ) : (
                  <span className="text-muted-foreground">N/A</span>
                )}
              </h3>
              <Badge variant={getBadgeVariant(status)} className="capitalize">
                {status || 'N/A'}
              </Badge>
            </div>
            <p className="mt-1 text-xs text-muted-foreground">
              {created_at ? new Date(created_at).toLocaleDateString() : 'N/A'}
            </p>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-3 py-5">
        <CardItemLink
          title="User"
          value={user_id || 'N/A'}
          href={user_id ? `/users/${user_id}` : undefined}
        />
        <CardItem
          title="Amount"
          value={`${currency} ${amount?.toFixed(2) ?? '0.00'}`}
        />
        <CardItem
          title="Payment Method"
          value={payment_method ? payment_method : 'N/A'}
        />

        {isExpanded && (
          <>
            <Separator className="my-4" />
            <CardItemLink
              title="Coupon"
              value={coupon_id || 'N/A'}
              href={coupon_id ? `/coupons/${coupon_id}` : undefined}
            />
            <CardItemLink
              title="Invoice"
              value={invoice_url ? 'View' : 'N/A'}
              href={invoice_url ?? undefined}
            />
            <CardItemLink
              title="Subscription"
              value={subscription_id || 'N/A'}
              href={
                subscription_id
                  ? `/subscriptions/${subscription_id}`
                  : undefined
              }
            />
            <CardItem
              title="Updated"
              value={
                updated_at ? new Date(updated_at).toLocaleDateString() : 'N/A'
              }
            />
          </>
        )}
      </CardContent>
    </Card>
  )
}

interface CardItemProp {
  title: string
  value: string
  href?: string
}

const CardItem = ({ title, value }: CardItemProp) => (
  <div className="flex items-center justify-between">
    <p className="text-sm font-medium text-muted-foreground">{title}</p>
    <p className="text-right text-sm">{value}</p>
  </div>
)

const CardItemLink = ({ title, value, href }: CardItemProp) => {
  const isValid = href && value !== 'N/A'
  return (
    <div className="flex items-center justify-between">
      <p className="text-sm font-medium text-muted-foreground">{title}</p>
      {isValid ? (
        <Link
          href={`dashboard/${href!}`}
          className="text-right text-sm text-primary-foreground underline transition hover:text-muted-foreground"
        >
          {value}
        </Link>
      ) : (
        <p className="text-right text-sm text-muted-foreground">{value}</p>
      )}
    </div>
  )
}
