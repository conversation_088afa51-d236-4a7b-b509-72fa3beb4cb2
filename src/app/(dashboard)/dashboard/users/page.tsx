'use client'
import { useQuery } from '@tanstack/react-query'
import { Plus } from 'lucide-react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'

import { fetchUsersWithPagination } from '@/actions/users'
import { ProtectedWrapper } from '@/components/auth/protected-wrapper'
import { Breadcrumbs } from '@/components/breadcrumbs'
import { ProfileTable } from '@/components/tables/user-tables/user-table'
import { Button } from '@/components/ui/button'
import { Heading } from '@/components/ui/heading'
import { Separator } from '@/components/ui/separator'
// import Link from 'next/link'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'User', link: '/dashboard/user' },
]

export default function Page() {
  return (
    <ProtectedWrapper allowedRoles={['admin']}>
      <div className="flex-1 space-y-4  p-4 pt-6 md:p-8">
        <Breadcrumbs items={breadcrumbItems} />
        <div className="flex items-start justify-between">
          <Heading
            title={`Users `}
            description="Manage users (Client side table functionalities.)"
          />
          <Link href={'/dashboard/user/new'}>
            <Button
              className="text-xs md:text-sm"
              // onClick={() => router.push(`/dashboard/user/new`)}
            >
              <Plus className="mr-2 h-4 w-4" /> Add New
            </Button>
          </Link>
        </div>
        <Separator />
        {/* <ProfileTable data={users ? (users as User[]) : []} /> */}
        {/* <ProfileTable
          pageNo={page}
          totalCount={count ? count : 0} // Use count from Supabase for total users
          data={users ? users : []} // Pass fetched components
          pageCount={pageCount}
          isLoading={isLoading}
        /> */}
        <Suspense>
          <Users />
        </Suspense>
      </div>
    </ProtectedWrapper>
  )
}

const Users = () => {
  const searchParams = useSearchParams()
  const page = Number(searchParams.get('page')) || 1
  const pageLimit = Number(searchParams.get('limit')) || 10

  const { isLoading, error, data } = useQuery({
    queryKey: ['users', page, pageLimit],
    queryFn: () => fetchUsersWithPagination(page, pageLimit),
  })
  const { users, count } = data || {}
  console.log(data, 'data')
  if (error) {
    console.error('Error fetching categories:', error)
  }

  const pageCount = Math.ceil((count || 0) / pageLimit)
  return (
    <>
      <ProfileTable
        pageNo={page}
        totalCount={count ? count : 0} // Use count from Supabase for total users
        data={users ? users : []} // Pass fetched components
        pageCount={pageCount}
        isLoading={isLoading}
      />
    </>
  )
}
