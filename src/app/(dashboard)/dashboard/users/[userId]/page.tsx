'use client'

import { ProtectedWrapper } from '@/components/auth/protected-wrapper'
import { Breadcrumbs } from '@/components/breadcrumbs'
import ProfileForm from '@/components/forms/profile-form'
// import { ProductForm } from '@/components/forms/component-form';
import { ScrollArea } from '@/components/ui/scroll-area'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'User', link: '/dashboard/user' },
  { title: 'Create', link: '/dashboard/user/create' },
]

// for profile need these fields
// avatar_url: null
// email: '<EMAIL>'
// full_name: null
// id: '8d80f025-12c9-42a6-baef-d59f6c4cbef7'
// is_pro: false
// submitted_components_count: 0
// username: null
export default function Page() {
  return (
    <ProtectedWrapper allowedRoles={['admin']}>
      <ScrollArea className="h-full">
        <div className="flex-1 space-y-4 p-5">
          <Breadcrumbs items={breadcrumbItems} />
          {/* <ProductForm
          categories={[
            { _id: 'shirts', name: 'shirts' },
            { _id: 'pants', name: 'pants' }
          ]}
          initialData={null}
          key={null}
        /> */}

          <ProfileForm />
        </div>
      </ScrollArea>
    </ProtectedWrapper>
  )
}
