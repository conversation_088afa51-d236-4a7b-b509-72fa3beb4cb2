'use client'
import { useParams } from 'next/navigation'
import { useEffect, useState } from 'react'

import { getProfile, updateProfile } from '@/actions/profiles'
import { ProtectedWrapper } from '@/components/auth/protected-wrapper'
import { Breadcrumbs } from '@/components/breadcrumbs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Profile', link: '/dashboard/profile' },
]

export default function ProfileEditPage() {
  const params = useParams()
  const userId = params?.id as string
  const [profile, setProfile] = useState<any>(null)
  const [form, setForm] = useState<any>({})
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)

  useEffect(() => {
    async function fetchProfile() {
      if (!userId) return
      const data = await getProfile(userId)
      setProfile(data)
      setForm(data)
    }
    fetchProfile()
  }, [userId])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    await updateProfile(form.id, form)
    setLoading(false)
    setSuccess(true)
    setProfile(form)
    setTimeout(() => setSuccess(false), 2000)
  }

  if (!profile) return <div>Loading...</div>

  return (
    <ProtectedWrapper allowedRoles={['admin']}>
      <ScrollArea className="h-full">
        <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
          <Breadcrumbs items={breadcrumbItems} />
          <h2 className="mb-4 text-3xl font-bold tracking-tight">
            Edit Profile
          </h2>
          <Card>
            <CardHeader>
              <CardTitle>Edit Profile</CardTitle>
              <CardDescription>Update user profile information</CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-4" onSubmit={handleSubmit}>
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src={form.avatar_url || undefined} />
                    <AvatarFallback>
                      {form.first_name?.[0] || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <Input
                    type="text"
                    name="avatar_url"
                    placeholder="Avatar URL"
                    value={form.avatar_url || ''}
                    onChange={handleChange}
                  />
                </div>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <Input
                    type="text"
                    name="first_name"
                    placeholder="First Name"
                    value={form.first_name || ''}
                    onChange={handleChange}
                  />
                  <Input
                    type="text"
                    name="last_name"
                    placeholder="Last Name"
                    value={form.last_name || ''}
                    onChange={handleChange}
                  />
                  <Input
                    type="text"
                    name="username"
                    placeholder="Username"
                    value={form.username || ''}
                    onChange={handleChange}
                  />
                  <Input
                    type="email"
                    name="email"
                    placeholder="Email"
                    value={form.email || ''}
                    onChange={handleChange}
                  />
                </div>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Updating...' : 'Update Profile'}
                </Button>
                {success && (
                  <div className="text-green-600">Profile updated!</div>
                )}
              </form>
              <div className="mt-8">
                <h3 className="font-semibold">Other Info</h3>
                <div className="mt-2 grid grid-cols-1 gap-2 text-sm text-muted-foreground md:grid-cols-2">
                  <div>Role: {profile.role}</div>
                  <div>
                    Subscription: {profile.subscription_status || 'None'}
                  </div>
                  <div>Pro: {profile.is_pro ? 'Yes' : 'No'}</div>
                  <div>Lifetime: {profile.is_lifetime_pro ? 'Yes' : 'No'}</div>
                  <div>
                    Submitted Components:{' '}
                    {profile.submitted_components_count || 0}
                  </div>
                  <div>Created: {profile.created_at}</div>
                  <div>Updated: {profile.updated_at}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>
    </ProtectedWrapper>
  )
}
