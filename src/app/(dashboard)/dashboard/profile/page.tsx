'use client'
import { useQuery } from '@tanstack/react-query'
import { Plus } from 'lucide-react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'

import { fetchProfilesWithPagination } from '@/actions/profiles'
import { ProtectedWrapper } from '@/components/auth/protected-wrapper'
import { Breadcrumbs } from '@/components/breadcrumbs'
import { ProfileTable } from '@/components/tables/profile-table/profile-table'
import { Button } from '@/components/ui/button'
import { Heading } from '@/components/ui/heading'
import { Separator } from '@/components/ui/separator'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Profile', link: '/dashboard/profile' },
]

export default function Page() {
  return (
    <ProtectedWrapper allowedRoles={['admin']}>
      <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
        <Breadcrumbs items={breadcrumbItems} />
        <div className="flex items-start justify-between">
          <Heading
            title={`Profiles`}
            description="Manage user profiles (Client side table functionalities.)"
          />
          <Link prefetch={true} href={'/dashboard/profile/new'}>
            <Button
              className="text-xs md:text-sm"
              // onClick={() => router.push(`/dashboard/user/new`)}
            >
              <Plus className="mr-2 h-4 w-4" /> Add New
            </Button>
          </Link>
        </div>
        <Separator />
        <Suspense>
          <Profiles />
        </Suspense>
      </div>
    </ProtectedWrapper>
  )
}

const Profiles = () => {
  const searchParams = useSearchParams()
  const page = Number(searchParams.get('page')) || 1
  const pageLimit = Number(searchParams.get('limit')) || 10

  const { isLoading, error, data } = useQuery({
    queryKey: ['profiles', page, pageLimit],
    queryFn: () => fetchProfilesWithPagination(page, pageLimit),
  })
  const { profiles, count } = data || {}
  if (error) {
    console.error('Error fetching profiles:', error)
  }

  const pageCount = Math.ceil((count || 0) / pageLimit)
  return (
    <>
      <ProfileTable
        pageNo={page}
        totalCount={count ? count : 0}
        data={profiles ? profiles : []}
        pageCount={pageCount}
        isLoading={isLoading}
      />
    </>
  )
}
