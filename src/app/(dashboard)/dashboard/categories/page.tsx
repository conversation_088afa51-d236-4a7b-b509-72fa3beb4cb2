'use client'
import { useQuery } from '@tanstack/react-query'
import { Plus } from 'lucide-react'
import Link from 'next/link'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { Suspense } from 'react'

import { fetchCategoriesWithPagination } from '@/actions/categories'
import { Breadcrumbs } from '@/components/breadcrumbs'
import { CategoryCard } from '@/components/cards/category-card'
import { buttonVariants } from '@/components/ui/button'
import { Heading } from '@/components/ui/heading'
import { Pagination } from '@/components/ui/pagination'
import { Separator } from '@/components/ui/separator'
import { canCreate } from '@/lib/permissions'
import { cn } from '@/lib/utils'
import { useProfileStore } from '@/stores/profile-store'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Categories', link: '/dashboard/Categories' },
]

export default function Page() {
  const { profile } = useProfileStore()
  const userRole = profile?.role as 'admin' | 'editor' | null

  return (
    // <ProtectedWrapper allowedRoles={['admin', 'editor']}>
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      <Breadcrumbs items={breadcrumbItems} />

      <div className="flex items-start justify-between">
        <Heading
          title={`Categories`}
          description="Manage categories (you can update and delete categories.)"
        />

        {canCreate(userRole) && (
          <Link
            prefetch={true}
            href={'/dashboard/categories/new'}
            className={cn(buttonVariants({ variant: 'default' }))}
          >
            <Plus className="mr-2 h-4 w-4" /> Add New
          </Link>
        )}
      </div>
      <Separator />

      <Suspense>
        <Categories />
      </Suspense>
    </div>
    // </ProtectedWrapper>
  )
}

const Categories = () => {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // Fix for URLSearchParams error
  const currentParams = new URLSearchParams(searchParams.toString())

  const page = Number(currentParams.get('page')) || 1
  const pageLimit = Number(currentParams.get('limit')) || 9

  const { isLoading, error, data } = useQuery({
    queryKey: ['categories', page, pageLimit],
    queryFn: () => fetchCategoriesWithPagination(page, pageLimit),
  })

  const { categories, count } = data || { categories: [], count: 0 }
  const pageCount = Math.ceil((count || 0) / pageLimit)

  const goToPage = (pageNumber: number) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', pageNumber.toString())
    router.push(`${pathname}?${params.toString()}`, { scroll: false })
  }

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4 text-center text-red-500">
        Error loading categories
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <section className="min-h-[calc(100vh-40vh)] w-full">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {isLoading
            ? Array.from({ length: pageLimit }).map((_, idx) => (
                <CategoryCard key={idx} category={{} as any} isLoading={true} />
              ))
            : categories?.map((category) => (
                <CategoryCard key={category.id} category={category} />
              ))}
        </div>
      </section>

      <Pagination
        currentPage={page}
        pageCount={pageCount}
        pageLimit={pageLimit}
        totalItems={count || 0}
        isLoading={isLoading}
        onPageChange={goToPage}
      />
    </div>
  )
}
