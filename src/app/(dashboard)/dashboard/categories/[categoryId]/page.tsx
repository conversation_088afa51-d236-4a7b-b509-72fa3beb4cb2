'use client'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'next/navigation'

import { getCategory } from '@/actions/categories'
import { Breadcrumbs } from '@/components/breadcrumbs'
import Error from '@/components/custom/error'
import Loading from '@/components/custom/loading'
import { CategoryForm } from '@/components/forms/category-form'
import { ScrollArea } from '@/components/ui/scroll-area'

const breadcrumbItems = [
  { title: 'Dashboard', link: '/dashboard' },
  { title: 'Categories', link: '/dashboard/categories' },
  { title: 'Manage', link: '#' },
]

export default function Page() {
  const params = useParams<{ categoryId: string }>()
  const { categoryId } = params

  const {
    isLoading,
    error,
    data: initialData,
    refetch,
  } = useQuery({
    queryKey: ['category', categoryId],
    queryFn: () => getCategory(categoryId),
    enabled: categoryId !== 'new',
  })

  if (isLoading) {
    return <Loading text="Loading category..." />
  }

  if (error) {
    return (
      <Error
        text="Failed to load category. Please try again."
        retry={() => refetch()}
      />
    )
  }

  return (
    <ScrollArea className="h-full">
      <div className="flex-1 space-y-4 p-8">
        <Breadcrumbs items={breadcrumbItems} />
        <CategoryForm
          initialData={categoryId === 'new' ? null : initialData}
          key={categoryId}
        />
      </div>
    </ScrollArea>
  )
}
