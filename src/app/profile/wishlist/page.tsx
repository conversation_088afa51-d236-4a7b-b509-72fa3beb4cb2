'use client'

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useCallback, useState } from 'react'
import { toast } from 'sonner'

import { getUserLikedComponents, toggleLike } from '@/actions/likes'
import { ProfileWrapper } from '@/components/profile/profile-wrapper'
import {
  LikedComponentsGrid,
  LikedPageBanner,
  LikedPageEmptyState,
  LikedPageErrorState,
  LikedPageSkeleton,
} from '@/components/profile/wishlist'
import { useAsyncThrottle } from '@/hooks/use-throttle'
import { useProfileStore } from '@/stores/profile-store'
import { Profile } from '@/types/custom'

export default function LikedComponentsPage() {
  return (
    <ProfileWrapper
      loadingMessage="Loading your liked components"
      loadingDescription="Gathering your favorite components..."
    >
      {(profile) => <LikedComponentsContent profile={profile} />}
    </ProfileWrapper>
  )
}

function LikedComponentsContent({ profile }: { profile: Profile }) {
  const { user, fetchProfile } = useProfileStore()
  const queryClient = useQueryClient()
  const [removingComponents, setRemovingComponents] = useState<Set<string>>(
    new Set()
  )

  const {
    data: queryResult,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['liked-components', user?.id],
    queryFn: async () => {
      if (!profile?.liked_components?.length) return []

      const result = await getUserLikedComponents(profile)
      return result
    },
    enabled: !!profile?.liked_components?.length,
  })

  // Handle the result - it can be an array or an error object
  const likedComponents = Array.isArray(queryResult) ? queryResult : []
  const hasError =
    queryResult && !Array.isArray(queryResult) && 'error' in queryResult

  // Enhanced remove component mutation with proper error handling and loading states
  const removeComponentMutation = useMutation({
    mutationFn: async (componentId: string) => {
      const toastId = toast.loading(`Removing component from favorites...`, {
        description: 'This may take a moment',
      })

      try {
        const result = await toggleLike(componentId)

        if (result.error) {
          throw new Error(result.error)
        }

        // Update loading state
        setRemovingComponents((prev) => {
          const newSet = new Set(prev)
          newSet.delete(componentId)
          return newSet
        })

        // Show success toast
        toast.success('Component removed from favorites!', {
          id: toastId,
          description: 'The component has been removed from your liked list',
        })

        return result
      } catch (error) {
        // Update loading state on error
        setRemovingComponents((prev) => {
          const newSet = new Set(prev)
          newSet.delete(componentId)
          return newSet
        })

        const errorMessage =
          error instanceof Error ? error.message : 'Failed to remove component'
        toast.error('Failed to remove component', {
          id: toastId,
          description: errorMessage,
        })
        throw error
      }
    },
    onSuccess: async () => {
      // Refresh profile to update liked components
      if (user?.id) {
        await fetchProfile(user.id)
      }
      // Invalidate and refetch queries
      await queryClient.invalidateQueries({
        queryKey: ['liked-components', user?.id],
      })
    },
    onError: (error) => {
      console.error('Error removing component:', error)
    },
  })

  // Throttled remove component handler
  const throttledRemoveComponent = useCallback(
    async (componentId: string) => {
      // Prevent multiple simultaneous requests for the same component
      if (
        removingComponents.has(componentId) ||
        removeComponentMutation.isPending
      ) {
        toast.warning('Please wait...', {
          description: 'Component removal is already in progress',
        })
        return
      }

      // Add to loading state
      setRemovingComponents((prev) => new Set(prev).add(componentId))

      // Execute the mutation
      removeComponentMutation.mutate(componentId)
    },
    [removingComponents, removeComponentMutation]
  )

  // Apply throttling to prevent rapid clicks
  const { execute: handleRemoveComponent } = useAsyncThrottle(
    throttledRemoveComponent,
    1500 // 1.5 second throttle
  )

  // Loading State
  if (isLoading) {
    return <LikedPageSkeleton />
  }

  // Error State
  if (hasError) {
    return (
      <LikedPageErrorState
        error={(queryResult as { error: string }).error}
        onRetry={() => refetch()}
      />
    )
  }

  // Empty State
  if (!profile?.liked_components?.length) {
    return <LikedPageEmptyState />
  }

  // Display Liked Components
  const likedComponentsCount = likedComponents?.length || 0

  return (
    <div className="bg-background">
      <div className="container mx-auto max-w-7xl px-4 py-8 md:py-16">
        {/* Banner Section */}
        <LikedPageBanner likedComponentsCount={likedComponentsCount} />

        {/* Main Content */}
        <div className="mt-8">
          {/* Components Grid */}
          <LikedComponentsGrid
            components={likedComponents as any[]}
            onRemove={handleRemoveComponent}
            removingComponents={removingComponents}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  )
}
