'use client'

import { useState } from 'react'

import { ActivityCard } from '@/components/cards/activity-card'
import { ProfileCard } from '@/components/cards/profile-card'
import { ProfileStatsCard } from '@/components/cards/profile-stats-card'
import { QuickActionsCard } from '@/components/cards/quick-actions-card'
import { ProfileLayout } from '@/components/layout/profile-layout'
import { ProfileTabs } from '@/components/profile/profile-tabs'
import { ProfileWrapper } from '@/components/profile/profile-wrapper'

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false)

  return (
    <ProfileWrapper>
      {(profile) => (
        <ProfileLayout>
          <div className="container mx-auto max-w-7xl space-y-8 px-4 py-16 md:py-20 lg:py-24">
            {/* Profile Header Card */}
            <ProfileCard
              profile={profile}
              onEditToggle={() => setIsEditing(!isEditing)}
              isEditing={isEditing}
              isCurrentUser={true}
              className="shadow-xl"
            />

            {/* Two column layout for stats and sidebar */}
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              <div className="space-y-8 lg:col-span-2">
                {/* Profile Stats Card */}
                <ProfileStatsCard profile={profile} className="shadow-lg" />

                {/* Profile Tabs - Now below stats */}
                <div className="rounded-xl border-0 bg-gradient-to-br from-background via-background to-accent/5 shadow-lg">
                  <ProfileTabs
                    profile={profile}
                    isEditing={isEditing}
                    onEditToggle={() => setIsEditing(!isEditing)}
                    onEditSuccess={() => setIsEditing(false)}
                  />
                </div>
              </div>

              <div className="space-y-6">
                {/* Quick Actions Card */}
                <QuickActionsCard className="shadow-lg" />

                {/* Activity Card */}
                <ActivityCard activities={[]} className="shadow-lg" />
              </div>
            </div>
          </div>
        </ProfileLayout>
      )}
    </ProfileWrapper>
  )
}
