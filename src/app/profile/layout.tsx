import { redirect } from 'next/navigation'

import { createClient } from '@/utils/supabase/server'

export default async function ProfileLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const supabase = createClient()

  const {
    data: { user },
    error,
  } = await supabase.auth.getUser()

  if (!user || error) {
    redirect('/login')
  }

  return <main className=" min-h-screen">{children}</main>
}
