import { FileText, Home, Layout, VerifiedIcon } from 'lucide-react'
import Link from 'next/link'

import { ConfettiEffect } from '@/components/custom/confetti-effect'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

export default function PaymentSuccessPage() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <div className="w-full max-w-2xl space-y-8">
        <Card className="overflow-hidden border-none">
          <CardHeader className="relative bg-gradient-to-r from-primary/75 to-primary p-6 pb-12 text-white">
            <div className=" mx-auto flex h-24 w-24 transform items-center justify-center rounded-full  bg-gradient-to-br from-emerald-400 to-emerald-600 shadow-lg">
              <VerifiedIcon className="h-12 w-12 text-white" />
            </div>
            <CardTitle className="text-center text-3xl font-bold">
              Payment Successful!
            </CardTitle>
            <CardDescription className="text-center text-blue-100">
              Welcome to CopyElement! Your Pro access is now active.
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6 p-6 pt-12">
            <div className="space-y-4">
              <Button size={'lg'} className="w-full py-6 text-lg " asChild>
                <Link prefetch href="/components/pro">
                  <Layout className="mr-2 h-5 w-5" />
                  Browse Components
                </Link>
              </Button>

              <Button
                variant="outline-primary"
                className="w-full py-6 text-lg"
                asChild
              >
                <Link prefetch href="/templates/pro">
                  <FileText className="mr-2 h-5 w-5" />
                  Browse Templates
                </Link>
              </Button>

              <Button
                variant="secondary"
                className="w-full py-6 text-lg"
                asChild
              >
                <Link href="/" prefetch>
                  <Home className="mr-2 h-5 w-5" />
                  Return to Homepage
                </Link>
              </Button>
            </div>

            <div className="space-y-2 text-center text-sm text-gray-500">
              <p>
                Need assistance?{' '}
                <Link
                  href="/contact-us"
                  className="font-medium text-blue-600 hover:underline"
                >
                  Contact Support
                </Link>
              </p>
              <p className="text-xs">
                A confirmation email has been sent to your inbox.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <ConfettiEffect numberOfPieces={600} />
    </div>
  )
}
