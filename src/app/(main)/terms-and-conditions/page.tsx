import PolicyLayout from '@/components/custom/policy-layout'

export default function TermsPage() {
  return (
    <PolicyLayout title="Terms and Conditions">
      <div className="space-y-6">
        <section>
          <h2 className="mb-2 text-2xl font-semibold">
            1. Acceptance of Terms
          </h2>
          <p>
            By accessing and using CopyElement, the world&apos;s first Elementor
            Component Library, you agree to comply with and be bound by these
            Terms and Conditions.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">2. Use of Service</h2>
          <p>
            CopyElement provides a library of Elementor components for web
            design. You agree to use these components in compliance with all
            applicable laws and regulations.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">
            3. Intellectual Property
          </h2>
          <p>
            All components, designs, and related materials provided by
            CopyElement are protected by copyright and other intellectual
            property laws. You may use these for your projects but may not
            redistribute or resell them.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">4. User Accounts</h2>
          <p>
            You are responsible for maintaining the confidentiality of your
            account and password. CopyElement reserves the right to refuse
            service, terminate accounts, or remove content at our discretion.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">
            5. Limitations of Liability
          </h2>
          <p>
            CopyElement shall not be liable for any indirect, incidental,
            special, consequential or punitive damages resulting from your use
            of or inability to use the service.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">6. Changes to Terms</h2>
          <p>
            CopyElement reserves the right to modify these terms at any time. We
            will provide notice of significant changes by posting on our
            website.
          </p>
        </section>
      </div>
    </PolicyLayout>
  )
}
