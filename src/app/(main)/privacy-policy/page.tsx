import { Metadata } from 'next'

import PolicyLayout from '@/components/custom/policy-layout'
import { generateMetadata } from '@/utils'

export const metadata: Metadata = generateMetadata({
  title: 'Privacy Policy - CopyElement',
  description:
    "Read CopyElement's privacy policy to understand how we collect, use, and protect your personal information.",
  noIndex: true, // Usually privacy policies don't need to be indexed
})

export default function PrivacyPage() {
  return (
    <PolicyLayout title="Privacy Policy">
      <div className="space-y-6">
        <section>
          <h2 className="mb-2 text-2xl font-semibold">
            1. Information Collection
          </h2>
          <p>
            CopyElement collects personal information such as name, email
            address, and payment details when you create an account or make a
            purchase. We also collect usage data to improve our Elementor
            Component Library service.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">2. Use of Information</h2>
          <p>
            We use collected information to provide and improve our services,
            process payments, send administrative emails, and respond to
            inquiries. We do not sell or rent your personal information to third
            parties.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">3. Data Security</h2>
          <p>
            CopyElement implements security measures to protect your personal
            information. However, no method of transmission over the Internet is
            100% secure, and we cannot guarantee absolute security.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">4. Cookies</h2>
          <p>
            We use cookies to enhance your experience on our website. You can
            set your browser to refuse all or some browser cookies, but this may
            limit your ability to use some features of our service.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">
            5. Third-Party Services
          </h2>
          <p>
            CopyElement may use third-party services that collect, monitor and
            analyze data. These third parties have their own privacy policies
            addressing how they use such information.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">
            6. Changes to Privacy Policy
          </h2>
          <p>
            We may update our Privacy Policy from time to time. We will notify
            you of any changes by posting the new Privacy Policy on this page
            and updating the last updated date.
          </p>
        </section>

        <section>
          <h2 className="mb-2 text-2xl font-semibold">7. Contact Us</h2>
          <p>
            If you have any questions about this Privacy Policy, please contact
            <NAME_EMAIL>.
          </p>
        </section>
      </div>
    </PolicyLayout>
  )
}
