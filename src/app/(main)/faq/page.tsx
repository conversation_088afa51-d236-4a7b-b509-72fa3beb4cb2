import { Metadata } from 'next'
import Script from 'next/script'
import { BreadcrumbList, FAQPage, WithContext } from 'schema-dts'

import { FAQSection } from '@/components/custom/faq-section'
import { generateMetadata } from '@/utils'
import { faqItems } from '@/utils/constants/faq'

export const metadata: Metadata = generateMetadata({
  title: 'FAQ - CopyElement | Frequently Asked Questions',
  description:
    'Find answers to common questions about CopyElement components, pricing, usage, and Elementor compatibility. Get help with our comprehensive FAQ guide.',
})

// Add breadcrumb schema
const breadcrumbSchema: WithContext<BreadcrumbList> = {
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  itemListElement: [
    {
      '@type': 'ListItem',
      position: 1,
      item: {
        '@id': 'https://copyelement.com',
        name: 'Home',
      },
    },
    {
      '@type': 'ListItem',
      position: 2,
      item: {
        '@id': 'https://copyelement.com/faq',
        name: 'FAQ',
      },
    },
  ],
}

const jsonLd: WithContext<FAQPage> = {
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: faqItems.map((faq) => ({
    '@type': 'Question',
    name: faq.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: faq.answer,
    },
  })),
  headline: 'Frequently Asked Questions - CopyElement',
  description:
    'Find answers to common questions about CopyElement components and services.',
  author: {
    '@type': 'Organization',
    name: 'CopyElement',
    url: 'https://copyelement.com',
  },
  datePublished: '2024-01-01', // Update with actual date
  dateModified: new Date().toISOString().split('T')[0],
}

export default function Page() {
  return (
    <>
      <Script
        id="faq-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify([jsonLd, breadcrumbSchema]),
        }}
      />
      {/* Add visible breadcrumbs for users */}
      <nav aria-label="Breadcrumb" className="container mx-auto py-4">
        <ol className="flex items-center space-x-2 text-sm">
          <li>
            <a href="/" className="text-gray-500 hover:text-primary">
              Home
            </a>
          </li>
          <li>
            <span className="text-gray-400">/</span>
          </li>
          <li>
            <span className="text-gray-900 dark:text-gray-100">FAQ</span>
          </li>
        </ol>
      </nav>
      <FAQSection
        items={faqItems}
        showAllFAQsButton={false}
        showContactSupport={true}
      />
    </>
  )
}
