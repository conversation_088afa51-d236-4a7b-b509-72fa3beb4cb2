import { AlertCircle, Home, LifeBuoy, RefreshCcw } from 'lucide-react'
import Link from 'next/link'

import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

export default function PaymentFailedPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="border-0 shadow-lg">
          <CardHeader className="space-y-1">
            <div className="flex items-center justify-center">
              <div className="rounded-full bg-gradient-to-br from-destructive/60 to-destructive p-3">
                <AlertCircle className="h-8 w-8 text-primary-foreground" />
              </div>
            </div>
            <CardTitle className="text-center text-3xl font-bold text-destructive">
              Payment Failed
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p className="text-muted-foreground">
              We&apos;re sorry, but your payment could not be processed at this
              time.
            </p>
            <div className="rounded-lg bg-secondary/50 p-4 text-left">
              <p className="mb-2 font-semibold text-primary">
                Possible reasons:
              </p>
              <ul className="list-inside list-disc space-y-1 text-sm text-muted-foreground">
                <li>Insufficient funds</li>
                <li>Incorrect payment information</li>
                <li>Temporary issue with your payment method</li>
                <li>Transaction declined by your bank</li>
              </ul>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <div className="flex w-full space-x-2">
              <Button className="flex-1" variant="default" asChild>
                <Link prefetch={true} href="/pricing">
                  <RefreshCcw className="mr-2 h-4 w-4" />
                  Try Again
                </Link>
              </Button>
              <Button variant="outline" className="flex-1" asChild>
                <Link prefetch={true} href="/contact-us">
                  <LifeBuoy className="mr-2 h-4 w-4" />
                  Contact Support
                </Link>
              </Button>
            </div>
            <Button variant="ghost" className="w-full" asChild>
              <Link prefetch={true} href="/">
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
      <footer className="mt-8 text-center text-muted-foreground">
        <p>
          Need help?{' '}
          <Link
            prefetch={true}
            href="/contact-us"
            className="text-primary hover:underline"
          >
            Contact Us
          </Link>
        </p>
      </footer>
    </div>
  )
}
