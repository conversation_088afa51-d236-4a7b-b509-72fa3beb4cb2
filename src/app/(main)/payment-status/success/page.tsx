import { Check<PERSON>ircle, Home, ShoppingBag } from 'lucide-react'
import Link from 'next/link'

import { ConfettiEffect } from '@/components/custom/confetti-effect'
import { Button } from '@/components/ui/button'
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

export default function PaymentSuccessPage() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-background to-secondary/10 p-4">
      <ConfettiEffect duration={6000} />

      <div className="w-full max-w-md">
        <Card className="border-0 shadow-lg">
          <CardHeader className="space-y-1">
            <div className="flex items-center justify-center">
              <div className="rounded-ful bg-gradient-to-br from-green-400 to-green-600 p-3">
                <CheckCircle className="h-8 w-8 text-primary-foreground" />
              </div>
            </div>
            <CardTitle className="text-center text-3xl font-bold text-green-400">
              Payment Successful
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p className="text-muted-foreground">
              Thank you for your purchase! Your payment has been processed
              successfully.
            </p>
            {/* <div className="space-y-2 rounded-lg bg-secondary/50 p-4">
              <p className="font-semibold text-primary">Order #12345</p>
              <p className="text-sm text-muted-foreground">
                Date: {new Date().toLocaleDateString()}
              </p>
            </div> */}
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            <div className="flex w-full space-x-2">
              {/* <Button className="flex-1" variant="outline">
                <FileText className="mr-2 h-4 w-4" />
                View Order
              </Button> */}
              <Button variant="outline" className="flex-1" asChild>
                <Link prefetch={true} href="/">
                  <Home className="mr-2 h-4 w-4" />
                  Go Home
                </Link>
              </Button>

              <Button className="flex-1" variant="default" asChild>
                <Link prefetch={true} href="/components">
                  <ShoppingBag className="mr-2 h-4 w-4" />
                  {/* element  */}
                  Explore
                </Link>
              </Button>
            </div>
            {/* <Button variant="ghost" className="w-full" asChild>
              <Link href="/">
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Link>
            </Button> */}
          </CardFooter>
        </Card>
      </div>
      <footer className="mt-8 text-center text-muted-foreground">
        <p>
          Need help?{' '}
          <Link
            prefetch={true}
            href="/contact"
            className="text-primary hover:underline"
          >
            Contact Us
          </Link>
        </p>
      </footer>
    </div>
  )
}
