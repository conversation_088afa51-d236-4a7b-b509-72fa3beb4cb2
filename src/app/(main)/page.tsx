import { Metadata } from 'next'
import { cookies } from 'next/headers'

export const metadata: Metadata = generateMetadata({
  title: 'CopyElement - Largest Elementor Components Library',
  description:
    "Build stunning WordPress sites in minutes with CopyElement’s 3500+ Elementor components with just <PERSON>py - Paste. No extra plugins, 100% responsive, free & Pro compatible.',",
})

// import Header from '@/components/Header'
import { BentoGridSecondDemo } from '@/components/accertinity/bento-grid'
import { HeroScrollDemo } from '@/components/accertinity/hero-scroll-demo'
import ComponentsOverview from '@/components/custom/components-overview'
import CTASection from '@/components/custom/cta-section'
import { FAQSection } from '@/components/custom/faq-section'
import HowItWorks from '@/components/custom/how-it-works'
import { NewFeatures } from '@/components/features/new-features'
import HeroSection from '@/components/hero-section'
import { HeroParallaxDemo } from '@/components/hero-section/hero-parallax'
import FeaturedOn from '@/components/MagicuiComponents/featured-on'
import { Reviews } from '@/components/MagicuiComponents/reviews'
import { generateMetadata } from '@/utils'
import { faqItems } from '@/utils/constants/faq'
import { createClient } from '@/utils/supabase/server'

export default async function Index() {
  const cookieStore = cookies()

  const canInitSupabaseClient = () => {
    // This function is just for the interactive tutorial.
    // Feel free to remove it once you have Supabase connected.
    try {
      // createServerClient(cookieStore)
      createClient()
      return true
    } catch (e) {
      return false
    }
  }

  const isSupabaseConnected = canInitSupabaseClient()
  const endDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)

  return (
    <main className="overflow-hidden">
      <HeroSection />
      {/* <HeroSection2/> */}

      {/* <Features /> */}
      <NewFeatures />

      <HowItWorks />

      {/* <div className="flex w-full flex-col items-center justify-center border-y border-slate-900 px-5  py-10 md:py-20">
        <Heading>How it Works!</Heading>

        <p className="mx-auto mb-6 max-w-3xl text-lg text-muted-foreground">
          Just Copy and Paste the code of the component you want to use in your
          project.
        </p>
        <div className="relative w-full  max-w-5xl rounded-xl bg-white p-5">
          <Image
            src={
              'https://utfs.io/f/LhUzHyAuqPAKk2vtI6mGlLpwOh6IFmS7zdt5BPKCijMrvaDU'
            }
            width={500}
            height={100}
            alt="hero"
            className="h-full w-full rounded-lg"
          />
        </div>
      </div> */}

      <HeroScrollDemo />

      <FeaturedOn />

      {/* <video className="h-full w-full rounded-lg" autoPlay>
        <source
          src="https://utfs.io/f/LhUzHyAuqPAKZ82Sb1hb9z0PnXqpVMKRFhI7aykWAjOftSxG"
          type="video/mp4"
        />
        Your browser does not support the video tag.
      </video> */}
      {/* <ParallaxScrollDemo/> */}
      {/* <AnimatedListDemo /> */}
      {/* <ParallaxScrollDemo /> */}
      <ComponentsOverview />

      <HeroParallaxDemo />

      <BentoGridSecondDemo />
      <Reviews />

      <FAQSection
        items={faqItems}
        maxItems={6}
        showAllFAQsButton={true}
        showContactSupport={false}
        title="Common Questions"
        description="Quick answers to frequently asked questions about CopyElement"
        className="py-16"
      />
      <CTASection />

      {/* <CTASection /> */}
      {/* <Footer /> */}
    </main>
  )
}
