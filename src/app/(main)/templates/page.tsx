import { Metadata } from 'next'
import { Suspense } from 'react'

import { fetchAllTemplates } from '@/actions/template'
import ComponentGridCustom from '@/components/custom/component-grid-custom'
import NotFound from '@/components/custom/not-found'
import TemplateFilterHeader from '@/components/custom/template-filter-header'
import LoadingGridForTemplate from '@/components/loaders/loading-grid2'
import { ComponentWithCategory } from '@/types/custom'
import { generateMetadata } from '@/utils'

export const metadata: Metadata = generateMetadata({
  title: 'All Templates - CopyElement',
  description:
    'Browse our extensive collection of Elementor components. Find the perfect elements for your website and copy them with a single click.',
})

// Keep the revalidate export if you want to revalidate the page periodically
export const revalidate = 86400
export const dynamic = 'force-static'

interface TemplatePageProps {
  searchParams: { filter?: string }
}

export default async function TemplatePage({
  searchParams,
}: TemplatePageProps) {
  const filter = searchParams.filter || 'all'
  const data = await fetchAllTemplates()
  const allComponents = data?.templates || []
  const error = data?.error

  // Filter components based on the filter parameter
  let components = allComponents
  if (filter === 'pro') {
    components = allComponents?.filter(
      (component: any) => component.is_pro === true
    )
  } else if (filter === 'free') {
    components = allComponents?.filter(
      (component: any) => component.is_pro === false
    )
  }

  if (error) {
    console.error('Error fetching components:', error)
    return (
      <div className="flex h-56 min-h-screen w-full flex-1 items-start justify-center p-4">
        <NotFound text="Something Went Wrong" />
      </div>
    )
  }

  return (
    <Suspense fallback={<LoadingGridForTemplate />}>
      <div className="min-h-screen w-full">
        <TemplateFilterHeader
          title="Templates"
          description="A template section highlights the unique and significant aspects of your product or service. Effectively communicate what makes your product valuable, the benefits of what you offer and why it's worth considering."
          currentFilter={filter}
        />
        {components?.length ? (
          <ComponentGridCustom
            components={components as ComponentWithCategory[]}
            isTemplate
          />
        ) : (
          <div className="flex h-56 min-h-screen w-full flex-1 items-start justify-center p-4">
            <NotFound text="No Templates Found" />
          </div>
        )}
      </div>
    </Suspense>
  )
}
