import { Metadata } from 'next'
import { Suspense } from 'react'

import {
  fetchCategories,
  fetchCategoryBySlug,
} from '@/actions/server-categories'
import { fetchCategoryTemplates } from '@/actions/template'
import ComponentGridCustom from '@/components/custom/component-grid-custom'
import NotFound from '@/components/custom/not-found'
import TemplateFilterHeader from '@/components/custom/template-filter-header'
import LoadingGridForTemplate from '@/components/loaders/loading-grid2'
import { ComponentWithCategory } from '@/types/custom'
import { generateMetadata as generateBaseMetadata } from '@/utils'
import { formatTitle } from '@/utils/functions/urls'

export async function generateStaticParams() {
  const categories = await fetchCategoriesForPaths()

  const paths = categories.map((category) => ({
    cat: category.slug,
  }))

  paths.push({ cat: 'free' })
  paths.push({ cat: 'pro' })
  paths.push({ cat: 'popular' })

  return paths
}

async function fetchCategoriesForPaths() {
  try {
    const categories = await fetchCategories(true) // true for templates
    return categories
  } catch (error) {
    console.error('Error fetching categories for paths:', error)
    return [] // Return an empty array to avoid build failures
  }
}

interface CategoryPageProps {
  params: {
    cat: string
  }
  searchParams: { filter?: string }
}

export async function generateMetadata({
  params,
}: {
  params: { cat: string }
}): Promise<Metadata> {
  const category = params.cat.charAt(0).toUpperCase() + params.cat.slice(1)

  let title = `${formatTitle(category)} Template - CopyElement`
  let description = `Explore our collection of ${params.cat} Elementor components. Find and copy the perfect ${params.cat} elements for your website.`

  // Handle special cases
  if (params.cat === 'pro') {
    title = 'Premium Template - CopyElement'
    description =
      'Access our exclusive collection of premium Elementor components. Advanced features and designs for professional websites.'
  } else if (params.cat === 'free') {
    title = 'Free Template - CopyElement'
    description =
      'Browse our free Elementor components. High-quality website elements available at no cost.'
  }

  return generateBaseMetadata({
    title,
    description,
  })
}

export default async function TemplateCategoryPage({
  params,
  searchParams,
}: CategoryPageProps) {
  const { cat } = params
  const filter = searchParams?.filter || 'all'

  // Fetch category details for special categories or actual categories
  let category = null
  let categoryTitle = formatTitle(cat)
  let categoryDescription = `Explore our collection of ${cat} Elementor templates. Find and copy the perfect ${cat} elements for your website.`

  // Handle special cases
  if (cat === 'pro') {
    categoryTitle = 'Premium Templates'
    categoryDescription =
      'Access our exclusive collection of premium Elementor templates. Advanced features and designs for professional websites.'
  } else if (cat === 'free') {
    categoryTitle = 'Free Templates'
    categoryDescription =
      'Browse our free Elementor templates. High-quality website elements available at no cost.'
  } else if (cat === 'popular') {
    categoryTitle = 'Popular Templates'
    categoryDescription =
      'Discover our most popular Elementor templates. Trending designs loved by the community.'
  } else {
    // Fetch actual category details
    category = await fetchCategoryBySlug(cat)
    if (category) {
      categoryTitle = category.name || formatTitle(cat)
      categoryDescription = category.description || categoryDescription
    }
  }

  const data = await fetchCategoryTemplates(cat)
  const allComponents = data?.templates || []
  const error = data?.error || null

  // Filter components based on the filter parameter
  let components = allComponents
  if (filter === 'pro') {
    components = allComponents?.filter(
      (component: any) => component.is_pro === true
    )
  } else if (filter === 'free') {
    components = allComponents?.filter(
      (component: any) => component.is_pro === false
    )
  }
  // For 'all', no filtering is applied

  if (error) {
    console.error('Error fetching components:', error)
    return (
      <div className="flex h-56 min-h-screen w-full flex-1 items-start justify-center p-4">
        <NotFound text="Something Went Wrong" />
      </div>
    )
  }

  // Create filter options for this category
  const filters = [
    { label: 'All', value: 'all', href: `/templates/${cat}` },
    { label: 'Pro', value: 'pro', href: `/templates/${cat}?filter=pro` },
    { label: 'Free', value: 'free', href: `/templates/${cat}?filter=free` },
  ]

  // Hide filters for pro and free categories since they're already filtered
  const shouldHideFilters = cat === 'pro' || cat === 'free'

  return (
    <Suspense fallback={<LoadingGridForTemplate />}>
      <div className="min-h-screen w-full">
        <TemplateFilterHeader
          title={categoryTitle}
          description={categoryDescription}
          image={category?.icon || undefined}
          currentFilter={filter}
          filters={filters}
          basePath={`/templates/${cat}`}
          hideFilters={shouldHideFilters}
        />
        {components?.length ? (
          <ComponentGridCustom
            components={components as ComponentWithCategory[] | []}
            isTemplate
          />
        ) : (
          <div className="flex h-56 min-h-screen w-full flex-1 items-start justify-center p-4">
            <NotFound
              text={
                cat === 'pro'
                  ? 'No Premium Templates Found'
                  : cat === 'free'
                    ? 'No Free Templates Found'
                    : 'No Templates Found'
              }
            />
          </div>
        )}
      </div>
    </Suspense>
  )
}
