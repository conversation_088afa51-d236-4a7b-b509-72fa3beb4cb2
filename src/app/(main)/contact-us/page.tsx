'use client'
import emailjs, { EmailJSResponseStatus } from '@emailjs/browser'
import { zodResolver } from '@hookform/resolvers/zod'
import { motion } from 'framer-motion'
import { Mail, MapPin } from 'lucide-react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

import Heading from '@/components/custom/heading'
import MaxWidthWrapper from '@/components/global/max-width-wrapper'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'

const formSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  email: z.string().email({ message: 'Invalid email address' }),
  subject: z.string().min(1, { message: 'Subject is required' }),
  message: z.string().min(1, { message: 'Message is required' }),
})

function Contact() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      subject: '',
      message: '',
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      await emailjs.send(
        process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID!,
        process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID!,
        values,
        { publicKey: process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY }
      )

      toast.success('Message sent successfully!', {
        description: "We'll get back to you as soon as possible.",
      })
      form.reset()
    } catch (error) {
      if (error instanceof EmailJSResponseStatus) {
        console.log('EMAILJS FAILED...', error)
        return
      }
      console.error('Error sending email:', error)
      toast.error('Failed to send message. Please try again later.')
    }
  }

  return (
    <MaxWidthWrapper>
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="container mx-auto mt-24 min-h-screen w-full p-4 md:px-0"
      >
        {/* Background Image */}
        <div
          className="absolute inset-0 -z-10 bg-contain bg-center bg-no-repeat"
          style={{
            backgroundImage:
              'url(https://gallery.theportfolio.in/background-images/1750953291767-section-bg-2.webp)',
          }}
        />

        {/* Clean overlay for better readability */}
        <div className="absolute inset-0 -z-10 bg-black/70" />

        <div className="mb-10 w-full text-center">
          <Heading className="pb-0">Contact Us</Heading>
          <p className="text-sm text-muted-foreground">
            We&apos;d love to talk about how we can help you.
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 md:px-5">
          <motion.div
            className="w-full space-y-6"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="border border-card bg-card">
              <CardHeader>
                <CardTitle className="font-heading text-xl md:text-3xl">
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-blue-600" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-blue-600" />
                  <span>
                    Saurabh Kumar, 514, Hazipur, Sector 104, Noida, Uttar
                    Pradesh - 201301
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card className="border border-card bg-card">
              <CardHeader>
                <CardTitle className="font-heading text-xl md:text-3xl">
                  Office Hours
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <p>Monday - Friday: 9AM - 5PM</p>
                <p>Saturday: 10AM - 2PM</p>
                <p>Sunday: Closed</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            className="w-full"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Card className="bg-card">
              <CardHeader>
                <CardTitle>Send Us a Message</CardTitle>
                <CardDescription>
                  Fill out the form below and we&apos;ll get back to you as soon
                  as possible.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className="space-y-6"
                  >
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Name</FormLabel>
                          <FormControl>
                            <Input placeholder="Your name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="Your email"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="subject"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Subject</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Subject of your message"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="message"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Message</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Your message here..."
                              className="h-32 resize-none"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <Button type="submit" className="w-full">
                      {form.formState.isSubmitting
                        ? 'Sending...'
                        : 'Send Message'}
                    </Button>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </motion.section>
    </MaxWidthWrapper>
  )
}

export default Contact
