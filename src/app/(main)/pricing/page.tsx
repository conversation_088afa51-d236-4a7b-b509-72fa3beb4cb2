import { fetchSubscriptionPlans } from '@/actions/subscriptions'
import CheckoutDialog from '@/components/custom/checkout-dialog'
import { LifetimeDealSection } from '@/components/custom/lifetime-deal-section'
import PricingCards from '@/components/custom/pricing-cards'
import AnimationContainer from '@/components/global/animation-container'
import MaxWidthWrapper from '@/components/global/max-width-wrapper'
import MagicBadge from '@/components/ui/magic-badge'

// Add this to make the page static
export const dynamic = 'force-static'
export const revalidate = 1 // revalidate every hour

export default async function PricingPage() {
  const data = await fetchSubscriptionPlans()


  // const endDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)


  // const { data: user } = await supabase.auth.getUser()

  return (
    <>
      <MaxWidthWrapper className="py-16 md:px-0 lg:px-0 lg:py-20">
        <AnimationContainer delay={0.1}>
          <div className="mx-auto flex w-full max-w-xl flex-col items-center justify-center py-8 lg:items-center">
            <MagicBadge title="Simple Pricing" />
            <h2 className="mt-6 text-center font-heading text-3xl font-medium !leading-[1.1] text-foreground md:text-5xl lg:text-center">
              Choose a plan that works for you
            </h2>
            <p className="mt-4 max-w-lg text-center text-lg text-muted-foreground lg:text-center">
              🎉 Early Access Special: Celebrate Our Launch with 40% OFF!
            </p>
          </div>
        </AnimationContainer>

        <AnimationContainer delay={0.15}>
          <LifetimeDealSection />
        </AnimationContainer>

        <AnimationContainer delay={0.2} className="min-h-[650px]">
          <PricingCards data={data} />
        </AnimationContainer>

        {/* use earlybird coupon for discount */}
        {/* <AnimationContainer delay={0.3} className="mt-16 lg:mt-24">
          <div className=" mb-3 text-center">
            <p className="text-2xl text-primary-foreground md:text-5xl">
              Use code{' '}
              <span className="bg-gradient-to-r from-purple-400 to-pink-600 bg-clip-text text-5xl font-extrabold text-transparent">
                EARLYBIRD
              </span>{' '}
              for 40% off!
            </p>
          </div>
          <DiscountTimer endDate={endDate} discountPercentage={40} />
        </AnimationContainer> */}
      </MaxWidthWrapper>
      <CheckoutDialog />
    </>
  )
}
