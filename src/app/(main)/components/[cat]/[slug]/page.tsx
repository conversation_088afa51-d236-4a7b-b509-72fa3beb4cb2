import { Metadata } from 'next'

import {
  fetchComponentBySlug,
  fetchComponentMetadata,
} from '@/actions/server-components'
import MainCardLikeAndCopyButton from '@/components/cards/interactive-card-buttons'
import { MainCard } from '@/components/cards/main-card'
import NotFound from '@/components/custom/not-found'
import { Badge } from '@/components/ui/badge'
import { Heading } from '@/components/ui/heading'
import { Separator } from '@/components/ui/separator'
import { ComponentWithCategory } from '@/types/custom'
import { generateMetadata as generateBaseMetadata } from '@/utils'

export async function generateMetadata({
  params,
}: {
  params: { cat: string; slug: string }
}): Promise<Metadata> {
  const { component, error } = await fetchComponentMetadata(params.slug)

  if (error || !component) {
    return generateBaseMetadata({
      title: 'Component Not Found - CopyElement',
      description: 'The requested component could not be found.',
      noIndex: true,
    })
  }

  return generateBaseMetadata({
    title: `${component.name} - Elementor Component | CopyElement`,
    description:
      component.meta_desc ||
      component.description ||
      `Copy and use the ${component.name} component in your Elementor website.`,
    image: component.image_url || '/thumbnail.png',
  })
}

// Add this to make the page static
export const dynamic = 'force-static'
export const revalidate = 86400 // revalidate every day

export default async function Page({
  params,
}: {
  params: { cat: string; slug: string }
}) {
  const { component, error } = await fetchComponentBySlug(params.slug)

  if (error) {
    console.error('Error fetching component:', error)
    return (
      <div className="flex min-h-[50vh] items-center justify-center">
        <NotFound text="Something Went Wrong" />
      </div>
    )
  }

  if (!component) {
    return (
      <div className="flex min-h-[50vh] items-center justify-center">
        <NotFound />
      </div>
    )
  }

  const tags = component.tags?.split(',').map((tag) => tag.trim()) || []

  return (
    <section className="container mx-auto min-h-screen max-w-7xl px-4 py-6">
      <div className="grid grid-cols-1 gap-6">
        {/* Main Component Card */}
        <div className="overflow-hidden rounded-xl border bg-card shadow-sm">
          <MainCard
            component={component as ComponentWithCategory}
            disableNavigation
          />
        </div>

        {/* Component Details */}
        <div className="rounded-xl border bg-card p-6">
          <Heading
            title={component.name}
            description={component.description ?? ''}
          />
          <Separator className="my-4" />

          {/* Tags */}
          {tags.length > 0 && (
            <div className="mb-6">
              <h3 className="mb-3 text-sm font-medium text-muted-foreground">
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge
                    key={`${component.name}-${tag}`}
                    variant="secondary"
                    className="text-xs"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="mt-6">
            <MainCardLikeAndCopyButton
              tags={component.tags ?? ''}
              id={component.id}
              is_pro={component.is_pro}
              likes={component.likes ?? 2490}
              isTemplate={false}
            />
          </div>
        </div>
      </div>
    </section>
  )
}
