import { Metadata } from 'next'
import { Suspense } from 'react'

import {
  fetchCategories,
  fetchCategoryBySlug,
} from '@/actions/server-categories'
import { fetchCategoryComponents } from '@/actions/server-components'
import NotFound from '@/components/custom/not-found'
import TemplateFilterHeader from '@/components/custom/template-filter-header'
import LoadingGridForComponent from '@/components/loaders/loading-grid1'
import { ComponentWithCategory } from '@/types/custom'
import { generateMetadata as generateBaseMetadata } from '@/utils'
import { formatTitle } from '@/utils/functions/urls'

import ComponentGridCustom from '../../../../components/custom/component-grid-custom'

// export const revalidate = 86400

export async function generateStaticParams() {
  const categories = await fetchCategoriesForPaths()

  const paths = categories.map((category) => ({
    cat: category.slug,
  }))

  paths.push({ cat: 'free' })
  paths.push({ cat: 'pro' })
  paths.push({ cat: 'popular' })

  return paths
}

async function fetchCategoriesForPaths() {
  try {
    const categories = await fetchCategories(false) // false for components
    return categories
  } catch (error) {
    console.error('Error fetching categories for paths:', error)
    return [] // Return an empty array to avoid build failures
  }
}

interface CategoryPageProps {
  params: {
    cat: string
  }
  searchParams: { filter?: string }
}

export async function generateMetadata({
  params,
}: {
  params: { cat: string }
}): Promise<Metadata> {
  const category = params.cat.charAt(0).toUpperCase() + params.cat.slice(1)

  let title = `${formatTitle(category)} Components - CopyElement`
  let description = `Explore our collection of ${params.cat} Elementor components. Find and copy the perfect ${params.cat} elements for your website.`

  // Handle special cases
  if (params.cat === 'pro') {
    title = 'Premium Components - CopyElement'
    description =
      'Access our exclusive collection of premium Elementor components. Advanced features and designs for professional websites.'
  } else if (params.cat === 'free') {
    title = 'Free Components - CopyElement'
    description =
      'Browse our free Elementor components. High-quality website elements available at no cost.'
  }

  return generateBaseMetadata({
    title,
    description,
  })
}

export default async function ComponentCategoryPage({
  params,
  searchParams,
}: CategoryPageProps) {
  const { cat } = params
  const filter = searchParams?.filter || 'all'

  // Fetch category details for special categories or actual categories
  let category = null
  let categoryTitle = formatTitle(cat)
  let categoryDescription = `Explore our collection of ${cat} Elementor components. Find and copy the perfect ${cat} elements for your website.`

  // Handle special cases
  if (cat === 'pro') {
    categoryTitle = 'Premium Components'
    categoryDescription =
      'Access our exclusive collection of premium Elementor components. Advanced features and designs for professional websites.'
  } else if (cat === 'free') {
    categoryTitle = 'Free Components'
    categoryDescription =
      'Browse our free Elementor components. High-quality website elements available at no cost.'
  } else if (cat === 'popular') {
    categoryTitle = 'Popular Components'
    categoryDescription =
      'Discover our most popular Elementor components. Trending designs loved by the community.'
  } else {
    // Fetch actual category details
    category = await fetchCategoryBySlug(cat)
    if (category) {
      categoryTitle = category.name || formatTitle(cat)
      categoryDescription = category.description || categoryDescription
    }
  }

  const data = await fetchCategoryComponents(cat)
  const allComponents = data?.components || []
  const error = data?.error || null

  // Filter components based on the filter parameter
  let components = allComponents
  if (filter === 'pro') {
    components = allComponents?.filter(
      (component: any) => component.is_pro === true
    )
  } else if (filter === 'free') {
    components = allComponents?.filter(
      (component: any) => component.is_pro === false
    )
  }
  // For 'all', no filtering is applied

  if (error) {
    console.error('Error fetching components:', error)
    return (
      <div className="flex h-56 min-h-screen w-full flex-1 items-start justify-center p-4">
        <NotFound text="Something Went Wrong" />
      </div>
    )
  }

  // Create filter options for this category
  const filters = [
    { label: 'All', value: 'all', href: `/components/${cat}` },
    { label: 'Pro', value: 'pro', href: `/components/${cat}?filter=pro` },
    { label: 'Free', value: 'free', href: `/components/${cat}?filter=free` },
  ]

  // Hide filters for pro and free categories since they're already filtered
  const shouldHideFilters = cat === 'pro' || cat === 'free'

  return (
    <Suspense fallback={<LoadingGridForComponent />}>
      <div className="min-h-screen w-full">
        <TemplateFilterHeader
          title={categoryTitle}
          description={categoryDescription}
          image={category?.icon || undefined}
          currentFilter={filter}
          filters={filters}
          basePath={`/components/${cat}`}
          hideFilters={shouldHideFilters}
        />
        {components?.length ? (
          <ComponentGridCustom
            components={components as ComponentWithCategory[] | []}
          />
        ) : (
          <div className="flex h-56 min-h-screen w-full flex-1 items-start justify-center p-4">
            <NotFound
              text={
                cat === 'pro'
                  ? 'No Premium Components Found'
                  : cat === 'free'
                    ? 'No Free Components Found'
                    : 'No Components Found'
              }
            />
          </div>
        )}
      </div>
    </Suspense>
  )
}
