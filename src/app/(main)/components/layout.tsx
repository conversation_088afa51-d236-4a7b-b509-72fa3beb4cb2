import Link from 'next/link'

import ComponentSidebar from '../../../components/component-sidebar'
import { ComponentHeader } from '../../../components/custom/component-header'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    // <main className="flex h-screen overflow-hidden">
    <div className="relative h-full overflow-hidden bg-background">
      <ComponentSidebar isTemplate={false} />
      {/* <main className="flex-1 overflow-hidden pt-16"> */}
      {/* <Header /> */}
      <main
        id="content"
        className={`overflow-x-hidden pt-12 transition-[margin] md:overflow-y-hidden md:pt-0 ${
          false ? 'md:ml-14' : 'md:ml-64'
        } h-full`}
      >
        <div className="relative mx-4 mt-3 ">
          <ComponentHeader isTemplate={false} />
        </div>
        <div className="relative min-h-screen  p-3 md:p-4">
          {children}
          {/* <Footer /> */}
          <footer className="mt-10 flex items-center justify-center rounded-2xl border border-gray-700 p-4 text-center">
            <p className="flex flex-col gap-2 text-center text-sm text-slate-600 dark:text-slate-500 sm:flex-row md:text-start ">
              © Copyright {new Date()?.getFullYear()}, All Rights Reserved by{' '}
              <Link
                href={`/`}
                className="cursor-pointer text-slate-900 underline dark:text-slate-100"
              >
                {' '}
                CopyElement{' '}
              </Link>
            </p>
          </footer>
        </div>
      </main>
    </div>
  )
}
