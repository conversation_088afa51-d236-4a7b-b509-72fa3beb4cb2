import { Metadata } from 'next'
import { Suspense } from 'react'

import { fetchAllComponents } from '@/actions/server-components'
import NotFound from '@/components/custom/not-found'
import TemplateFilterHeader from '@/components/custom/template-filter-header'
import LoadingGridForComponent from '@/components/loaders/loading-grid1'
import { ComponentWithCategory } from '@/types/custom'
import { generateMetadata } from '@/utils'

import ComponentGridCustom from '../../../components/custom/component-grid-custom'

export const metadata: Metadata = generateMetadata({
  title: 'All Components - CopyElement',
  description:
    'Browse our extensive collection of Elementor components. Find the perfect elements for your website and copy them with a single click.',
})

// Keep the revalidate export if you want to revalidate the page periodically
export const revalidate = 86400
export const dynamic = 'force-static'

interface ComponentPageProps {
  searchParams: { filter?: string }
}

export default async function ComponentsPage({
  searchParams,
}: ComponentPageProps) {
  const filter = searchParams?.filter || 'all'
  const data = await fetchAllComponents()
  const allComponents = data?.components || []
  const error = data?.error

  // Filter components based on the filter parameter
  let components = allComponents
  if (filter === 'pro') {
    components = allComponents?.filter(
      (component: any) => component.is_pro === true
    )
  } else if (filter === 'free') {
    components = allComponents?.filter(
      (component: any) => component.is_pro === false
    )
  }

  if (error) {
    console.error('Error fetching components:', error)
    return (
      <div className="flex h-56 min-h-screen w-full flex-1 items-start justify-center p-4">
        <NotFound text="Something Went Wrong" />
      </div>
    )
  }

  return (
    <Suspense fallback={<LoadingGridForComponent />}>
      <div className="min-h-screen w-full">
        <TemplateFilterHeader
          title="Components"
          description="Discover our comprehensive collection of Elementor components. High-quality, customizable elements designed to enhance your website's functionality and visual appeal."
          currentFilter={filter}
          basePath="/components"
        />
        {components?.length ? (
          <ComponentGridCustom
            components={components as ComponentWithCategory[]}
          />
        ) : (
          <div className="flex h-56 min-h-screen w-full flex-1 items-start justify-center p-4">
            <NotFound text="No Components Found" />
          </div>
        )}
      </div>
    </Suspense>
  )
}
