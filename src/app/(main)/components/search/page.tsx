'use client'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'

import { searchComponents } from '@/actions/components'
import NotFound from '@/components/custom/not-found'
import { ComponentWithCategory } from '@/types/custom'

import ComponentGridCustom from '../../../../components/custom/component-grid-custom'

const SearchComponentPage = () => {
  return (
    <Suspense>
      <Components />
    </Suspense>
  )
}

const Components = () => {
  const searchParams = useSearchParams()
  const query = searchParams.get('q')

  const {
    isLoading,
    error,
    data: components,
  } = useQuery({
    queryKey: [`components-search-${query}`],
    queryFn: () => searchComponents(query ?? ''),
    // enabled: params.componentId !== 'new', // Don't fetch data if it's a new component
  })

  // console.log(components)
  return (
    <>
      <div className="min-h-screen w-full ">
        <h1 className="my-2 mb-8 w-full  border-b pb-2 text-center text-xl font-semibold leading-7 text-slate-800 dark:text-slate-100 md:mb-5 md:py-5 md:pb-6  md:text-3xl  lg:leading-9 ">
          Search Result for -{' '}
          <span className="text-purple-400">&quot; {query} &quot;</span>
        </h1>

        {isLoading && (
          <div className="flex h-64 w-full items-center justify-center">
            {/* <Loader /> */}
            loading...
          </div>
        )}

        {!isLoading && (
          <div className="h-full w-full">
            {components && components.length > 0 ? (
              <ComponentGridCustom
                components={components as ComponentWithCategory[] | []}
              />
            ) : (
              <NotFound />
            )}
          </div>
        )}
      </div>
    </>
  )
}

export default SearchComponentPage
