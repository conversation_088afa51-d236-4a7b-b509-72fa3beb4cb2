@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 99.2157%;
    --foreground: 0 0% 0%;
    --card: 0 0% 99.2157%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 98.8235%;
    --popover-foreground: 0 0% 0%;
    --primary: 257.9412 100% 60%;
    --primary-foreground: 0 0% 100%;
    --secondary: 214.2857 24.1379% 94.3137%;
    --secondary-foreground: 0 0% 3.1373%;
    --muted: 0 0% 96.0784%;
    --muted-foreground: 0 0% 32.1569%;
    --accent: 221.3793 100% 94.3137%;
    --accent-foreground: 216.3158 76% 49.0196%;
    --destructive: 358.4416 74.7573% 59.6078%;
    --destructive-foreground: 0 0% 100%;
    --border: 240 17.0732% 91.9608%;
    --input: 0 0% 92.1569%;
    --ring: 0 0% 0%;
    --chart-1: 148.0952 53.3898% 53.7255%;
    --chart-2: 257.9412 100% 60%;
    --chart-3: 24.8571 98.1308% 58.0392%;
    --chart-4: 217.0787 76.7241% 54.5098%;
    --chart-5: 0 0% 45.4902%;
    --sidebar: 210 42.8571% 97.2549%;
    --sidebar-foreground: 0 0% 0%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 92.1569%;
    --sidebar-accent-foreground: 0 0% 0%;
    --sidebar-border: 0 0% 92.1569%;
    --sidebar-ring: 0 0% 0%;
    --font-sans: Plus Jakarta Sans, sans-serif;
    --font-serif: Lora, serif;
    --font-mono: IBM Plex Mono, monospace;
    --radius: 1.4rem;
    --shadow-2xs: 0px 2px 3px 0px hsl(0 0% 0% / 0.08);
    --shadow-xs: 0px 2px 3px 0px hsl(0 0% 0% / 0.08);
    --shadow-sm: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 1px 2px -1px hsl(0 0% 0% / 0.16);
    --shadow: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 1px 2px -1px hsl(0 0% 0% / 0.16);
    --shadow-md: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 2px 4px -1px hsl(0 0% 0% / 0.16);
    --shadow-lg: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 4px 6px -1px hsl(0 0% 0% / 0.16);
    --shadow-xl: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 8px 10px -1px hsl(0 0% 0% / 0.16);
    --shadow-2xl: 0px 2px 3px 0px hsl(0 0% 0% / 0.4);
    --tracking-normal: -0.025em;
    --spacing: 0.27rem;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 94.1176%;
    --card: 0 0% 3.9216%;
    --card-foreground: 0 0% 94.1176%;
    --popover: 228 6.8493% 14.3137%;
    --popover-foreground: 0 0% 94.1176%;
    --primary: 262.7933 78.1659% 55.098%;
    --primary-foreground: 0 0% 100%;
    --secondary: 226.6667 7.6923% 22.9412%;
    --secondary-foreground: 0 0% 94.1176%;
    --muted: 240 2.7778% 14.1176%;
    --muted-foreground: 0 0% 58.8235%;
    --accent: 217.2414 32.5843% 17.451%;
    --accent-foreground: 261.0811 83.4586% 73.9216%;
    --destructive: 0 94.7712% 70%;
    --destructive-foreground: 0 0% 100%;
    --border: 240 2.439% 8.0392%;
    --input: 0 0% 23.1373%;
    --ring: 258 97.7011% 65.8824%;
    --chart-1: 141.8919 69.1589% 58.0392%;
    --chart-2: 257.6687 100% 68.0392%;
    --chart-3: 0 93.5484% 81.7647%;
    --chart-4: 217.5484 87.5706% 65.2941%;
    --chart-5: 0 0% 62.7451%;
    --sidebar: 0 0% 3.9216%;
    --sidebar-foreground: 0 0% 94.1176%;
    --sidebar-primary: 262.7933 78.1659% 55.098%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 226.6667 9.6774% 18.2353%;
    --sidebar-accent-foreground: 263.1579 65.5172% 82.9412%;
    --sidebar-border: 222.8571 6.422% 21.3725%;
    --sidebar-ring: 262.7933 78.1659% 55.098%;
    --font-sans: Plus Jakarta Sans, sans-serif;
    --font-serif: Lora, serif;
    --font-mono: IBM Plex Mono, monospace;
    --radius: 1.4rem;
    --shadow-2xs: 0px 2px 3px 0px hsl(0 0% 0% / 0.08);
    --shadow-xs: 0px 2px 3px 0px hsl(0 0% 0% / 0.08);
    --shadow-sm: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 1px 2px -1px hsl(0 0% 0% / 0.16);
    --shadow: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 1px 2px -1px hsl(0 0% 0% / 0.16);
    --shadow-md: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 2px 4px -1px hsl(0 0% 0% / 0.16);
    --shadow-lg: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 4px 6px -1px hsl(0 0% 0% / 0.16);
    --shadow-xl: 0px 2px 3px 0px hsl(0 0% 0% / 0.16),
      0px 8px 10px -1px hsl(0 0% 0% / 0.16);
    --shadow-2xl: 0px 2px 3px 0px hsl(0 0% 0% / 0.4);
  }

  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply box-border bg-background text-foreground;
  }
}

@layer utilities {
  /* Grid Pattern Background */
  .bg-grid-white {
    background-image: linear-gradient(
        rgba(255, 255, 255, 0.1) 1px,
        transparent 1px
      ),
      linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  }

  .bg-grid-slate {
    background-image: linear-gradient(
        rgba(148, 163, 184, 0.1) 1px,
        transparent 1px
      ),
      linear-gradient(90deg, rgba(148, 163, 184, 0.1) 1px, transparent 1px);
  }

  .min-h-screen {
    min-height: 100vh; /* Fallback */
    min-height: 100dvh;
  }
  .h-screen {
    height: 100vh; /* Fallback */
    height: 100dvh;
  }

  @variants responsive {
    /* Hide scrollbar for Chrome, Safari, and Opera */
    .no-scrollbar::-webkit-scrollbar {
      display: none;
    }

    /* Hide scrollbar for IE, Edge, and Firefox */
    .no-scrollbar {
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
    }
  }
}

html,
body {
  font-family: 'Inter', sans-serif !important;
}

::-webkit-scrollbar {
  width: 6px;
  scrollbar-color: #262626;
}

::-webkit-scrollbar-thumb {
  background-color: #262626;
  border-radius: 3px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0);
}

::selection {
  background-color: hsl(var(--primary));
  color: hsl(var(--foreground));
}

.gradient {
  background: conic-gradient(
    from 230.29deg at 51.63% 52.16%,
    rgb(36, 0, 255) 0deg,
    rgb(0, 135, 255) 67.5deg,
    rgb(108, 39, 157) 198.75deg,
    rgb(24, 38, 163) 251.25deg,
    rgb(54, 103, 196) 301.88deg,
    rgb(105, 30, 255) 360deg
  );
}

.not-found {
  box-shadow: rgba(150, 18, 226, 0.3) 5px 5px;
}

.dumy {
  color: hsl(0 0% 98%);
  color: rgb(38, 38, 38);
  /* @apply to-purple-500 */
}

/* Custom animations for profile redesign */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes tilt {
  0%,
  50%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(1deg);
  }
  75% {
    transform: rotate(-1deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-tilt {
  animation: tilt 10s infinite linear;
}
