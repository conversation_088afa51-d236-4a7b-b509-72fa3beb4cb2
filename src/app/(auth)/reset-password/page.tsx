import Image from 'next/image'

import Heading from '@/components/custom/heading'
import ResetPasswordForm from '@/components/forms/reset-password-form'

const ResetPasswordPage = () => {
  return (
    <div className="w-full  space-y-8 rounded-xl bg-card p-4 backdrop-blur-lg md:p-8 lg:p-10 2xl:p-12">
      <div className="flex flex-col space-y-2 text-center">
        <Image
          src="https://utfs.io/f/LhUzHyAuqPAKx520Mrc1PXGUOs3opCecDqjZnIQyYFm0xu9d"
          width={70}
          height={70}
          alt="logo"
          className="mx-auto"
        />
        <Heading className="pb-0 !text-4xl font-bold">Reset Password</Heading>
        <p className="text-sm text-muted-foreground">
          Enter your new password below.
        </p>
      </div>

      <ResetPasswordForm />
    </div>
  )
}

export default ResetPasswordPage
