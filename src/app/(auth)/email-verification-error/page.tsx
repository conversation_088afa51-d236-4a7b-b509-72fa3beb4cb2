import Image from 'next/image'
import Link from 'next/link'

import Heading from '@/components/custom/heading'
import { Button } from '@/components/ui/button'

const EmailVerificationErrorPage = () => {
  return (
    <div className="grid h-screen place-content-center p-3">
      <div className="max-w-xl space-y-3 text-center">
        <Image
          width={500}
          height={500}
          className="mx-auto h-80 w-80 rounded-2xl object-cover"
          src="https://utfs.io/f/qPlpyBmwd8UNq2wHyXmwd8UNImxaXcQiRef3guFlqKoSLMAC"
          alt="Email Verification Failed"
        />

        <Heading className="pb-0 pt-4"> Email Verification Failed </Heading>

        <p className="text-center text-lg text-muted-foreground lg:text-center">
          We couldn&apos;t verify your email address. <br /> Please try again or
          contact support.
        </p>

        <div className="flex justify-center space-x-4 pt-4">
          <Button variant={'outline'} asChild>
            <Link
              prefetch={true}
              href="/"
              className="text-white hover:text-white/80"
            >
              Go Home
            </Link>
          </Button>
          <Button variant={'default'} asChild>
            <Link
              prefetch={true}
              href="/auth/register"
              className="text-white hover:text-white/80"
            >
              Sign Up Again
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}

export default EmailVerificationErrorPage
