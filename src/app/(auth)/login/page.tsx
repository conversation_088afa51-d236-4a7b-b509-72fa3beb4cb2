import Image from 'next/image'
import Link from 'next/link'

import Heading from '@/components/custom/heading'
import LoginForm from '@/components/forms/login-form-new'
import GoogleAuthButton from '@/components/google-auth-button'

const LoginPage = () => {
  return (
    <div className="w-full  space-y-8 rounded-xl bg-card p-4 backdrop-blur-lg md:p-8 lg:p-10 2xl:p-12">
      <div className="flex flex-col space-y-2 text-center">
        {/* <h1 className="text-3xl font-bold tracking-tight text-white">
              Welcome back
            </h1> */}
        <Image
          src="https://utfs.io/f/LhUzHyAuqPAKx520Mrc1PXGUOs3opCecDqjZnIQyYFm0xu9d"
          width={70}
          height={70}
          alt="logo"
          className="mx-auto"
        />
        <Heading className="pb-0 !text-4xl font-bold">Welcome back</Heading>
        <p className="text-sm text-gray-300">
          Enter your email and password to sign in to your account
        </p>
      </div>

      <LoginForm />

      <div className="flex items-center justify-center">
        <div className="h-px w-full bg-gray-300/30" />
        <span className="mx-4 text-sm text-gray-300">or</span>
        <div className="h-px w-full bg-gray-300/30" />
      </div>

      <div className="space-y-4">
        <GoogleAuthButton mode="signin" />
      </div>

      <p className="text-center text-sm text-gray-300">
        Don&apos;t have an account?{' '}
        <Link
          prefetch={true}
          href="/signup"
          className="font-semibold text-white underline underline-offset-4 hover:text-white/80"
        >
          Register
        </Link>
      </p>
    </div>
  )
}

export default LoginPage
