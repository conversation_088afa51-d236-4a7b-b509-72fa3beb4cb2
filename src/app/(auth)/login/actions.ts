'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { z } from 'zod'
import { zfd } from 'zod-form-data'

import { createClient } from '@/utils/supabase/server'

// Form validation schema using `zod`
const loginFormSchema = zfd.formData({
  email: zfd.text(z.string().email()),
  password: zfd.text(
    z.string().min(8, { message: 'Password must be at least 8 characters.' })
  ),
})

export async function handleLogin(data: FormData) {
  // Parse and validate form data
  const values = loginFormSchema.parse(data)

  const supabase = createClient()
  const { email, password } = values

  const { error, data: authData } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) {
    redirect('/error')
  }

  revalidatePath('/', 'layout')
  redirect('/')
}
