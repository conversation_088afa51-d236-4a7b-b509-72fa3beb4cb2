import { ChevronLeftCircle } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { redirect } from 'next/navigation'
import React from 'react'

import LinkItem from '@/components/LInkItem/LinkItem'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { createClient } from '@/utils/supabase/server'

type Props = {
  children: React.ReactNode
}

const AuthLayout = async ({ children }: Props) => {
  const supabase = createClient()

  //check user
  const { data } = await supabase.auth.getUser()

  const user = data?.user

  // if user redirect to home page
  if (user) {
    redirect('/')
  }

  return (
    <>
      <div className="grid min-h-screen w-full gap-5 p-2 pb-12 md:grid-cols-2 md:gap-10 md:p-4 md:pb-4">
        <div className="relative hidden overflow-hidden rounded-[20px] bg-[#3B06D2] p-4 md:block md:h-[calc(100vh_-_32px)]">
          <Button
            variant={'link'}
            className="absolute left-4 top-4 z-20"
            asChild
          >
            <LinkItem href="/">
              <Image
                // src="/icons/logo.png"
                src="https://utfs.io/f/LhUzHyAuqPAKx520Mrc1PXGUOs3opCecDqjZnIQyYFm0xu9d"
                width={145}
                height={34}
                alt="Logo"
                className="h-auto w-10 rounded-sm"
              />
            </LinkItem>
          </Button>

          <Button
            variant="outline"
            asChild
            className="absolute right-4 top-4 z-20"
          >
            <Link href="/" className={cn('text-white hover:text-white/80')}>
              <ChevronLeftCircle className="mr-2 h-4 w-4" />
              Back
            </Link>
          </Button>

          <Image
            src="https://utfs.io/f/qPlpyBmwd8UNQmhiUN5oEoxcL67v0k4aHeTGCZpY53l1iSgW"
            width={240}
            height={240}
            alt="Logo Cover Step"
            className="size-40 absolute left-0 top-0.5 md:h-auto md:w-auto"
          />
          <Image
            src="https://utfs.io/f/qPlpyBmwd8UNjhPfnZKKw8vEHUYxf9mgWpOalC0SGu2cnd3J"
            width={145}
            height={34}
            alt="Logo Cover Cartoon"
            className="absolute bottom-0 left-0 right-0 h-52 w-full md:h-96"
          />
          <div className="absolute left-1/2 top-1/4 w-full max-w-md -translate-x-1/2 space-y-3 px-3 text-center text-white">
            <h2 className="text-lg font-bold sm:text-2xl lg:text-[30px]/9">
              Welcome to CopyElement
            </h2>
            <p className="text-sm lg:text-xl/[30px]">
              Build Stunning WordPress Sites in Minutes with CopyElement&apos;s
              3500+ Elementor Components
            </p>
          </div>
        </div>

        {/* auth forms */}
        <div className="flex w-full items-center justify-center">
          {children}
        </div>
      </div>
    </>
  )
}

export default AuthLayout
