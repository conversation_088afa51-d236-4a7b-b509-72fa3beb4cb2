import Image from 'next/image'
import Link from 'next/link'

import Heading from '@/components/custom/heading'
import RegisterForm from '@/components/forms/register-form'
import GoogleAuthButton from '@/components/google-auth-button'

const SignupPage = () => {
  return (
    <div className="w-full  space-y-8 rounded-xl bg-card p-4 backdrop-blur-lg md:p-8 lg:p-10 2xl:p-12">
      <div className="flex flex-col space-y-2 text-center">
        <Image
          src="https://utfs.io/f/LhUzHyAuqPAKx520Mrc1PXGUOs3opCecDqjZnIQyYFm0xu9d"
          width={70}
          height={70}
          alt="logo"
          className="mx-auto"
        />
        <Heading className="pb-0 !text-4xl font-bold">
          Create an account
        </Heading>
      </div>

      <RegisterForm />

      <div className="flex items-center justify-center">
        <div className="h-px w-full bg-gray-300/30" />
        <span className="mx-4 text-sm text-gray-300">or</span>
        <div className="h-px w-full bg-gray-300/30" />
      </div>

      <div className="space-y-4">
        <GoogleAuthButton mode="signup" />
      </div>

      <p className="text-center text-sm text-gray-300">
        Already have an account?{' '}
        <Link
          prefetch={true}
          href="/login"
          className="font-semibold text-white underline underline-offset-4 hover:text-white/80"
        >
          Login
        </Link>
      </p>
    </div>
  )
}

export default SignupPage
