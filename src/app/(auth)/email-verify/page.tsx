'use client'

import { useQuery } from '@tanstack/react-query'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { toast } from 'sonner'

import Heading from '@/components/custom/heading'
import { Button } from '@/components/ui/button'
import { createClient } from '@/utils/supabase/client'

const EmailVerifyPage = () => {
  const router = useRouter()
  const supabase = createClient()
  const [isResending, setIsResending] = useState(false)

  // Check auth status
  const { data: session, isLoading } = useQuery({
    queryKey: ['auth-session'],
    queryFn: async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession()
      return session
    },
  })

  // Redirect if user is already verified
  useEffect(() => {
    if (session?.user?.email_confirmed_at) {
      router.replace('/')
    }
  }, [session, router])

  const handleResendEmail = async () => {
    if (!session?.user?.email) return

    setIsResending(true)
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: session.user.email,
      })

      if (error) throw error

      toast.success('Verification email resent successfully')
    } catch (error) {
      toast.error('Failed to resend verification email')
    } finally {
      setIsResending(false)
    }
  }

  if (isLoading) {
    return <div className="grid h-screen place-content-center">Loading...</div>
  }

  return (
    <div className="grid h-screen place-content-center p-3">
      <div className="max-w-xl space-y-3 text-center">
        <Image
          width={500}
          height={500}
          className="mx-auto h-80 w-80 rounded-2xl object-cover"
          src="https://utfs.io/f/qPlpyBmwd8UNnqTGiZ4QAdDWOuyip0L8ohmMxRPBzt97eJZF"
          alt="Email Verification"
        />

        <Heading className="pb-0 pt-4">Verify Your Email</Heading>

        <p className="text-center text-lg text-muted-foreground lg:text-center">
          We&apos;ve sent a verification email to
          <br />
          <span className="font-semibold text-primary">
            {session?.user?.email}
          </span>
        </p>

        <div className="space-y-2">
          <Button
            variant="outline"
            className="w-full"
            onClick={handleResendEmail}
            disabled={isResending}
          >
            {isResending ? 'Resending...' : 'Resend Verification Email'}
          </Button>

          <Button
            variant="ghost"
            className="w-full"
            onClick={() => router.push('/login')}
          >
            Back to Login
          </Button>
        </div>
      </div>
    </div>
  )
}

export default EmailVerifyPage
