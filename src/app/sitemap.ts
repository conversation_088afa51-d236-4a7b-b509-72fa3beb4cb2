import { MetadataRoute } from 'next'
import { unstable_cache } from 'next/cache'

import { createClient } from '@/utils/supabase/server'

const BASE_URL = 'https://copyelement.com'

// Define a type-safe Component structure
type Component = {
  id: string
  slug: string
  updated_at: string | null
  category: { slug: string } | null
  is_pro: boolean
  likes: number | null
}

// Fetch all components with category slugs
export const fetchAllComponents = unstable_cache(
  async () => {
    const supabase = createClient()
    try {
      const { data: components, error } = (await supabase
        .from('components')
        .select(
          'id, name, description, image_url, tags, meta_desc, is_pro, slug, likes, category:category_id(slug)'
        )
        .eq('is_active', true)
        .order('created_at', { ascending: false })) as {
        data: Component[] | null
        error: any
      }

      if (error) {
        console.error('Error fetching components:', error)
        return { components: null, error }
      }

      return { components, error }
    } catch (error) {
      console.error('Error fetching components:', error)
      return { components: null, error }
    }
  },
  ['all-components'],
  {
    tags: ['components'],
    revalidate: 86400, // Cache for 24 hours
  }
)

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const { components, error } = await fetchAllComponents()

  if (error || !components) {
    console.error('Error fetching components:', error)
    return []
  }

  // Define static routes
  const staticRoutes = [
    { url: `${BASE_URL}/`, priority: 1.0 },
    { url: `${BASE_URL}/components`, priority: 0.9 },
    { url: `${BASE_URL}/pricing`, priority: 0.8 },
    { url: `${BASE_URL}/about-us`, priority: 0.8 },
    { url: `${BASE_URL}/contact-us`, priority: 0.8 },
    { url: `${BASE_URL}/faq`, priority: 0.8 },
    { url: `${BASE_URL}/terms-and-conditions`, priority: 0.8 },
    { url: `${BASE_URL}/privacy-policy`, priority: 0.8 },
    { url: `${BASE_URL}/cancellation-and-refunds`, priority: 0.8 },
  ].map((route) => ({
    url: route.url,
    lastModified: new Date().toISOString(),
    priority: route.priority,
  }))

  // Generate component-related routes
  const componentRoutes = components.flatMap((component) => {
    const urls = [
      {
        url: `${BASE_URL}/${component.category?.slug ?? 'uncategorized'}/${
          component.slug
        }`,
        lastModified: component.updated_at
          ? new Date(component.updated_at).toISOString()
          : new Date().toISOString(),
        priority: 0.7,
      },
      {
        url: `${BASE_URL}/components/${component.slug}`,
        lastModified: component.updated_at
          ? new Date(component.updated_at).toISOString()
          : new Date().toISOString(),
        priority: 0.7,
      },
    ]

    // Add `/free/{slug}` route for non-pro components
    if (!component.is_pro) {
      urls.push({
        url: `${BASE_URL}/free/${component.slug}`,
        lastModified: component.updated_at
          ? new Date(component.updated_at).toISOString()
          : new Date().toISOString(),
        priority: 0.6,
      })
    }

    // Add `/popular/{slug}` route if likes > 100
    if (component.likes && component.likes > 100) {
      urls.push({
        url: `${BASE_URL}/popular/${component.slug}`,
        lastModified: component.updated_at
          ? new Date(component.updated_at).toISOString()
          : new Date().toISOString(),
        priority: 0.6,
      })
    }

    return urls
  })

  return [...staticRoutes, ...componentRoutes]
}
