import { NextRequest, NextResponse } from 'next/server'

import {
  applyDiscount,
  CreateSupabaseOrder,
  generateOrderId,
} from '@/utils/helpers'
import { PayPalPayment } from '@/utils/payment/paypal'
import { PhonePePayment } from '@/utils/payment/phonepe'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    console.log('Starting order creation process...')

    const supabase = createClient()
    const { data: userResult, error: userError } = await supabase.auth.getUser()

    if (userError || !userResult) {
      console.error('User error:', userError)
      throw new Error('User not found.')
    }

    const user = userResult.user
    const { subscription_plan_id, coupon_code, total_amount, payment_option } =
      await request.json()

    const currency = payment_option === 'phonepe' ? 'INR' : 'USD'

    const { data: subscriptionPlan, error: planError } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('id', subscription_plan_id)
      .single()

    if (planError || !subscriptionPlan) {
      console.error('Subscription plan error:', planError)
      throw new Error('Invalid subscription plan.')
    }

    let coupon
    let amount = subscriptionPlan.unit_amount

    if (coupon_code) {
      const { data: couponData, error: couponError } = await supabase
        .from('coupons')
        .select('*')
        .eq('code', coupon_code)
        .single()

      if (couponError || !couponData) {
        console.error('Coupon error:', couponError)
        throw new Error('Invalid coupon.')
      }

      coupon = couponData

      console.log('Coupon found:', coupon)

      if (coupon.limit !== null && coupon.limit <= 0) {
        throw new Error('Coupon limit exceeded.')
      }

      ;({ amount, coupon } = await applyDiscount(amount, coupon_code))
    }

    const actualAmountAfterDiscount = Math.floor(amount / 100)

    if (actualAmountAfterDiscount === 0) {
      console.log('100% discount applied. Completing order without payment.')
      const order_id = generateOrderId(
        subscriptionPlan.plan_type ?? '',
        user.email ?? ''
      )

      const order = await CreateSupabaseOrder(
        order_id,
        subscriptionPlan.id,
        user.id,
        0,
        'FREE',
        currency,
        coupon?.id
      )

      // Update user profile with is_pro flag
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ is_pro: true })
        .eq('id', user.id)

      if (updateError) {
        console.error('Profile update error:', updateError)
      }

      // Decrease the coupon limit if applicable
      if (coupon && coupon.limit !== null) {
        const { error: couponUpdateError } = await supabase
          .from('coupons')
          .update({ limit: coupon.limit - 1 })
          .eq('id', coupon.id)

        if (couponUpdateError) {
          console.error('Coupon update error:', couponUpdateError)
        }
      }

      return NextResponse.json(
        {
          message: 'Order completed successfully with 100% discount.',
          order_id: order.id,
        },
        { status: 200 }
      )
    }

    const order_id = generateOrderId(
      subscriptionPlan.plan_type ?? '',
      user.email ?? ''
    )
    const totalAmount =
      total_amount > actualAmountAfterDiscount
        ? total_amount
        : actualAmountAfterDiscount

    const order = await CreateSupabaseOrder(
      order_id,
      subscriptionPlan.id,
      user.id,
      totalAmount,
      payment_option,
      currency,
      coupon?.id
    )

    let paymentOrder

    if (payment_option === 'phonepe') {
      paymentOrder = await PhonePePayment.createOrder(
        totalAmount,
        user.id,
        order_id
      )
    } else if (payment_option === 'paypal') {
      paymentOrder = await PayPalPayment.createOrder(
        totalAmount,
        currency,
        order_id
      )
    } else {
      throw new Error('Unsupported payment option.')
    }

    if (paymentOrder) {
      const { data: paymentResult, error: paymentError } = await supabase
        .from('payments')
        .insert({
          order_id: order_id,
          payment_id:
            paymentOrder?.id || paymentOrder?.data.merchantTransactionId,
          payment_status: 'PENDING',
        })
        .select('id')
        .single()

      if (paymentError || !paymentResult) {
        console.error('Supabase payment error:', paymentError)
      }

      const { error: updateError } = await supabase
        .from('orders')
        .update({ payment_id: paymentResult?.id })
        .eq('id', order.id)

      if (updateError) {
        console.error('Order update error:', updateError)
      }

      // Decrease the coupon limit if applicable
      if (coupon && coupon.limit !== null) {
        const { error: couponUpdateError } = await supabase
          .from('coupons')
          .update({ limit: coupon.limit - 1 })
          .eq('id', coupon.id)

        if (couponUpdateError) {
          console.error('Coupon update error:', couponUpdateError)
        }
      }
    }

    return NextResponse.json(
      {
        payment_order: paymentOrder,
        payment_transaction_id:
          paymentOrder?.id || paymentOrder?.data.merchantTransactionId,
        amount: amount,
        currency: currency,
        redirectUrl:
          paymentOrder.links?.find(
            (link: { rel: string }) => link.rel === 'approve'
          )?.href || paymentOrder.data?.instrumentResponse.redirectInfo.url,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error('Order creation failed:', error)
    return NextResponse.json(
      {
        error: 'Failed to create order',
        details: (error as Error).message,
      },
      { status: 500 }
    )
  }
}
