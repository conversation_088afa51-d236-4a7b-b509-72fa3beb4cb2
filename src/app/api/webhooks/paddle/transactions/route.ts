// route.ts
import { EventName, Paddle } from '@paddle/paddle-node-sdk'
import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

import {
  handleTransactionCanceled,
  handleTransactionCompleted,
  handleTransactionCreated,
  handleTransactionPaid,
  handleTransactionPastDue,
  handleTransactionPaymentFailed,
} from '@/lib/paddle/events/transaction-events'
import { getSubscriptionPlan } from '@/lib/paddle/helpers/subscription-helpers'
import { parseTransactionEvent } from '@/lib/paddle/utils/parse-events'

const paddle = new Paddle(process.env.PADDLE_API_KEY!)

function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

  if (!serviceRoleKey) {
    throw new Error(
      'SUPABASE_SERVICE_ROLE_KEY is required for webhook operations'
    )
  }

  return createSupabaseClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  })
}

export async function POST(request: NextRequest) {
  console.log('Received Paddle transaction webhook request')
  try {
    const signature = request.headers.get('paddle-signature') || ''
    const rawRequestBody = await request.text()
    const secretKey = process.env.PADDLE_TRANSACTION_WEBHOOK_SECRET_KEY || ''

    if (!signature || !rawRequestBody) {
      return NextResponse.json(
        { error: 'Signature missing in header or empty body' },
        { status: 400 }
      )
    }

    if (!secretKey) {
      return NextResponse.json(
        { error: 'Webhook secret key not configured' },
        { status: 500 }
      )
    }

    const eventData = await paddle.webhooks.unmarshal(
      rawRequestBody,
      secretKey,
      signature
    )

    console.log(' transaction event:', eventData)
    const parsedTransaction = parseTransactionEvent(eventData)
    const supabase = createServiceRoleClient()
    console.log('Parsed transaction:', parsedTransaction)

    if (!parsedTransaction.userId) {
      console.error('Missing userId in custom_data:', parsedTransaction)
      throw new Error('Missing userId in custom_data')
    }

    const plan = await getSubscriptionPlan(supabase, parsedTransaction.priceId)
    console.log('Fetched subscription plan:', plan)

    if (!plan) {
      throw new Error('Invalid or missing subscription plan')
    }

    const handlerPayload = {
      transaction: parsedTransaction,
      plan,
      supabase,
    }

    switch (eventData.eventType) {
      case EventName.TransactionCreated:
        await handleTransactionCreated(handlerPayload)
        break
      case EventName.TransactionCompleted:
        await handleTransactionCompleted(handlerPayload)
        break
      case EventName.TransactionPaid:
        await handleTransactionPaid(handlerPayload)
        break
      case EventName.TransactionPaymentFailed:
        await handleTransactionPaymentFailed(handlerPayload)
        break
      case EventName.TransactionCanceled:
        await handleTransactionCanceled(handlerPayload)
        break
      case EventName.TransactionPastDue:
        await handleTransactionPastDue(handlerPayload)
        break
      default:
        console.log(`Ignoring unsupported event type: ${eventData.eventType}`)
    }

    return NextResponse.json({
      message: 'Transaction webhook processed successfully',
      eventType: eventData.eventType,
      eventId: eventData.eventId,
    })
  } catch (error) {
    console.error('Error processing Paddle transaction webhook:', error)
    return NextResponse.json(
      {
        error: 'Failed to process transaction webhook',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
