// route.ts
import { EventName, Paddle } from '@paddle/paddle-node-sdk'
import { createClient as createSupabaseClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

import {
  handleSubscriptionActivated,
  handleSubscriptionCanceled,
  handleSubscriptionCreated,
  handleSubscriptionPastDue,
  handleSubscriptionPaused,
  handleSubscriptionResumed,
  handleSubscriptionUpdated,
} from '@/lib/paddle/events/subscription-events'
import { getSubscriptionPlan } from '@/lib/paddle/helpers/subscription-helpers'
import { parseSubscriptionEvent } from '@/lib/paddle/utils/parse-events'

const paddle = new Paddle(process.env.PADDLE_API_KEY!)

function createServiceRoleClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

  if (!serviceRoleKey) {
    throw new Error(
      'SUPABASE_SERVICE_ROLE_KEY is required for webhook operations'
    )
  }

  return createSupabaseClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  })
}

export async function POST(request: NextRequest) {
  console.log('Received Paddle subscription webhook request')

  try {
    const signature = request.headers.get('paddle-signature') || ''
    const rawRequestBody = await request.text()
    const secretKey = process.env.PADDLE_SUBSCRIPTION_WEBHOOK_SECRET_KEY || ''

    if (!signature || !rawRequestBody) {
      return NextResponse.json(
        { error: 'Missing signature or body' },
        { status: 400 }
      )
    }

    if (!secretKey) {
      return NextResponse.json(
        { error: 'Webhook secret key not configured' },
        { status: 500 }
      )
    }

    const eventData = await paddle.webhooks.unmarshal(
      rawRequestBody,
      secretKey,
      signature
    )

    const subscriptionEventTypes = [
      EventName.SubscriptionCreated,
      EventName.SubscriptionUpdated,
      EventName.SubscriptionCanceled,
      EventName.SubscriptionPaused,
      EventName.SubscriptionResumed,
      EventName.SubscriptionActivated,
      EventName.SubscriptionPastDue,
    ]

    if (!subscriptionEventTypes.includes(eventData.eventType)) {
      return NextResponse.json({ message: 'Ignored non-subscription event' })
    }

    console.log(' Paddle subscription event:', eventData)

    const parsedSubscription = parseSubscriptionEvent(eventData)
    const supabase = createServiceRoleClient()

    if (!parsedSubscription.userId) {
      console.error('Missing userId in custom_data:', parsedSubscription)
      throw new Error('Missing userId in custom_data')
    }

    const subscriptionPlan = await getSubscriptionPlan(
      supabase,
      parsedSubscription.priceId
    )
    if (!subscriptionPlan) {
      throw new Error('Invalid or missing subscription plan')
    }

    // Prepare handler payload object
    const handlerPayload = {
      subscription: parsedSubscription,
      plan: subscriptionPlan,
      supabase,
    }

    // Pass the payload object to each handler
    switch (eventData.eventType) {
      case EventName.SubscriptionCreated:
        await handleSubscriptionCreated(handlerPayload)
        break
      case EventName.SubscriptionUpdated:
        await handleSubscriptionUpdated(handlerPayload)
        break
      case EventName.SubscriptionCanceled:
        await handleSubscriptionCanceled(handlerPayload)
        break
      case EventName.SubscriptionPaused:
        await handleSubscriptionPaused(handlerPayload)
        break
      case EventName.SubscriptionResumed:
        await handleSubscriptionResumed(handlerPayload)
        break
      case EventName.SubscriptionActivated:
        await handleSubscriptionActivated(handlerPayload)
        break
      case EventName.SubscriptionPastDue:
        await handleSubscriptionPastDue(handlerPayload)
        break
    }

    return NextResponse.json({
      message: 'Subscription webhook processed successfully',
      eventType: eventData.eventType,
      eventId: eventData.eventId,
    })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json(
      {
        error: 'Failed to process subscription webhook',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    )
  }
}
