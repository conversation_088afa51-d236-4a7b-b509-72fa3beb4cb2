import crypto from 'crypto'

import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON><PERSON><PERSON> from 'razorpay'

import { createClient } from '@/utils/supabase/server'

const razorpay = new Razorpay({
  key_id: process.env.RAZORPAY_KEY_ID!,
  key_secret: process.env.RAZORPAY_KEY_SECRET,
})

export async function POST(request: NextRequest): Promise<NextResponse> {
  const supabase = createClient()
  try {
    const { razorpay_order_id, razorpay_payment_id, razorpay_signature } =
      await request.json()

    const body = razorpay_order_id + '|' + razorpay_payment_id

    const expectedSignature = crypto
      .createHmac('sha256', process.env.RAZORPAY_KEY_SECRET!)
      .update(body.toString())
      .digest('hex')

    const isAuthentic = expectedSignature === razorpay_signature

    if (!isAuthentic) {
      return NextResponse.json(
        { success: false, error: 'Invalid signature' },
        { status: 400 }
      )
    }

    // Fetch the order details
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .select('*')
      .eq('order_id', razorpay_order_id)
      .single()

    if (orderError) {
      throw new Error(`Failed to fetch order: ${orderError.message}`)
    }

    // Update the order status
    const { error: updateOrderError } = await supabase
      .from('orders')
      .update({ status: 'paid' })
      .eq('razorpay_order_id', razorpay_order_id)

    if (updateOrderError) {
      throw new Error(
        `Failed to update order status: ${updateOrderError.message}`
      )
    }

    // Insert the payment details
    const { error: paymentError } = await supabase.from('payments').insert({
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      payment_status: 'success',
    })

    if (paymentError) {
      throw new Error(
        `Failed to insert payment details: ${paymentError.message}`
      )
    }

    return NextResponse.json({ success: true }, { status: 200 })
  } catch (error) {
    console.error('Error verifying payment:', error)
    if (error instanceof Error) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 500 }
      )
    }
    return NextResponse.json(
      { success: false, error: 'An unexpected error occurred' },
      { status: 500 }
    )
  }
}
