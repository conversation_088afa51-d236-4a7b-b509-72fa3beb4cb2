import { NextRequest, NextResponse } from 'next/server'

import {
  createPaddleCheckout,
  createPaddleCustomer,
} from '@/lib/paddle/paddle-client'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const {
      priceId,
      email,
      name,
      userId,
      customData = {},
      discountId,
    } = await request.json()

    if (!priceId || !email) {
      return NextResponse.json(
        { error: 'Price ID and email are required' },
        { status: 400 }
      )
    }

    const supabase = createClient()

    // Get user from Supabase if userId provided
    let userProfile = null
    if (userId) {
      const { data } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
      userProfile = data
    }

    // Create or find Paddle customer
    let customerId: string | undefined

    try {
      const customer = await createPaddleCustomer({
        email,
        name:
          name ||
          (userProfile
            ? `${userProfile.first_name} ${userProfile.last_name}`.trim()
            : undefined),
        customData: {
          userId: userId || userProfile?.id,
          email,
          ...customData,
        },
      })
      customerId = customer.id
    } catch (customerError) {
      console.warn(
        'Customer creation failed, proceeding without customer ID:',
        customerError
      )
    }

    // Create checkout session
    const checkout = await createPaddleCheckout({
      priceId,
      customerId,
      customData: {
        userId: userId || userProfile?.id,
        email,
        ...customData,
      },
      successUrl: `${process.env.NEXT_PUBLIC_BASE_URL}/payment-success`,
      discountId,
    })

    return NextResponse.json({
      transaction: checkout,
      transactionId: checkout.id,
      // Note: You may need to create a separate checkout URL using Paddle's frontend SDK
      // or handle the payment flow differently based on your Paddle setup
      message: 'Transaction created successfully',
    })
  } catch (error) {
    console.error('Error creating Paddle checkout:', error)
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    )
  }
}
