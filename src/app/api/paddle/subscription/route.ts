import { NextRequest, NextResponse } from 'next/server'

import {
  cancelPaddleSubscription,
  getPaddleSubscription,
  pausePaddleSubscription,
  resumePaddleSubscription,
} from '@/lib/paddle/paddle-client'
import { createClient } from '@/utils/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const { action, subscriptionId, userId } = await request.json()

    if (!action || !subscriptionId) {
      return NextResponse.json(
        { error: 'Action and subscription ID are required' },
        { status: 400 }
      )
    }

    const supabase = createClient()

    // Verify user owns this subscription
    if (userId) {
      const { data: order } = await supabase
        .from('orders')
        .select('*')
        .eq('order_id', `paddle_${subscriptionId}`)
        .eq('user_id', userId)
        .single()

      if (!order) {
        return NextResponse.json(
          { error: 'Subscription not found for this user' },
          { status: 404 }
        )
      }
    }

    let result
    switch (action) {
      case 'cancel':
        result = await cancelPaddleSubscription(subscriptionId)
        break
      case 'pause':
        result = await pausePaddleSubscription(subscriptionId)
        break
      case 'resume':
        result = await resumePaddleSubscription(subscriptionId)
        break
      case 'get':
        result = await getPaddleSubscription(subscriptionId)
        break
      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: cancel, pause, resume, or get' },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success: true,
      subscription: result,
      message: `Subscription ${action} successful`,
    })
  } catch (error) {
    console.error('Error managing subscription:', error)
    return NextResponse.json(
      { error: 'Failed to manage subscription' },
      { status: 500 }
    )
  }
}

// GET method to retrieve subscription details
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const subscriptionId = searchParams.get('subscriptionId')
    const userId = searchParams.get('userId')

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      )
    }

    const supabase = createClient()

    // Verify user owns this subscription if userId provided
    if (userId) {
      const { data: order } = await supabase
        .from('orders')
        .select('*')
        .eq('order_id', `paddle_${subscriptionId}`)
        .eq('user_id', userId)
        .single()

      if (!order) {
        return NextResponse.json(
          { error: 'Subscription not found for this user' },
          { status: 404 }
        )
      }
    }

    const subscription = await getPaddleSubscription(subscriptionId)

    return NextResponse.json({
      subscription,
    })
  } catch (error) {
    console.error('Error retrieving subscription:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve subscription' },
      { status: 500 }
    )
  }
}
