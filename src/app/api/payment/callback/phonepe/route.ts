import { NextResponse, type NextRequest } from 'next/server'

import { PhonePePayment } from '@/utils/payment/phonepe'
import { createClient } from '@/utils/supabase/server'

// GET for Redirect URL and POST for Callback URL
export async function GET(request: NextRequest) {
  const supabase = createClient()
  const { searchParams } = new URL(request.url)
  const merchantTransactionId = searchParams.get('merchantTransactionId')

  try {
    if (!merchantTransactionId) {
      // return NextResponse.json({ error: 'Missing merchantId' }, { status: 400 })
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status/failed`,
        {
          status: 301,
        }
      )
    }
    // Verify the payment status with PhonePe
    const phonepeResponse = await PhonePePayment.checkStatus(
      merchantTransactionId
    )

    const { success, code, data: phonepeData } = phonepeResponse

    // Define the payment record to be created or updated
    const paymentRecord = {
      payment_method: 'PayPal', // Payment method
      payment_status: code, // Status from PhonePe (e.g., PAYMENT_SUCCESS, FAILED)
      transaction_id: phonepeData?.transactionId, // Transaction ID
    }

    // Perform an update operation to  update the payment record
    const { data, error } = await supabase
      .from('payments') // Replace with the correct table name
      .update(paymentRecord) // Upsert based on order_id
      .eq('payment_id', merchantTransactionId)

    if (error) {
      console.error('Failed to upsert payment record:', error)
      // throw new Error('Failed to upsert payment record')
    }

    if (!success || code !== 'PAYMENT_SUCCESS') {
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status/failed`,
        {
          status: 301,
        }
      )
    }

    //
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status/success`,
      {
        status: 301,
      }
    )
  } catch (error) {
    console.error('Error handling payment callback:', error)
    // return NextResponse.json(
    //   { error: 'Internal Server Error' },
    //   { status: 500 },
    // )
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status/failed`,
      { status: 301 }
    )
  }
}
