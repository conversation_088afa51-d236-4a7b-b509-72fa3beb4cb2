import { NextResponse, type NextRequest } from 'next/server'

import { PayPalPayment } from '@/utils/payment/paypal'
import { createClient } from '@/utils/supabase/server'

// Handle the payment return and cancel URLs
export async function GET(request: NextRequest) {
  const supabase = createClient()
  // console.log('callback called with payload')

  const { searchParams } = new URL(request.url)
  const token = searchParams.get('token')
  // console.log('token', token)
  const orderId = token

  try {
    // Parse the PayPal callback payload
    // const callbackPayload = await request.json()
    // console.log('callback called with payload', callbackPayload)
    // const { orderId } = callbackPayload // Ensure the payload includes `orderId`

    if (!orderId) {
      return NextResponse.json(
        { error: 'Invalid payload: missing orderId' },
        { status: 400 }
      )
    }

    // Check the order status with PayPal
    const statusResponse = await PayPalPayment.checkStatus(orderId)

    // Extract relevant data from the status response
    const {
      status,
      id: transactionId,
      purchase_units: [{ amount }],
      payer: { email_address: payerEmail },
    } = statusResponse

    // Define the payment record to be created or updated
    const paymentRecord = {
      // transactionId: orderId, // Order ID from the callback
      payment_method: 'PayPal', // Payment method
      payment_status: status, // Status from PayPal (e.g., COMPLETED, FAILED)
      transaction_id: transactionId, // Transaction ID
      // amount: parseFloat(amount.value) * 100, // Amount in cents/paise
      // currency: amount.currency_code, // Currency code (e.g., USD, INR)
      payer_email: payerEmail,
    }

    // Perform an update operation to  update the payment record
    const { data, error } = await supabase
      .from('payments') // Replace with the correct table name
      .update(paymentRecord) // Upsert based on order_id
      .eq('payment_id', orderId)

    if (error) {
      console.error('Failed to upsert payment record:', error)
      // throw new Error('Failed to upsert payment record')
    }

    if (status === 'APPROVED') {
      // NextResponse.redirect('/payment-status/success')
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status/success`,
        {
          status: 301,
        }
      )
    } else {
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status/failed`,
        {
          status: 301,
        }
      )
    }
  } catch (error) {
    console.error('Error handling PayPal callback:', error)
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_BASE_URL}/payment-status/failed`,
      {
        status: 301,
      }
    )
  }
}
