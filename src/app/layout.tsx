/* eslint-disable import/order */
import { GoogleTagManager } from '@next/third-parties/google'
import { GeistSans } from 'geist/font/sans'
import { ViewTransitions } from 'next-view-transitions'
import Script from 'next/script'
import NextTopLoader from 'nextjs-toploader'

import Footer from '@/components/Footer/footer'
import { TopBanner } from '@/components/custom/top-banner'
import { NavbarServer } from '@/components/layout/navbar-server'
import Providers from '@/providers'
import './globals.css'

import { MainJsonLd } from '@/components/json-ld'
import { aeonik, cn } from '@/utils'

const defaultUrl = 'https://copyelement.com'

export const metadata = {
  metadataBase: new URL(defaultUrl),
  title: 'CopyElement - Largest Elementor Components Library',
  description: `Build stunning WordPress sites in minutes with CopyElement's 3500+ plugin-free, copy-paste Elementor components. No extra plugins, 100% responsive, free & Pro compatible.`,
  alternates: {
    canonical: 'https://copyelement.com',
  },
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ViewTransitions>
      <html
        lang="en"
        className={GeistSans.className}
        style={{ colorScheme: 'dark' }}
      >
        <head />
        <MainJsonLd />
        <Script
          strategy="lazyOnload"
          src="https://embed.tawk.to/675b0791af5bfec1dbdb0c2f/1ietpb4d6"
        />
        <GoogleTagManager gtmId="GTM-WRBFJ3Z8" />

        <body
          className={cn(
            'dark min-h-screen overflow-x-hidden bg-background text-foreground antialiased',
            aeonik.variable
          )}
        >
          <NextTopLoader showSpinner={false} height={3} color="#8c49ff" />
          <Providers>
            {/* <SpecialNotification /> */}
            <TopBanner />
            {/* <SmoothCursor
              springConfig={{
                damping: 65,
                stiffness: 500,
                mass: 1,
                restDelta: 0.001,
              }}
            /> */}
            <NavbarServer />
            {children}
            <Footer />
          </Providers>
        </body>
      </html>
    </ViewTransitions>
  )
}
