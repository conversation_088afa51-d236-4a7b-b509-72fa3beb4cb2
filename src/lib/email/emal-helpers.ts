const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>'

export async function sendAdminEmail(subject: string, message: string) {
  try {
    // await sendEmail({
    //   to: ADMIN_EMAIL,
    //   subject: subject,
    //   html: `<p>${message}</p>`,
    // })
    return
  } catch (error) {
    console.error('Error sending admin email:', error)
    throw new Error('Failed to send admin email')
  }
}

export async function sendCustomerEmail(
  email: string,
  subject: string,
  message: string
) {
  try {
    console.log('Sending customer email:', { email, subject, message })
    // Uncomment the following lines when the email sending function is ready
    // await sendEmail({
    //   to: email,
    //   subject: subject,
    //   html: `<p>${message}</p>`,
    // })
    return
  } catch (error) {
    console.error('Error sending customer email:', error)
    throw new Error('Failed to send customer email')
  }
}
