// lib/email/mailer.ts
import { Resend } from 'resend'

interface EmailOptions {
  to: string | string[]
  subject: string
  html: string
  text?: string
}

const resend = new Resend(process.env.RESEND_API_KEY)

/**
 * Sends an email using the Resend API.
 *
 * Ensure RESEND_API_KEY and RESEND_FROM_EMAIL environment variables are set.
 * RESEND_FROM_EMAIL should be a verified domain/email in your Resend account.
 */
export async function sendEmail({ to, subject, html, text }: EmailOptions) {
  const fromEmail = process.env.RESEND_FROM_EMAIL

  if (!fromEmail) {
    console.error(
      'RESEND_FROM_EMAIL environment variable is not set. Cannot send email.'
    )
    throw new Error('RESEND_FROM_EMAIL is not configured.')
  }

  try {
    const { data, error } = await resend.emails.send({
      from: fromEmail,
      to: Array.isArray(to) ? to : [to], // Ensure 'to' is an array
      subject: subject,
      html: html,
      text: text,
    })

    if (error) {
      console.error('Error sending email with Resend:', error)
      throw new Error(`Failed to send email: ${error.message}`)
    }

    console.log('Email sent successfully:', data)
    return data
  } catch (error) {
    console.error('Failed to send email:', error)
    throw error
  }
}
