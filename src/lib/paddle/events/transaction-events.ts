import { SupabaseClient } from '@supabase/supabase-js'

import { sendAdminEmail, sendCustomerEmail } from '@/lib/email/emal-helpers'
import { updateUserProfile } from '@/lib/paddle/helpers/subscription-helpers'
import {
  createTransactionRecord,
  updateTransactionRecord,
} from '@/lib/paddle/helpers/transaction-helpers'
import type { ParsedTransaction } from '@/lib/paddle/utils/parse-events'
import type { Plan as SubscriptionPlan } from '@/types/custom'

type HandlerPayload = {
  transaction: ParsedTransaction
  plan: SubscriptionPlan
  supabase: SupabaseClient
}

function notifyAdmin(
  eventType: string,
  userId: string,
  planType: string,
  message: string
) {
  return sendAdminEmail(
    eventType,
    `${message} for user: ${userId} with plan: ${planType}`
  )
}

function notifyCustomer(
  email: string | null,
  eventType: string,
  message: string
) {
  if (!email) return Promise.resolve()
  return sendCustomerEmail(email, eventType, message)
}

export async function handleTransactionCreated({
  transaction,
  plan,
  supabase,
}: HandlerPayload) {
  const {
    id,
    userId,
    checkoutUrl,
    totalAmount,
    currency,
    status,
    totals,
    paymentDetails,
    eventId,
  } = transaction
  if (!userId) return

  await createTransactionRecord(supabase, {
    event_id: eventId,
    user_id: userId,
    subscription_id: plan.id,
    payment_id: id,
    amount: parseFloat(totalAmount),
    currency,
    status,
    amount_breakdown: totals,
    metadata: paymentDetails,
    checkout_url: checkoutUrl,
  })

  await notifyAdmin(
    'Transaction Created',
    userId,
    plan.plan_type,
    'A new transaction was created'
  )
}

export async function handleTransactionCompleted({
  transaction,
  plan,
  supabase,
}: HandlerPayload) {
  const { id, userId, email, status } = transaction
  if (!userId || !plan.id) return

  await updateTransactionRecord(supabase, id, {
    status,
  })

  console.info('plan type:', plan?.plan_type)

  if (plan.plan_type === 'lifetime') {
    console.info('Updating user profile for lifetime plan')
    await updateUserProfile(supabase, userId, true, plan.plan_type)
    await notifyCustomer(
      email,
      'Lifetime Plan Activated',
      'Your lifetime plan is now active!'
    )
  } else {
    await notifyCustomer(
      email,
      'Payment Successful',
      'Your payment was successfully processed.'
    )
  }

  await notifyAdmin(
    'Transaction Completed',
    userId,
    plan.plan_type,
    'Transaction completed'
  )
}

export async function handleTransactionPaid({
  transaction,
  plan,
  supabase,
}: HandlerPayload) {
  const { userId, id, email, status } = transaction

  if (!userId || !plan.id) return

  await updateTransactionRecord(supabase, id, {
    status,
  })

  await notifyAdmin(
    'Transaction Paid',
    userId,
    plan.plan_type,
    'Transaction was paid'
  )
}

export async function handleTransactionCanceled({
  transaction,
  plan,
  supabase,
}: HandlerPayload) {
  const { userId, id, email, status } = transaction
  if (!userId || !id) return

  await updateTransactionRecord(supabase, id, {
    status,
  })

  await notifyCustomer(
    email,
    'Transaction Canceled',
    'Your transaction has been canceled.'
  )
  await notifyAdmin(
    'Transaction Canceled',
    userId,
    plan.plan_type,
    'Transaction was canceled'
  )
}

export async function handleTransactionPastDue({
  transaction,
  plan,
  supabase,
}: HandlerPayload) {
  const { userId, id, email, status } = transaction
  if (!userId || !id) return

  await updateTransactionRecord(supabase, id, {
    status,
  })

  if (plan.plan_type !== 'lifetime') {
    await notifyCustomer(
      email,
      'Payment Past Due',
      'Your payment is past due. Please update your payment method.'
    )
  }

  await notifyAdmin(
    'Transaction Past Due',
    userId,
    plan.plan_type,
    'Transaction is past due'
  )
}

export async function handleTransactionPaymentFailed({
  transaction,
  plan,
  supabase,
}: HandlerPayload) {
  const { userId, id, email, status } = transaction
  if (!userId || !id) return

  await updateTransactionRecord(supabase, id, {
    status,
  })

  await notifyCustomer(
    email,
    'Payment Failed',
    'Your payment failed. Please try again.'
  )
  await notifyAdmin(
    'Transaction Payment Failed',
    userId,
    plan.plan_type,
    'Transaction payment failed'
  )
}
