import { SupabaseClient } from '@supabase/supabase-js'

import { sendAdminEmail, sendCustomerEmail } from '@/lib/email/emal-helpers'
import { updateUserProfile } from '@/lib/paddle/helpers/subscription-helpers'
import type { ParsedSubscription } from '@/lib/paddle/utils/parse-events'
import { Plan } from '@/types/custom'

type HandlerPayload = {
  subscription: ParsedSubscription
  plan: Plan
  supabase: SupabaseClient
}

async function notifyAdmin(
  eventType: string,
  userId: string,
  planType: string,
  message: string
) {
  await sendAdminEmail(
    eventType,
    `${message} for user: ${userId} with plan: ${planType}`
  )
}

async function notifyCustomer(
  email: string | null,
  eventType: string,
  message: string
) {
  if (email) {
    await sendCustomerEmail(email, eventType, message)
  }
}

export async function handleSubscriptionCreated({
  subscription,
  plan,
  supabase,
}: HandlerPayload) {
  const { userId, email } = subscription
  const hasTrialPeriod = subscription?.billingCycle?.interval === 'trial'

  if (hasTrialPeriod) {
    await updateUserProfile(supabase, userId!, true, plan?.plan_type)
    await notifyCustomer(
      email,
      'Subscription Created with Trial Period',
      'Your subscription has been created with a trial period. Enjoy your trial!'
    )
  }

  await notifyAdmin(
    'New Subscription Created',
    userId!,
    plan.plan_type,
    'A new subscription has been created'
  )
}

export async function handleSubscriptionActivated({
  subscription,
  plan,
  supabase,
}: HandlerPayload) {
  await updateUserProfile(supabase, subscription.userId!, true, plan.plan_type)
  await notifyCustomer(
    subscription.email,
    'Subscription Activated',
    'Your subscription has been activated. Enjoy!'
  )
  await notifyAdmin(
    'Subscription Activated',
    subscription.userId!,
    plan.plan_type,
    'Subscription has been activated'
  )
}

export async function handleSubscriptionUpdated({
  subscription,
  plan,
  supabase,
}: HandlerPayload) {
  const isPro = subscription.status === 'active'
  await updateUserProfile(supabase, subscription.userId!, isPro, plan.plan_type)
  await notifyAdmin(
    'Subscription Updated',
    subscription.userId!,
    plan.plan_type,
    'Subscription has been updated'
  )
}

export async function handleSubscriptionCanceled({
  subscription,
  plan,
  supabase,
}: HandlerPayload) {
  const now = new Date()
  const billingEnd = new Date(
    subscription.currentBillingPeriod?.endsAt ?? new Date()
  )

  if (now <= billingEnd) {
    await notifyCustomer(
      subscription.email,
      'Subscription Canceled',
      'Your subscription has been canceled. You will still have access until the end of your current billing period.'
    )
  } else {
    await updateUserProfile(
      supabase,
      subscription.userId!,
      false,
      plan.plan_type
    )
    await notifyCustomer(
      subscription.email,
      'Subscription Canceled',
      'Your subscription has been canceled and your access has been deactivated.'
    )
  }

  await notifyAdmin(
    'Subscription Canceled',
    subscription.userId!,
    plan.plan_type,
    'Subscription has been canceled'
  )
}

export async function handleSubscriptionPastDue({
  subscription,
  plan,
  supabase,
}: HandlerPayload) {
  await updateUserProfile(supabase, subscription.userId!, false, plan.plan_type)
  await notifyCustomer(
    subscription.email,
    'Subscription Past Due',
    'Your subscription payment is past due. Please update your payment information.'
  )
  await notifyAdmin(
    'Subscription Past Due',
    subscription.userId!,
    plan.plan_type,
    'Payment is past due for subscription'
  )
}

export async function handleSubscriptionPaused({
  subscription,
  plan,
  supabase,
}: HandlerPayload) {
  await updateUserProfile(supabase, subscription.userId!, false, plan.plan_type)
  await notifyCustomer(
    subscription.email,
    'Subscription Paused',
    'Your subscription has been paused. You can resume it at any time.'
  )
  await notifyAdmin(
    'Subscription Paused',
    subscription.userId!,
    plan.plan_type,
    'Subscription has been paused'
  )
}

export async function handleSubscriptionResumed({
  subscription,
  plan,
  supabase,
}: HandlerPayload) {
  await updateUserProfile(supabase, subscription.userId!, true, plan.plan_type)
  await notifyCustomer(
    subscription.email,
    'Subscription Resumed',
    'Your subscription has been resumed. Enjoy your access!'
  )
  await notifyAdmin(
    'Subscription Resumed',
    subscription.userId!,
    plan.plan_type,
    'Subscription has been resumed'
  )
}
