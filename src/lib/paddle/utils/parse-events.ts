export interface ParsedTransaction {
  eventId: string
  eventType: string
  occurredAt: string
  notificationId: string
  id: string
  userId: string | null
  email: string | null
  name: string | null
  status: string
  subscriptionId: string | null
  priceId: string
  totalAmount: string
  currency: string
  checkoutUrl: string | null
  paymentDetails: any | null
  invoiceNumber?: string

  totals: {
    fee: string
    tax: string
    total: string
    credit: string
    balance: string
    discount: string
    earnings: string
    subtotal: string
    grand_total: string
    currency_code: string
    credit_to_balance: string
  } | null
}

export interface ParsedSubscription {
  eventId: string
  eventType: string
  occurredAt: string
  notificationId: string
  id: string
  userId: string | null
  email: string | null
  name: string | null
  status: string
  priceId: string
  productId: string
  productName: string
  billingCycle: {
    interval: string
    frequency: number
  }
  nextBilledAt: string | null
  firstBilledAt: string | null
  currentBillingPeriod: {
    startsAt: string | null
    endsAt: string | null
  }
}

// Type guard (updated for camelCase)
function isValidEvent(event: any): boolean {
  return event?.data && Array.isArray(event.data.items)
}

// Transaction parser (camelCase)
export function parseTransactionEvent(event: any): ParsedTransaction {
  if (!isValidEvent(event)) throw new Error('Invalid transaction event')

  const data = event.data
  const item = data.items?.[0]
  const unitPrice = item?.price?.unitPrice || {}
  const userInfo = data.customData || {}
  const priceId = item?.price?.id || item?.priceId || ''

  return {
    eventId: event.eventId,
    eventType: event.eventType,
    occurredAt: event.occurredAt,
    notificationId: event.notificationId,
    id: data.id,
    userId: userInfo?.userId || data.customerId || null,
    email: userInfo?.email || null,
    name: userInfo?.name || null,
    status: data.status,
    subscriptionId: data.subscriptionId || null,
    priceId: priceId,
    totalAmount: data.details?.totals?.total || '0',
    currency:
      data.details?.totals?.currencyCode || unitPrice.currencyCode || 'USD',
    checkoutUrl: data.checkout?.url || null,
    invoiceNumber: data.invoiceNumber || null,
    paymentDetails: data?.payments?.[0] || null,
    totals: data.details?.totals || null,
  }
}

// Subscription parser (camelCase)
export function parseSubscriptionEvent(event: any): ParsedSubscription {
  if (!isValidEvent(event)) throw new Error('Invalid subscription event')

  const data = event.data
  const item = data.items?.[0]
  const billingCycle = item?.price?.billingCycle || data.billingCycle || {}
  const userInfo = data.customData || {}

  return {
    eventId: event.eventId,
    eventType: event.eventType,
    occurredAt: event.occurredAt,
    notificationId: event.notificationId,
    id: data.id,
    userId: userInfo?.userId || data.customerId || null,
    email: userInfo?.email || null,
    name: userInfo?.name || null,
    status: data.status,
    priceId: item?.price?.id || '',
    productId: item?.product?.id || '',
    productName: item?.product?.name || '',
    billingCycle: {
      interval: billingCycle.interval || 'month',
      frequency: billingCycle.frequency || 1,
    },
    nextBilledAt: data.nextBilledAt || null,
    firstBilledAt: data.firstBilledAt || null,
    currentBillingPeriod: {
      startsAt: data.currentBillingPeriod?.startsAt || null,
      endsAt: data.currentBillingPeriod?.endsAt || null,
    },
  }
}
