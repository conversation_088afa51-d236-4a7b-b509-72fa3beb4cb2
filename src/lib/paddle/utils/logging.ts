import { SupabaseClient } from '@supabase/supabase-js'

export interface WebhookEventLog {
  event_id?: string
  event_type?: string
  user_id?: string
  source?: string // e.g., 'paddle', 'stripe', 'github', etc.
  source_event_type?: string
  processed_at?: string
  data?: any
  error_message?: string
  status?: 'success' | 'error' | 'unhandled'
  metadata?: any
}

/**
 * Log webhook event processing for debugging and analytics
 */
export async function logWebhookEvent(
  event: any,
  options: {
    eventType?: string
    userId?: string
    source?: string
    sourceEventType?: string
    status?: 'success' | 'error' | 'unhandled'
    errorMessage?: string
    metadata?: any
  },
  supabase: SupabaseClient
): Promise<void> {
  try {
    const logData: WebhookEventLog = {
      event_id:
        event.eventId ||
        event.id ||
        event.event_id ||
        `${Date.now()}-${Math.random()}`,
      event_type: options.eventType || 'webhook',
      user_id: options.userId,
      source: options.source || 'unknown',
      source_event_type:
        options.sourceEventType ||
        event.eventType ||
        event.type ||
        event.event_type,
      processed_at: new Date().toISOString(),
      data: event.data || event,
      error_message: options.errorMessage,
      status: options.status || 'success',
      metadata: options.metadata,
    }

    const { error } = await supabase.from('webhook_events').insert(logData)

    if (error) {
      console.error('Error logging webhook event:', error)
    }
  } catch (error) {
    console.error('Error in logWebhookEvent:', error)
  }
}

/**
 * Log successful event processing
 */
export async function logEventSuccess(
  event: any,
  eventType: string,
  supabase: SupabaseClient,
  source: string = 'paddle',
  userId?: string,
  metadata?: any
): Promise<void> {
  await logWebhookEvent(
    event,
    {
      eventType,
      userId,
      source,
      status: 'success',
      metadata,
    },
    supabase
  )
}

/**
 * Log unhandled events
 */
export async function logUnhandledEvent(
  event: any,
  supabase: SupabaseClient,
  source: string = 'paddle'
): Promise<void> {
  await logWebhookEvent(
    event,
    {
      eventType: 'unhandled',
      source,
      status: 'unhandled',
      errorMessage: 'Unhandled event type',
    },
    supabase
  )
}

/**
 * Log event processing errors
 */
export async function logEventError(
  event: any,
  error: any,
  source: string = 'paddle',
  supabase: SupabaseClient
): Promise<void> {
  await logWebhookEvent(
    event,
    {
      eventType: 'error',
      source,
      status: 'error',
      errorMessage: error instanceof Error ? error.message : String(error),
    },
    supabase
  )
}

// Legacy function for backward compatibility
export async function logEventProcessing(
  event: any,
  eventType: string,
  userId: string | undefined,
  supabase: SupabaseClient
): Promise<void> {
  await logEventSuccess(event, eventType, supabase, 'paddle', userId)
}
