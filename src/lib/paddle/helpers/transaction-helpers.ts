import { SupabaseClient } from '@supabase/supabase-js'

import type { Transaction, TransactionInsert } from '@/types/custom'

export type AppTransactionStatus =
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'refunded'
  | 'cancelled'

const paddleToAppStatusMap: Record<string, AppTransactionStatus> = {
  draft: 'pending',
  ready: 'pending',
  billed: 'processing',
  paid: 'processing',
  completed: 'completed',
  canceled: 'cancelled',
  past_due: 'failed',
}

/**
 * Map incoming Paddle status to internal app status
 */
export function mapPaddleStatusToAppStatus(
  status: string
): AppTransactionStatus {
  const mapped = paddleToAppStatusMap[status]
  if (!mapped) {
    console.warn(`Unknown Paddle status "${status}", defaulting to 'pending'`)
    return 'pending'
  }
  return mapped
}

/**
 * Create or update a transaction record in the database
 */
export async function createTransactionRecord(
  supabase: SupabaseClient,
  data: TransactionInsert
) {
  const mappedStatus = mapPaddleStatusToAppStatus(data.status)

  const insertPayload = {
    event_id: data.event_id ?? null,
    user_id: data.user_id,
    subscription_id: data.subscription_id,
    amount: data.amount,
    currency: data.currency,
    status: mappedStatus,
    payment_method: 'paddle',
    payment_id: data.payment_id ?? null,
    invoice_url: data.invoice_url ?? null,
    coupon_id: data.coupon_id ?? null,
    metadata: data.metadata ?? null,
    amount_breakdown: data.amount_breakdown ?? null,
  }

  const { error: insertError, data: result } = await supabase
    .from('transactions')
    .insert(insertPayload)
    .select()

  console.log('transaction created:', result)

  // If insert fails due to RLS or conflict, fallback to update
  if (insertError?.code === '23505' || insertError?.code === '42501') {
    console.warn(
      `Insert failed due to conflict or RLS (code ${insertError.code}), updating instead`
    )

    const { error: updateError, data: result } = await supabase
      .from('transactions')
      .update(insertPayload)
      .eq('payment_id', data.payment_id ?? '')
      .select()

    console.log('transaction updated during creation:', result)

    if (updateError) {
      console.error(
        'Failed to update existing transaction record:',
        updateError
      )
      throw updateError
    }

    return
  }

  if (insertError) {
    console.error('Failed to create transaction record:', insertError)
    throw insertError
  }
}

/**
 * Update an existing transaction record by paymentId
 */
export async function updateTransactionRecord(
  supabase: SupabaseClient,
  paymentId: string,
  updates: Partial<Pick<Transaction, 'status'>>
) {
  // if (!paymentId) {
  //   console.warn('Missing paymentId while updating transaction')
  //   return
  // }

  const mappedStatus =
    updates.status && typeof updates.status === 'string'
      ? mapPaddleStatusToAppStatus(updates.status)
      : undefined

  const { error } = await supabase
    .from('transactions')
    .update({ ...(mappedStatus && { status: mappedStatus }) })
    .eq('payment_id', paymentId)

  if (error) {
    console.error('Failed to update transaction record:', error.message)
    throw error
  }
}
