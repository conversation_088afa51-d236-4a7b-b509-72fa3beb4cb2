import { SupabaseClient } from '@supabase/supabase-js'

import { sendEmail } from '@/lib/email/mailer'

// export type TransactionStatus =
//   | 'draft'
//   | 'ready'
//   | 'billed'
//   | 'paid'
//   | 'completed'
//   | 'canceled'
//   | 'past_due'

export async function getSubscriptionPlan(
  supabase: SupabaseClient,
  priceId: string
) {
  try {
    const { data: subscriptionPlan, error } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('paddle_price_id', priceId)
      .single()

    if (!subscriptionPlan) {
      console.warn(
        `No matching subscription plan found for Paddle price ID: ${priceId}`
      )
      return null
    }
    return subscriptionPlan
  } catch (error) {
    console.error('Error fetching subscription plan:', error)
    throw new Error('Failed to fetch subscription plan')
  }
}

export async function updateUserProfile(
  supabase: SupabaseClient,
  userId: string,
  isPro: boolean,
  planType: string
) {
  const subscriptionStartDate = new Date().toISOString()
  let subscriptionEndDate: string | null = null

  if (planType === 'lifetime') {
    subscriptionEndDate = null
  } else if (planType === 'pro-y') {
    subscriptionEndDate = new Date(
      Date.now() + 365 * 24 * 60 * 60 * 1000
    ).toISOString()
  } else if (planType === 'pro-m') {
    subscriptionEndDate = new Date(
      Date.now() + 30 * 24 * 60 * 60 * 1000
    ).toISOString()
  }

  const { error } = await supabase
    .from('profiles')
    .update({
      is_pro: isPro,
      is_lifetime_pro: planType === 'lifetime',
      is_lifetime_subscription: planType === 'lifetime',
      subscription_status: isPro ? 'active' : 'inactive',
      subscription_start_date: subscriptionStartDate,
      subscription_end_date: subscriptionEndDate,
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId)

  if (error) {
    console.error('Error updating user profile:', error)
    throw new Error('Failed to update user profile')
  }

  return userId
}

export async function sendSubscriptionEmail(
  email: string,
  subject: string,
  message: string
) {
  try {
    await sendEmail({
      to: email,
      subject: subject,
      html: `<p>${message}</p>`,
    })
  } catch (error) {
    console.error('Error sending subscription email:', error)
    throw new Error('Failed to send subscription email')
  }
}
