import { Paddle } from '@paddle/paddle-node-sdk'

// Initialize Paddle client
export const paddle = new Paddle(process.env.PADDLE_API_KEY!)

// Paddle environment configuration
export const PADDLE_ENVIRONMENT = process.env.PADDLE_ENVIRONMENT || 'sandbox'

// Paddle webhook secret
export const PADDLE_WEBHOOK_SECRET = process.env.PADDLE_WEBHOOK_SECRET_KEY!

// Helper function to create a checkout session
export async function createPaddleCheckout({
  priceId,
  customerId,
  customData,
  successUrl,
  discountId,
}: {
  priceId: string
  customerId?: string
  customData?: Record<string, any>
  successUrl?: string
  discountId?: string
}) {
  try {
    const checkoutPayload: any = {
      items: [
        {
          priceId: priceId,
          quantity: 1,
        },
      ],
      customData: customData,
      returnUrl:
        successUrl || `${process.env.NEXT_PUBLIC_BASE_URL}/payment-success`,
    }

    if (customerId) {
      checkoutPayload.customerId = customerId
    }

    if (discountId) {
      checkoutPayload.discountId = discountId
    }

    // Note: Use the correct Paddle method based on your SDK version
    // This might need to be adjusted based on the actual Paddle SDK methods
    const checkout = await paddle.transactions.create(checkoutPayload)
    return checkout
  } catch (error) {
    console.error('Error creating Paddle checkout:', error)
    throw error
  }
}

// Helper function to create a customer
export async function createPaddleCustomer({
  email,
  name,
  customData,
}: {
  email: string
  name?: string
  customData?: Record<string, any>
}) {
  try {
    const customer = await paddle.customers.create({
      email,
      name,
      customData,
    })
    return customer
  } catch (error) {
    console.error('Error creating Paddle customer:', error)
    throw error
  }
}

// Helper function to get subscription details
export async function getPaddleSubscription(subscriptionId: string) {
  try {
    const subscription = await paddle.subscriptions.get(subscriptionId)
    return subscription
  } catch (error) {
    console.error('Error fetching Paddle subscription:', error)
    throw error
  }
}

// Helper function to cancel subscription
export async function cancelPaddleSubscription(subscriptionId: string) {
  try {
    const subscription = await paddle.subscriptions.cancel(subscriptionId, {
      effectiveFrom: 'next_billing_period',
    })
    return subscription
  } catch (error) {
    console.error('Error canceling Paddle subscription:', error)
    throw error
  }
}

// Helper function to pause subscription
export async function pausePaddleSubscription(subscriptionId: string) {
  try {
    const subscription = await paddle.subscriptions.pause(subscriptionId, {
      effectiveFrom: 'next_billing_period',
    })
    return subscription
  } catch (error) {
    console.error('Error pausing Paddle subscription:', error)
    throw error
  }
}

// Helper function to resume subscription
export async function resumePaddleSubscription(subscriptionId: string) {
  try {
    const subscription = await paddle.subscriptions.resume(subscriptionId, {
      effectiveFrom: 'immediately',
    })
    return subscription
  } catch (error) {
    console.error('Error resuming Paddle subscription:', error)
    throw error
  }
}

// Helper function to get transaction details
export async function getPaddleTransaction(transactionId: string) {
  try {
    const transaction = await paddle.transactions.get(transactionId)
    return transaction
  } catch (error) {
    console.error('Error fetching Paddle transaction:', error)
    throw error
  }
}

// Helper function to create a one-time payment
export async function createPaddlePayment({
  priceId,
  customerId,
  customData,
  successUrl,
}: {
  priceId: string
  customerId?: string
  customData?: Record<string, any>
  successUrl?: string
}) {
  try {
    const transactionPayload: any = {
      items: [
        {
          priceId: priceId,
          quantity: 1,
        },
      ],
      customData: customData,
    }

    if (customerId) {
      transactionPayload.customerId = customerId
    }

    const transaction = await paddle.transactions.create(transactionPayload)
    return transaction
  } catch (error) {
    console.error('Error creating Paddle payment:', error)
    throw error
  }
}

// Helper function to validate webhook signature
export function validatePaddleWebhook(
  signature: string,
  body: string,
  secret: string
): boolean {
  try {
    // This is typically handled by the Paddle SDK's unmarshal function
    // This is just a utility for manual validation if needed
    return true
  } catch (error) {
    console.error('Error validating Paddle webhook:', error)
    return false
  }
}

// Helper to format Paddle amounts (Paddle uses the smallest currency unit)
export function formatPaddleAmount(amount: string, currency: string): number {
  const numAmount = parseInt(amount)

  // Most currencies use 2 decimal places (cents)
  if (['USD', 'EUR', 'GBP', 'CAD', 'AUD'].includes(currency.toUpperCase())) {
    return numAmount / 100
  }

  // Some currencies don't use decimal places
  if (['JPY', 'KRW'].includes(currency.toUpperCase())) {
    return numAmount
  }

  // Default to 2 decimal places
  return numAmount / 100
}

// Helper to convert to Paddle amount format
export function toPaddleAmount(amount: number, currency: string): string {
  // Most currencies use 2 decimal places (cents)
  if (['USD', 'EUR', 'GBP', 'CAD', 'AUD'].includes(currency.toUpperCase())) {
    return Math.round(amount * 100).toString()
  }

  // Some currencies don't use decimal places
  if (['JPY', 'KRW'].includes(currency.toUpperCase())) {
    return Math.round(amount).toString()
  }

  // Default to 2 decimal places
  return Math.round(amount * 100).toString()
}
