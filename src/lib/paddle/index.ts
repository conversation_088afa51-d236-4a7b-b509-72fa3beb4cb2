// Subscription Events
export {
  handleSubscriptionActivated,
  handleSubscriptionCanceled,
  handleSubscriptionCreated,
  handleSubscriptionPastDue,
  handleSubscriptionPaused,
  handleSubscriptionResumed,
  handleSubscriptionUpdated,
} from './events/subscription-events'

// Transaction Events (for separate transaction webhook)
export {
  handleTransactionCompleted,
  handleTransactionPaid,
  handleTransactionPaymentFailed,
} from './events/transaction-events'

// Utilities
export { logEventError, logUnhandledEvent } from './utils/logging'

// Types for supported events
export type SupportedPaddleEvent =
  | import('@paddle/paddle-node-sdk').SubscriptionCreatedEvent
  | import('@paddle/paddle-node-sdk').SubscriptionUpdatedEvent
  | import('@paddle/paddle-node-sdk').SubscriptionCanceledEvent
  | import('@paddle/paddle-node-sdk').SubscriptionPausedEvent
  | import('@paddle/paddle-node-sdk').SubscriptionResumedEvent
  | import('@paddle/paddle-node-sdk').SubscriptionActivatedEvent
  | import('@paddle/paddle-node-sdk').SubscriptionPastDueEvent
  | import('@paddle/paddle-node-sdk').TransactionCompletedEvent
  | import('@paddle/paddle-node-sdk').TransactionPaidEvent
  | import('@paddle/paddle-node-sdk').TransactionPaymentFailedEvent
