import { Role } from '@/actions/users'

/**
 * Check if a user has permission based on their role
 */
export function hasPermission(
  userRole: Role | null | undefined,
  allowedRoles: Role[]
): boolean {
  if (!userRole) return false
  return allowedRoles.includes(userRole)
}

/**
 * Check if a user has admin permission
 */
export function isAdmin(userRole: Role | null | undefined): boolean {
  return userRole === 'admin'
}

/**
 * Check if a user has editor permission
 */
export function isEditor(userRole: Role | null | undefined): boolean {
  return userRole === 'editor' || userRole === 'admin'
}

/**
 * Check if a user can delete items (admin only)
 */
export function canDelete(userRole: Role | null | undefined): boolean {
  return userRole === 'admin'
}

/**
 * Check if a user can edit items (admin and editor)
 */
export function canEdit(userRole: Role | null | undefined): boolean {
  return userRole === 'admin' || userRole === 'editor'
}

/**
 * Check if a user can create items (admin and editor)
 */
export function canCreate(userRole: Role | null | undefined): boolean {
  return userRole === 'admin' || userRole === 'editor'
}
