import { Database } from './supabase'

export type Component = Database['public']['Tables']['components']['Row']
export type Template = Database['public']['Tables']['templates']['Row']
export type Category = Database['public']['Tables']['categories']['Row']
export type Order = Database['public']['Tables']['orders']['Row']

export type Payment = Database['public']['Tables']['payments']['Row']
export type Profile = Database['public']['Tables']['profiles']['Row']
export type Plan = Database['public']['Tables']['subscription_plans']['Row']
export type ComponentWithCategory = Component & {
  category: {
    name: string
    slug: string
  }
}

export type Transaction = Database['public']['Tables']['transactions']['Row']
export type TransactionInsert =
  Database['public']['Tables']['transactions']['Insert']
export type TransactionUpdate =
  Database['public']['Tables']['transactions']['Update']

export type CompleteOrder = Order & {
  profiles: {
    id: string
    email: string
    first_name: string | null
    last_name: string | null
    username: string | null
    avatar_url: string | null
  }
  subscription_plans: {
    id: string
    name: string | null
    plan_type: string | null
    description: string | null
  }
  coupons: {
    id: string
    code: string | null
    discount_percentage: number | null
    discount_amount: number | null
  } | null
  payments: {
    id: string
    payment_status: string | null
    transaction_id: string | null
    payment_id: string | null
    order_id: string | null
    payer_email: string | null
    created_at: string | null
    updated_at: string | null
  } | null

  // Mapped fields for convenience
  user_email: string | null
  user_name: string
  user_id: string
  username: string | null
  user_avatar: string | null

  plan_name: string | null
  plan_type: string | null
  plan_description: string | null

  coupon_code: string | null
  discount_percentage: number | null
  discount_amount: number | null

  payment_status: string
  payment_method: string | null
  transaction_id: string
  created_at: string
}

// export type CompleTransaction = Transaction & {

// }
