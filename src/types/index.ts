import { Icons } from '@/components/icons'

export interface NavItem {
  title: string
  href: string
  slug?: string
  disabled?: boolean
  external?: boolean
  icon: keyof typeof Icons
  label?: string
  description?: string
  sub?: NavItem[]
  isAdmin?: boolean
}

export interface NavItemWithChildren extends NavItem {
  items: NavItemWithChildren[]
}

export interface NavItemWithOptionalChildren extends NavItem {
  items?: NavItemWithChildren[]
}

export interface FooterItem {
  title: string
  items: {
    title: string
    href: string
    external?: boolean
  }[]
}

export interface NavLink {
  title: string
  label?: string
  href: string
  // icon: JSX.Element
  icon: keyof typeof Icons
  isAdmin?: boolean
}

export interface SideLink extends NavLink {
  sub?: NavLink[]
}

export type MainNavItem = NavItemWithOptionalChildren

export type SidebarNavItem = NavItemWithChildren
