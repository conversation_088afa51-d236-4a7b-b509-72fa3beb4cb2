export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: '12.2.3 (519615d)'
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      categories: {
        Row: {
          created_at: string | null
          description: string | null
          icon: string | null
          id: string
          is_active: boolean | null
          is_component_only: boolean | null
          is_template_only: boolean | null
          label: Database['public']['Enums']['category_label']
          name: string | null
          preview_image: string | null
          sequence: number | null
          slug: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          is_component_only?: boolean | null
          is_template_only?: boolean | null
          label?: Database['public']['Enums']['category_label']
          name?: string | null
          preview_image?: string | null
          sequence?: number | null
          slug: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          is_component_only?: boolean | null
          is_template_only?: boolean | null
          label?: Database['public']['Enums']['category_label']
          name?: string | null
          preview_image?: string | null
          sequence?: number | null
          slug?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      component_versions: {
        Row: {
          code: string | null
          component_id: string | null
          created_at: string | null
          id: string
          is_pro: boolean | null
          updated_at: string | null
          version_name: string | null
        }
        Insert: {
          code?: string | null
          component_id?: string | null
          created_at?: string | null
          id?: string
          is_pro?: boolean | null
          updated_at?: string | null
          version_name?: string | null
        }
        Update: {
          code?: string | null
          component_id?: string | null
          created_at?: string | null
          id?: string
          is_pro?: boolean | null
          updated_at?: string | null
          version_name?: string | null
        }
        Relationships: [
          {
            foreignKeyName: 'component_versions_component_id_fkey'
            columns: ['component_id']
            isOneToOne: false
            referencedRelation: 'components'
            referencedColumns: ['id']
          },
        ]
      }
      components: {
        Row: {
          category_id: string | null
          code: string
          created_at: string | null
          description: string | null
          discount_id: string | null
          id: string
          image_url: string
          is_active: boolean
          is_pro: boolean
          likes: number | null
          meta_desc: string | null
          name: string
          slug: string | null
          tags: string | null
          updated_at: string | null
        }
        Insert: {
          category_id?: string | null
          code?: string
          created_at?: string | null
          description?: string | null
          discount_id?: string | null
          id?: string
          image_url?: string
          is_active?: boolean
          is_pro?: boolean
          likes?: number | null
          meta_desc?: string | null
          name?: string
          slug?: string | null
          tags?: string | null
          updated_at?: string | null
        }
        Update: {
          category_id?: string | null
          code?: string
          created_at?: string | null
          description?: string | null
          discount_id?: string | null
          id?: string
          image_url?: string
          is_active?: boolean
          is_pro?: boolean
          likes?: number | null
          meta_desc?: string | null
          name?: string
          slug?: string | null
          tags?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: 'components_category_id_fkey'
            columns: ['category_id']
            isOneToOne: false
            referencedRelation: 'categories'
            referencedColumns: ['id']
          },
          {
            foreignKeyName: 'public_components_discount_id_fkey'
            columns: ['discount_id']
            isOneToOne: false
            referencedRelation: 'coupons'
            referencedColumns: ['id']
          },
        ]
      }
      coupons: {
        Row: {
          code: string | null
          created_at: string | null
          description: string | null
          discount_amount: number | null
          discount_percentage: number | null
          id: string
          is_banner: boolean | null
          limit: number
          updated_at: string | null
          valid_from: string | null
          valid_to: string | null
        }
        Insert: {
          code?: string | null
          created_at?: string | null
          description?: string | null
          discount_amount?: number | null
          discount_percentage?: number | null
          id?: string
          is_banner?: boolean | null
          limit?: number
          updated_at?: string | null
          valid_from?: string | null
          valid_to?: string | null
        }
        Update: {
          code?: string | null
          created_at?: string | null
          description?: string | null
          discount_amount?: number | null
          discount_percentage?: number | null
          id?: string
          is_banner?: boolean | null
          limit?: number
          updated_at?: string | null
          valid_from?: string | null
          valid_to?: string | null
        }
        Relationships: []
      }
      orders: {
        Row: {
          amount: number
          coupon_id: string | null
          created_at: string | null
          currency: string
          id: string
          order_id: string | null
          payment_id: string | null
          payment_option: string | null
          status: string
          subscription_plan_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          amount: number
          coupon_id?: string | null
          created_at?: string | null
          currency: string
          id?: string
          order_id?: string | null
          payment_id?: string | null
          payment_option?: string | null
          status?: string
          subscription_plan_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          amount?: number
          coupon_id?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          order_id?: string | null
          payment_id?: string | null
          payment_option?: string | null
          status?: string
          subscription_plan_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: 'orders_coupon_id_fkey'
            columns: ['coupon_id']
            isOneToOne: false
            referencedRelation: 'coupons'
            referencedColumns: ['id']
          },
          {
            foreignKeyName: 'orders_payment_id_fkey'
            columns: ['payment_id']
            isOneToOne: false
            referencedRelation: 'payments'
            referencedColumns: ['id']
          },
          {
            foreignKeyName: 'orders_subscription_plan_id_fkey'
            columns: ['subscription_plan_id']
            isOneToOne: false
            referencedRelation: 'subscription_plans'
            referencedColumns: ['id']
          },
          {
            foreignKeyName: 'orders_user_id_fkey'
            columns: ['user_id']
            isOneToOne: false
            referencedRelation: 'profiles'
            referencedColumns: ['id']
          },
        ]
      }
      payments: {
        Row: {
          created_at: string | null
          id: string
          order_id: string | null
          payer_email: string | null
          payment_id: string | null
          payment_status: string | null
          transaction_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          order_id?: string | null
          payer_email?: string | null
          payment_id?: string | null
          payment_status?: string | null
          transaction_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          order_id?: string | null
          payer_email?: string | null
          payment_id?: string | null
          payment_status?: string | null
          transaction_id?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          current_subscription_plan_id: string | null
          email: string | null
          first_name: string | null
          id: string
          is_lifetime_pro: boolean | null
          is_lifetime_subscription: boolean | null
          is_pro: boolean | null
          last_name: string | null
          liked_components: string[] | null
          role: string
          submitted_components_count: number | null
          subscription_end_date: string | null
          subscription_start_date: string | null
          subscription_status: string | null
          updated_at: string | null
          username: string | null
          username_attempt_failed: boolean | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          current_subscription_plan_id?: string | null
          email?: string | null
          first_name?: string | null
          id: string
          is_lifetime_pro?: boolean | null
          is_lifetime_subscription?: boolean | null
          is_pro?: boolean | null
          last_name?: string | null
          liked_components?: string[] | null
          role?: string
          submitted_components_count?: number | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string | null
          updated_at?: string | null
          username?: string | null
          username_attempt_failed?: boolean | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          current_subscription_plan_id?: string | null
          email?: string | null
          first_name?: string | null
          id?: string
          is_lifetime_pro?: boolean | null
          is_lifetime_subscription?: boolean | null
          is_pro?: boolean | null
          last_name?: string | null
          liked_components?: string[] | null
          role?: string
          submitted_components_count?: number | null
          subscription_end_date?: string | null
          subscription_start_date?: string | null
          subscription_status?: string | null
          updated_at?: string | null
          username?: string | null
          username_attempt_failed?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: 'profiles_current_subscription_plan_id_fkey'
            columns: ['current_subscription_plan_id']
            isOneToOne: false
            referencedRelation: 'subscription_plans'
            referencedColumns: ['id']
          },
        ]
      }
      submissions: {
        Row: {
          component_code: string | null
          component_name: string | null
          created_at: string | null
          discount_code: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          component_code?: string | null
          component_name?: string | null
          created_at?: string | null
          discount_code?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          component_code?: string | null
          component_name?: string | null
          created_at?: string | null
          discount_code?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: []
      }
      subscription_plans: {
        Row: {
          actual_price: number | null
          currency: string
          description: string | null
          discounted_price: number | null
          duration_count: number | null
          duration_interval:
            | Database['public']['Enums']['pricing_plan_interval']
            | null
          id: string
          is_active: boolean
          metadata: Json | null
          name: string | null
          paddle_price_id: string | null
          plan_image: string | null
          plan_type: string
          pricing_type: Database['public']['Enums']['pricing_type']
          sequence: number
          unit_amount: number
        }
        Insert: {
          actual_price?: number | null
          currency?: string
          description?: string | null
          discounted_price?: number | null
          duration_count?: number | null
          duration_interval?:
            | Database['public']['Enums']['pricing_plan_interval']
            | null
          id?: string
          is_active?: boolean
          metadata?: Json | null
          name?: string | null
          paddle_price_id?: string | null
          plan_image?: string | null
          plan_type?: string | null
          pricing_type?: Database['public']['Enums']['pricing_type']
          sequence?: number
          unit_amount?: number
        }
        Update: {
          actual_price?: number | null
          currency?: string
          description?: string | null
          discounted_price?: number | null
          duration_count?: number | null
          duration_interval?:
            | Database['public']['Enums']['pricing_plan_interval']
            | null
          id?: string
          is_active?: boolean
          metadata?: Json | null
          name?: string | null
          paddle_price_id?: string | null
          plan_image?: string | null
          plan_type?: string | null
          pricing_type?: Database['public']['Enums']['pricing_type']
          sequence?: number
          unit_amount?: number
        }
        Relationships: []
      }
      templates: {
        Row: {
          category_id: string | null
          code: string
          created_at: string | null
          description: string | null
          discount_id: string | null
          id: string
          image_url: string
          is_active: boolean
          is_pro: boolean
          likes: number | null
          meta_desc: string | null
          name: string
          slug: string | null
          tags: string | null
          updated_at: string | null
        }
        Insert: {
          category_id?: string | null
          code?: string
          created_at?: string | null
          description?: string | null
          discount_id?: string | null
          id?: string
          image_url?: string
          is_active?: boolean
          is_pro?: boolean
          likes?: number | null
          meta_desc?: string | null
          name?: string
          slug?: string | null
          tags?: string | null
          updated_at?: string | null
        }
        Update: {
          category_id?: string | null
          code?: string
          created_at?: string | null
          description?: string | null
          discount_id?: string | null
          id?: string
          image_url?: string
          is_active?: boolean
          is_pro?: boolean
          likes?: number | null
          meta_desc?: string | null
          name?: string
          slug?: string | null
          tags?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: 'templates_category_id_fkey'
            columns: ['category_id']
            isOneToOne: false
            referencedRelation: 'categories'
            referencedColumns: ['id']
          },
        ]
      }
      transactions: {
        Row: {
          amount: number
          coupon_id: string | null
          created_at: string | null
          currency: string
          id: string
          invoice_url: string | null
          metadata: Json | null
          payment_id: string | null
          payment_method: string | null
          status: string
          subscription_id: string | null
          updated_at: string | null
          user_id: string
          amount_breakdown: Json | null
        }
        Insert: {
          amount: number
          coupon_id?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          invoice_url?: string | null
          metadata?: Json | null
          payment_id?: string | null
          payment_method?: string | null
          status: string
          subscription_id?: string | null
          updated_at?: string | null
          user_id: string
          amount_breakdown?: Json | null
          event_id?: string | null
          checkout_url?: string | null
        }
        Update: {
          amount?: number
          coupon_id?: string | null
          created_at?: string | null
          currency?: string
          id?: string
          invoice_url?: string | null
          metadata?: Json | null
          payment_id?: string | null
          payment_method?: string | null
          status?: string
          subscription_id?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: 'transactions_coupon_id_fkey'
            columns: ['coupon_id']
            isOneToOne: false
            referencedRelation: 'coupons'
            referencedColumns: ['id']
          },
          {
            foreignKeyName: 'transactions_subscription_id_fkey'
            columns: ['subscription_id']
            isOneToOne: false
            referencedRelation: 'subscription_plans'
            referencedColumns: ['id']
          },
          {
            foreignKeyName: 'transactions_user_id_fkey'
            columns: ['user_id']
            isOneToOne: false
            referencedRelation: 'profiles'
            referencedColumns: ['id']
          },
        ]
      }
      user_components: {
        Row: {
          component_id: string | null
          created_at: string | null
          id: string
          is_pro: boolean | null
          user_id: string | null
        }
        Insert: {
          component_id?: string | null
          created_at?: string | null
          id?: string
          is_pro?: boolean | null
          user_id?: string | null
        }
        Update: {
          component_id?: string | null
          created_at?: string | null
          id?: string
          is_pro?: boolean | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: 'user_components_component_id_fkey'
            columns: ['component_id']
            isOneToOne: false
            referencedRelation: 'components'
            referencedColumns: ['id']
          },
        ]
      }
      webhook_events: {
        Row: {
          created_at: string | null
          data: Json | null
          error_message: string | null
          event_id: string
          event_type: string | null
          metadata: Json | null
          processed_at: string | null
          source: string | null
          source_event_type: string | null
          status: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          data?: Json | null
          error_message?: string | null
          event_id: string
          event_type?: string | null
          metadata?: Json | null
          processed_at?: string | null
          source?: string | null
          source_event_type?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          data?: Json | null
          error_message?: string | null
          event_id?: string
          event_type?: string | null
          metadata?: Json | null
          processed_at?: string | null
          source?: string | null
          source_event_type?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      populate_transactions_table: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_profiles_subscription_end_date: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_profiles_with_subscription_info: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      category_label: 'new' | 'beta' | 'pro' | 'premium' | 'hot' | 'default'
      pricing_plan_interval: 'day' | 'week' | 'month' | 'year' | 'lifetime'
      pricing_type: 'one_time' | 'recurring'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: 'objects_bucketId_fkey'
            columns: ['bucket_id']
            isOneToOne: false
            referencedRelation: 'buckets'
            referencedColumns: ['id']
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: 's3_multipart_uploads_bucket_id_fkey'
            columns: ['bucket_id']
            isOneToOne: false
            referencedRelation: 'buckets'
            referencedColumns: ['id']
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: 's3_multipart_uploads_parts_bucket_id_fkey'
            columns: ['bucket_id']
            isOneToOne: false
            referencedRelation: 'buckets'
            referencedColumns: ['id']
          },
          {
            foreignKeyName: 's3_multipart_uploads_parts_upload_id_fkey'
            columns: ['upload_id']
            isOneToOne: false
            referencedRelation: 's3_multipart_uploads'
            referencedColumns: ['id']
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      can_insert_object: {
        Args: { bucketid: string; name: string; owner: string; metadata: Json }
        Returns: undefined
      }
      extension: {
        Args: { name: string }
        Returns: string
      }
      filename: {
        Args: { name: string }
        Returns: string
      }
      foldername: {
        Args: { name: string }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, '__InternalSupabase'>

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, 'public'>]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema['Tables'] & DefaultSchema['Views'])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Views'])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema['Tables'] &
        DefaultSchema['Views'])
    ? (DefaultSchema['Tables'] &
        DefaultSchema['Views'])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema['Tables']
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables']
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions['schema']]['Tables'][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema['Tables']
    ? DefaultSchema['Tables'][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema['Enums']
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums']
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions['schema']]['Enums'][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema['Enums']
    ? DefaultSchema['Enums'][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema['CompositeTypes']
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes']
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions['schema']]['CompositeTypes'][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema['CompositeTypes']
    ? DefaultSchema['CompositeTypes'][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      category_label: ['new', 'beta', 'pro', 'premium', 'hot', 'default'],
      pricing_plan_interval: ['day', 'week', 'month', 'year', 'lifetime'],
      pricing_type: ['one_time', 'recurring'],
    },
  },
  storage: {
    Enums: {},
  },
} as const
