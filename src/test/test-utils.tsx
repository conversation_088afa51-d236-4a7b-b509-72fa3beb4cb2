/* eslint-disable import/export */
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { RenderOptions, render as rtlRender } from '@testing-library/react'
import { ReactElement, ReactNode } from 'react'

// Create a new QueryClient instance for each test
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        refetchOnWindowFocus: false,
      },
    },
  })

interface WrapperProps {
  children: ReactNode
}

function Wrapper({ children }: WrapperProps) {
  const testQueryClient = createTestQueryClient()

  return (
    <QueryClientProvider client={testQueryClient}>
      {children}
    </QueryClientProvider>
  )
}

function render(ui: ReactElement, options?: Omit<RenderOptions, 'wrapper'>) {
  return rtlRender(ui, { wrapper: Wrapper, ...options })
}

// Re-export everything
export * from '@testing-library/react'

// Override render method
export { render }

// Export user-event
export { userEvent } from '@testing-library/user-event'
