import { type NextRequest } from 'next/server'

import { updateSession } from '@/utils/supabase/middleware'

export async function middleware(request: NextRequest) {
  // Add logging to debug
  console.log('Middleware running for path:', request.nextUrl.pathname)

  // Skip middleware for specific paths
  if (
    // request.nextUrl.pathname.includes('/_next') ||
    // request.nextUrl.pathname.includes('/api/') ||
    request.nextUrl.pathname.match(/\.(ico|png|jpg|jpeg|gif|svg)$/)
  ) {
    return
  }

  return await updateSession(request)
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
