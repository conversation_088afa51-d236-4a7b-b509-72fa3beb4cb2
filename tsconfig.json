{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "types": ["@testing-library/jest-dom"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.test.ts", "**/*.test.tsx", "types.d.ts", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out"]}